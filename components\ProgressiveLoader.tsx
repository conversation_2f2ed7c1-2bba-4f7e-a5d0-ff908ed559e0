import React from 'react';
import { LoadingStep, formatTimeRemaining, getLoadingIcon } from '../lib/loadingStates';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, Clock, Loader2 } from 'lucide-react';

interface ProgressiveLoaderProps {
  isLoading: boolean;
  currentStep: string | null;
  steps: LoadingStep[];
  progress: number;
  estimatedTimeRemaining?: number;
  className?: string;
  variant?: 'default' | 'compact' | 'minimal';
  showSteps?: boolean;
  showTimeRemaining?: boolean;
}

export const ProgressiveLoader: React.FC<ProgressiveLoaderProps> = ({
  isLoading,
  currentStep,
  steps,
  progress,
  estimatedTimeRemaining,
  className = '',
  variant = 'default',
  showSteps = true,
  showTimeRemaining = true,
}) => {
  const currentStepData = steps.find(step => step.id === currentStep);
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  if (!isLoading) return null;

  const renderMinimal = () => (
    <div className={`flex items-center gap-3 ${className}`}>
      <Loader2 className="h-5 w-5 animate-spin text-lime-600" />
      <div className="flex-1">
        <div className="text-sm font-medium text-gray-900">
          {currentStepData?.label || 'Processing...'}
        </div>
        {currentStepData?.description && (
          <div className="text-xs text-gray-600">
            {currentStepData.description}
          </div>
        )}
      </div>
      <div className="text-sm font-bold text-lime-600 bg-lime-100 px-2 py-1 rounded-full">
        {Math.round(progress)}%
      </div>
    </div>
  );

  const renderCompact = () => (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <div className="flex items-center gap-3 mb-3">
        <div className="w-8 h-8 bg-lime-100 rounded-full flex items-center justify-center">
          <Loader2 className="h-4 w-4 animate-spin text-lime-600" />
        </div>
        <div className="flex-1">
          <div className="font-medium text-gray-900">
            {currentStepData?.label || 'Processing...'}
          </div>
          {currentStepData?.description && (
            <div className="text-sm text-gray-600">
              {currentStepData.description}
            </div>
          )}
        </div>
        <div className="text-lg font-bold text-lime-600">
          {Math.round(progress)}%
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <motion.div
          className="h-2 rounded-full bg-gradient-to-r from-lime-400 to-lime-500"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
        />
      </div>

      {showTimeRemaining && estimatedTimeRemaining && estimatedTimeRemaining > 1000 && (
        <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
          <Clock className="h-3 w-3" />
          <span>About {formatTimeRemaining(estimatedTimeRemaining)} remaining</span>
        </div>
      )}
    </div>
  );

  const renderDefault = () => (
    <div className={`bg-white rounded-xl border-2 border-gray-200 p-6 ${className}`}>
      {/* Current Step Header */}
      <div className="flex items-center gap-4 mb-6">
        <div className="w-12 h-12 bg-lime-100 rounded-xl flex items-center justify-center">
          <motion.div
            key={currentStep}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Loader2 className="h-6 w-6 animate-spin text-lime-600" />
          </motion.div>
        </div>
        <div className="flex-1">
          <motion.h3
            key={currentStepData?.label}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="text-lg font-semibold text-gray-900"
          >
            {currentStepData?.label || 'Processing...'}
          </motion.h3>
          <AnimatePresence mode="wait">
            {currentStepData?.description && (
              <motion.p
                key={currentStepData.description}
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                transition={{ duration: 0.2 }}
                className="text-gray-600"
              >
                {currentStepData.description}
              </motion.p>
            )}
          </AnimatePresence>
        </div>
        <div className="text-2xl font-bold text-lime-600 bg-lime-100 px-3 py-2 rounded-lg">
          {Math.round(progress)}%
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
          <motion.div
            className="h-3 rounded-full bg-gradient-to-r from-lime-400 to-lime-500 shadow-sm"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: 'easeOut' }}
          />
        </div>
      </div>

      {/* Time Remaining */}
      {showTimeRemaining && estimatedTimeRemaining && estimatedTimeRemaining > 1000 && (
        <div className="flex items-center gap-2 mb-4 text-sm text-gray-600">
          <Clock className="h-4 w-4" />
          <span>Estimated time remaining: {formatTimeRemaining(estimatedTimeRemaining)}</span>
        </div>
      )}

      {/* Step Progress */}
      {showSteps && steps.length > 1 && (
        <div className="space-y-2">
          <div className="text-sm font-medium text-gray-700 mb-3">
            Step {currentStepIndex + 1} of {steps.length}
          </div>
          <div className="grid grid-cols-1 gap-2">
            {steps.map((step, index) => {
              const isCompleted = index < currentStepIndex;
              const isCurrent = step.id === currentStep;
              const isPending = index > currentStepIndex;

              return (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`flex items-center gap-3 p-2 rounded-lg transition-colors ${
                    isCurrent
                      ? 'bg-lime-50 border border-lime-200'
                      : isCompleted
                      ? 'bg-green-50'
                      : 'bg-gray-50'
                  }`}
                >
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    isCompleted
                      ? 'bg-green-500 text-white'
                      : isCurrent
                      ? 'bg-lime-500 text-white'
                      : 'bg-gray-300 text-gray-600'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : isCurrent ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <span className="text-xs font-bold">{index + 1}</span>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      isCurrent ? 'text-lime-900' : isCompleted ? 'text-green-900' : 'text-gray-600'
                    }`}>
                      {step.label}
                    </div>
                    {isCurrent && step.description && (
                      <div className="text-xs text-lime-700">
                        {step.description}
                      </div>
                    )}
                  </div>
                  {isCurrent && step.progress !== undefined && (
                    <div className="text-xs font-bold text-lime-600">
                      {Math.round(step.progress)}%
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>
      )}

      {/* Pulse Animation */}
      <div className="flex items-center justify-center mt-4">
        <div className="flex space-x-2">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-lime-400 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );

  switch (variant) {
    case 'minimal':
      return renderMinimal();
    case 'compact':
      return renderCompact();
    default:
      return renderDefault();
  }
};

export default ProgressiveLoader;