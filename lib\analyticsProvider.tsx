/**
 * Analytics Provider Component
 * Initializes and provides analytics context throughout the app
 */

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { initializeAnalytics, analytics } from './analytics';

interface AnalyticsContextType {
  analytics: typeof analytics;
  isInitialized: boolean;
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null);

interface AnalyticsProviderProps {
  children: ReactNode;
  config?: {
    enabled?: boolean;
    debug?: boolean;
    trackingId?: string;
    apiEndpoint?: string;
  };
}

export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({
  children,
  config = {}
}) => {
  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    // Initialize analytics with configuration
    const analyticsConfig: any = {
      enabled: config.enabled ?? process.env.NODE_ENV === 'production',
      debug: config.debug ?? process.env.NODE_ENV === 'development',
      ...config
    };
    
    const trackingId = config.trackingId || process.env.REACT_APP_ANALYTICS_ID;
    if (trackingId) analyticsConfig.trackingId = trackingId;
    
    const apiEndpoint = config.apiEndpoint || process.env.REACT_APP_ANALYTICS_ENDPOINT;
    if (apiEndpoint) analyticsConfig.apiEndpoint = apiEndpoint;

    initializeAnalytics(analyticsConfig);
    setIsInitialized(true);

    // Track initial app load
    analytics.track('page_view', {
      page: window.location.pathname,
      referrer: document.referrer,
      timestamp: Date.now()
    });

    // Set up global error tracking
    const handleError = (error: Error, context?: Record<string, any>) => {
      analytics.trackError(error, context, 'high');
    };

    // Set up performance tracking
    const trackPagePerformance = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          analytics.trackPerformance({
            name: 'page_load_complete',
            value: navigation.loadEventEnd - navigation.fetchStart,
            unit: 'ms',
            timestamp: Date.now(),
            context: {
              domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
              firstByte: navigation.responseStart - navigation.fetchStart
            }
          });
        }
      }
    };

    // Track performance when page is fully loaded
    if (document.readyState === 'complete') {
      trackPagePerformance();
    } else {
      window.addEventListener('load', trackPagePerformance);
    }

    // Track user engagement
    let engagementStartTime = Date.now();
    let isEngaged = true;

    const trackEngagement = () => {
      if (isEngaged) {
        const engagementTime = Date.now() - engagementStartTime;
        analytics.track('user_interaction', {
          action: 'engagement_session',
          duration: engagementTime,
          element: 'page'
        });
      }
    };

    // Track when user becomes inactive
    const handleVisibilityChange = () => {
      if (document.hidden) {
        trackEngagement();
        isEngaged = false;
      } else {
        engagementStartTime = Date.now();
        isEngaged = true;
      }
    };

    // Track before page unload
    const handleBeforeUnload = () => {
      trackEngagement();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('load', trackPagePerformance);
    };
  }, [config]);

  const contextValue: AnalyticsContextType = {
    analytics,
    isInitialized
  };

  return (
    <AnalyticsContext.Provider value={contextValue}>
      {children}
    </AnalyticsContext.Provider>
  );
};

// Hook to use analytics
export const useAnalytics = (): AnalyticsContextType => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};

// HOC for tracking component renders
export const withAnalytics = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) => {
  const WrappedComponent = (props: P) => {
    const { analytics } = useAnalytics();

    useEffect(() => {
      analytics.track('user_interaction', {
        action: 'component_render',
        element: componentName,
        timestamp: Date.now()
      });
    }, [analytics]);

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withAnalytics(${componentName})`;
  return WrappedComponent;
};

// Hook for tracking user interactions
export const useTrackInteraction = () => {
  const { analytics } = useAnalytics();

  return {
    trackClick: (element: string, context?: Record<string, any>) => {
      analytics.trackUserInteraction('click', element, context);
    },
    trackHover: (element: string, context?: Record<string, any>) => {
      analytics.trackUserInteraction('hover', element, context);
    },
    trackFocus: (element: string, context?: Record<string, any>) => {
      analytics.trackUserInteraction('focus', element, context);
    },
    trackScroll: (element: string, context?: Record<string, any>) => {
      analytics.trackUserInteraction('scroll', element, context);
    },
    trackCustom: (action: string, element: string, context?: Record<string, any>) => {
      analytics.trackUserInteraction(action, element, context);
    }
  };
};

// Hook for tracking feature usage
export const useTrackFeature = () => {
  const { analytics } = useAnalytics();

  return {
    trackFeatureUsage: (feature: string, context?: Record<string, any>) => {
      analytics.trackFeatureUsage(feature, context);
    },
    trackFileUpload: (fileName: string, fileSize: number, fileType: string) => {
      analytics.trackFileUpload(fileName, fileSize, fileType);
    },
    trackFileAnalysis: (fileName: string, duration: number, issuesFound: number) => {
      analytics.trackFileAnalysis(fileName, duration, issuesFound);
    },
    trackFileRepair: (fileName: string, duration: number, issuesRepaired: number, success: boolean) => {
      analytics.trackFileRepair(fileName, duration, issuesRepaired, success);
    }
  };
};

export default AnalyticsProvider;