/**
 * Progressive Loading States Management
 * Provides a comprehensive system for managing loading states with smooth transitions
 */

import React from 'react';

export interface LoadingStep {
  id: string;
  label: string;
  description?: string;
  duration?: number; // Expected duration in ms
  icon?: string;
  progress?: number; // 0-100
}

export interface LoadingState {
  isLoading: boolean;
  currentStep: string | null;
  steps: LoadingStep[];
  progress: number; // Overall progress 0-100
  startTime: number;
  estimatedTimeRemaining?: number;
}

export class ProgressiveLoader {
  private state: LoadingState;
  private listeners: ((state: LoadingState) => void)[] = [];
  private stepTimeouts: NodeJS.Timeout[] = [];
  private progressInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.state = {
      isLoading: false,
      currentStep: null,
      steps: [],
      progress: 0,
      startTime: 0,
    };
  }

  // Subscribe to state changes
  subscribe(listener: (state: LoadingState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // Notify all listeners of state changes
  private notify() {
    this.listeners.forEach(listener => listener({ ...this.state }));
  }

  // Start loading with predefined steps
  start(steps: LoadingStep[]) {
    this.cleanup();

    this.state = {
      isLoading: true,
      currentStep: steps[0]?.id || null,
      steps: steps.map(step => ({ ...step, progress: 0 })),
      progress: 0,
      startTime: Date.now(),
    };

    this.notify();
    this.startProgressSimulation();
  }

  // Move to next step
  nextStep(stepId?: string) {
    if (!this.state.isLoading) return;

    const currentIndex = this.state.steps.findIndex(s => s.id === this.state.currentStep);

    if (stepId) {
      // Jump to specific step
      const targetIndex = this.state.steps.findIndex(s => s.id === stepId);
      if (targetIndex !== -1) {
        this.state.currentStep = stepId;
        this.state.progress = Math.round((targetIndex / this.state.steps.length) * 100);
      }
    } else if (currentIndex < this.state.steps.length - 1) {
      // Move to next step
      this.state.currentStep = this.state.steps[currentIndex + 1].id;
      this.state.progress = Math.round(((currentIndex + 1) / this.state.steps.length) * 100);
    }

    this.notify();
  }

  // Update current step progress
  updateStepProgress(progress: number, description?: string) {
    if (!this.state.isLoading || !this.state.currentStep) return;

    const stepIndex = this.state.steps.findIndex(s => s.id === this.state.currentStep);
    if (stepIndex !== -1) {
      this.state.steps[stepIndex].progress = Math.max(0, Math.min(100, progress));
      if (description) {
        this.state.steps[stepIndex].description = description;
      }
    }

    this.notify();
  }

  // Set overall progress directly
  setProgress(progress: number, stepId?: string) {
    if (!this.state.isLoading) return;

    this.state.progress = Math.max(0, Math.min(100, progress));

    if (stepId) {
      this.state.currentStep = stepId;
    }

    this.notify();
  }

  // Complete loading
  complete() {
    this.state.isLoading = false;
    this.state.progress = 100;
    this.state.currentStep = null;

    this.cleanup();
    this.notify();
  }

  // Cancel loading
  cancel() {
    this.state.isLoading = false;
    this.state.currentStep = null;
    this.state.progress = 0;

    this.cleanup();
    this.notify();
  }

  // Get current state
  getState(): LoadingState {
    return { ...this.state };
  }

  // Cleanup timers and intervals
  private cleanup() {
    this.stepTimeouts.forEach(timeout => clearTimeout(timeout));
    this.stepTimeouts = [];

    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
  }

  // Simulate smooth progress between steps
  private startProgressSimulation() {
    if (this.progressInterval) return;

    this.progressInterval = setInterval(() => {
      if (!this.state.isLoading) {
        this.cleanup();
        return;
      }

      const currentStepIndex = this.state.steps.findIndex(s => s.id === this.state.currentStep);
      if (currentStepIndex === -1) return;

      const currentStep = this.state.steps[currentStepIndex];
      const baseProgress = (currentStepIndex / this.state.steps.length) * 100;
      const stepProgress = (currentStep.progress || 0) / 100;
      const stepContribution = (1 / this.state.steps.length) * 100;

      this.state.progress = Math.min(99, baseProgress + (stepProgress * stepContribution));

      // Calculate estimated time remaining
      const elapsed = Date.now() - this.state.startTime;
      if (this.state.progress > 5) {
        const estimatedTotal = (elapsed / this.state.progress) * 100;
        this.state.estimatedTimeRemaining = Math.max(0, estimatedTotal - elapsed);
      }

      this.notify();
    }, 100);
  }
}

// Predefined loading sequences for common operations
export const LoadingSequences = {
  fileUpload: [
    {
      id: 'upload',
      label: 'Uploading File',
      description: 'Securely uploading your file...',
      duration: 2000,
      icon: '📤'
    },
    {
      id: 'validate',
      label: 'Validating File',
      description: 'Checking file format and integrity...',
      duration: 1000,
      icon: '🔍'
    },
    {
      id: 'analyze',
      label: 'Analyzing Content',
      description: 'Scanning for issues and errors...',
      duration: 3000,
      icon: '🔬'
    },
    {
      id: 'process',
      label: 'Processing Results',
      description: 'Preparing your analysis report...',
      duration: 1500,
      icon: '📋'
    }
  ],

  fileRepair: [
    {
      id: 'prepare',
      label: 'Preparing Repair',
      description: 'Setting up repair environment...',
      duration: 1000,
      icon: '⚙️'
    },
    {
      id: 'backup',
      label: 'Creating Backup',
      description: 'Safely backing up your original file...',
      duration: 800,
      icon: '💾'
    },
    {
      id: 'repair',
      label: 'Repairing File',
      description: 'Fixing detected issues...',
      duration: 4000,
      icon: '🔧'
    },
    {
      id: 'validate',
      label: 'Validating Repair',
      description: 'Ensuring repair was successful...',
      duration: 1200,
      icon: '✅'
    },
    {
      id: 'finalize',
      label: 'Finalizing',
      description: 'Preparing your repaired file...',
      duration: 800,
      icon: '🎉'
    }
  ],

  pageLoad: [
    {
      id: 'init',
      label: 'Initializing',
      description: 'Loading application...',
      duration: 500,
      icon: '🚀'
    },
    {
      id: 'components',
      label: 'Loading Components',
      description: 'Setting up interface...',
      duration: 800,
      icon: '🧩'
    },
    {
      id: 'ready',
      label: 'Ready',
      description: 'Application ready to use',
      duration: 200,
      icon: '✨'
    }
  ]
};

// Hook for using progressive loader in React components
export function useProgressiveLoader() {
  const [loader] = React.useState(() => new ProgressiveLoader());
  const [state, setState] = React.useState<LoadingState>(loader.getState());

  React.useEffect(() => {
    const unsubscribe = loader.subscribe(setState);
    return unsubscribe;
  }, [loader]);

  return {
    ...state,
    start: (steps: LoadingStep[]) => loader.start(steps),
    nextStep: (stepId?: string) => loader.nextStep(stepId),
    updateStepProgress: (progress: number, description?: string) =>
      loader.updateStepProgress(progress, description),
    setProgress: (progress: number, stepId?: string) =>
      loader.setProgress(progress, stepId),
    complete: () => loader.complete(),
    cancel: () => loader.cancel(),
  };
}

// Utility functions
export const formatTimeRemaining = (ms: number): string => {
  if (ms < 1000) return 'Less than a second';
  if (ms < 60000) return `${Math.round(ms / 1000)} seconds`;
  return `${Math.round(ms / 60000)} minutes`;
};

export const getLoadingIcon = (step: LoadingStep): string => {
  return step.icon || '⏳';
};