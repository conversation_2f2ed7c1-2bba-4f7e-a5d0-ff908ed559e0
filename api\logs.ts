import { VercelRequest, VercelResponse } from '@vercel/node';
import { rateLimitMiddleware, RATE_LIMITS } from '../lib/rateLimiter';
import { validateOrigin } from '../lib/csrf';

// Increase the max body size for log submissions
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};

interface LogEntry {
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  context: string;
  message: string;
  data?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  error?: any;
  stack?: string;
}

export default async function handler(
  req: VercelRequest,
  res: VercelResponse
) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // SECURITY: Rate limiting for logging endpoint
  const rateLimitCheck = rateLimitMiddleware('logs', {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // Allow more requests for logging
    message: 'Too many log submissions'
  });

  if (!rateLimitCheck(req, res)) {
    return; // Rate limit exceeded, response already sent
  }

  // SECURITY: Origin validation
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'https://sheethealer.com',
    'https://www.sheethealer.com'
  ];

  if (!validateOrigin(req, allowedOrigins)) {
    return res.status(403).json({
      success: false,
      error: 'Invalid origin - request not allowed from this domain'
    });
  }

  try {
    const logEntry: LogEntry = req.body;

    // Basic validation
    if (!logEntry.timestamp || !logEntry.level || !logEntry.context || !logEntry.message) {
      return res.status(400).json({
        success: false,
        error: 'Missing required log fields'
      });
    }

    // Validate log level
    const validLevels = ['debug', 'info', 'warn', 'error', 'fatal'];
    if (!validLevels.includes(logEntry.level)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid log level'
      });
    }

    // In a real application, you would:
    // 1. Store logs in a database (e.g., MongoDB, PostgreSQL)
    // 2. Send to a logging service (e.g., Datadog, LogRocket, Sentry)
    // 3. Process logs for analytics and monitoring

    // For now, we'll just log to console in production
    const logPrefix = `[REMOTE LOG] [${logEntry.level.toUpperCase()}] [${logEntry.context}]`;
    const timestamp = new Date(logEntry.timestamp).toISOString();

    if (logEntry.level === 'error' || logEntry.level === 'fatal') {
      console.error(`${logPrefix} ${timestamp} - ${logEntry.message}`, {
        data: logEntry.data,
        error: logEntry.error,
        stack: logEntry.stack,
        userId: logEntry.userId,
        sessionId: logEntry.sessionId
      });
    } else if (logEntry.level === 'warn') {
      console.warn(`${logPrefix} ${timestamp} - ${logEntry.message}`, {
        data: logEntry.data,
        userId: logEntry.userId,
        sessionId: logEntry.sessionId
      });
    } else {
      console.log(`${logPrefix} ${timestamp} - ${logEntry.message}`, {
        data: logEntry.data,
        userId: logEntry.userId,
        sessionId: logEntry.sessionId
      });
    }

    // TODO: In production, implement proper log storage
    // Examples:
    // - await saveToDatabase(logEntry);
    // - await sendToDatadog(logEntry);
    // - await sendToSentry(logEntry);

    return res.status(200).json({
      success: true,
      message: 'Log entry received'
    });

  } catch (error) {
    console.error('Logging API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to process log entry'
    });
  }
}