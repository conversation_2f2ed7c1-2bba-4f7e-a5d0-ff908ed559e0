import React, { useEffect, Suspense } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import ConsentBanner from '../components/ConsentBanner';
import ReactGA from 'react-ga4';
import Cookies from 'js-cookie';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import { ReactPlugin } from '@stagewise-plugins/react';
import Navbar from '../components/Navbar';
import { registerServiceWorker, addNetworkListeners } from '../lib/serviceWorkerManager';
import { queryClient } from '../lib/queryClient';

// Lazy load page components for code splitting
const HomePage = React.lazy(() => import('../pages/HomePage'));
const PrivacyPolicyPage = React.lazy(() => import('../pages/PrivacyPolicyPage'));
const TermsOfServicePage = React.lazy(() => import('../pages/TermsOfServicePage'));
const ExcelRepairPage = React.lazy(() => import('../pages/ExcelRepairPage').then(module => ({ default: module.ExcelRepairPage })));

const GA_MEASUREMENT_ID = 'G-LJ1XHVGTSW';

// Loading component for Suspense fallback
const PageLoader: React.FC = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      <p className="text-gray-600 font-medium">Loading...</p>
    </div>
  </div>
);

const App: React.FC = () => {
  useEffect(() => {
    // Check if cookie consent is already given
    const consent = Cookies.get('sheethealer-cookie-consent');
    if (consent === 'true') {
      ReactGA.initialize(GA_MEASUREMENT_ID);
    }

    // Register service worker for offline support
    if (process.env.NODE_ENV === 'production') {
      registerServiceWorker('/sw.js');

      // Add network status listeners
      addNetworkListeners(
        () => {
          // Online callback
          console.log('🌐 Connection restored');
        },
        () => {
          // Offline callback
          console.log('📱 Working offline');
        }
      );
    }
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <main className="flex-grow">
            <Suspense fallback={<PageLoader />}>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/repair" element={<ExcelRepairPage />} />
                <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
                <Route path="/terms-of-service" element={<TermsOfServicePage />} />
              </Routes>
            </Suspense>
          </main>
          <ConsentBanner />
        </div>
        {/* React Query Devtools - only in development */}
        {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;
