
import React from 'react';
import Modal from './Modal';
import { CheckCir<PERSON>, Crown, Shield, Star } from 'lucide-react';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({ isOpen, onClose }) => (
  <Modal isOpen={isOpen} onClose={onClose}>
    <div className="text-center">
      <div className="w-20 h-20 bg-gradient-to-br from-lime-400 to-lime-600 rounded-2xl flex items-center justify-center mx-auto mb-8">
        <Crown className="h-10 w-10 text-white" />
      </div>
      
      <h2 className="text-3xl font-bold text-white mb-4">Upgrade to Fix Your Files</h2>
      <p className="text-gray-300 mb-10 text-lg">
        We&apos;ve detected issues that require our Pro repair engine to fix.
      </p>

      <div className="space-y-6 mb-10">
        {/* One-Time Plan */}
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-colors">
          <h3 className="text-xl font-bold text-white mb-4">One-Time Fix</h3>
          <div className="text-3xl font-bold text-white mb-1">$9</div>
          <div className="text-gray-400 mb-6">Single file repair</div>
          
          <ul className="text-left space-y-3 mb-8">
            <li className="flex items-center gap-3 text-gray-300">
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-3 w-3 text-white" />
              </div>
              Fix this single file
            </li>
            <li className="flex items-center gap-3 text-gray-300">
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-3 w-3 text-white" />
              </div>
              Detailed repair report
            </li>
            <li className="flex items-center gap-3 text-gray-300">
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-3 w-3 text-white" />
              </div>
              Before/after comparison
            </li>
          </ul>
          
          <button className="w-full bg-white text-gray-900 py-3 px-6 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Fix This File - $9
          </button>
        </div>

        {/* Unlimited Plan */}
        <div className="bg-gradient-to-br from-lime-600 to-lime-700 rounded-xl p-6 relative border-2 border-lime-400">
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span className="bg-lime-400 text-gray-900 text-sm px-4 py-1 rounded-full font-bold">POPULAR</span>
          </div>
          
          <div className="flex items-center justify-center gap-2 mb-4">
            <Star className="h-6 w-6 text-white" />
            <h3 className="text-xl font-bold text-white">Unlimited Monthly</h3>
          </div>
          
          <div className="text-3xl font-bold text-white mb-1">$19</div>
          <div className="text-lime-100 mb-6">per month</div>
          
          <ul className="text-left space-y-3 mb-8">
            <li className="flex items-center gap-3 text-white">
              <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-3 w-3 text-lime-600" />
              </div>
              Unlimited file repairs
            </li>
            <li className="flex items-center gap-3 text-white">
              <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-3 w-3 text-lime-600" />
              </div>
              Priority processing
            </li>
            <li className="flex items-center gap-3 text-white">
              <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-3 w-3 text-lime-600" />
              </div>
              Advanced repair features
            </li>
            <li className="flex items-center gap-3 text-white">
              <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-3 w-3 text-lime-600" />
              </div>
              30-day file history
            </li>
          </ul>
          
          <button className="w-full bg-white text-lime-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Go Unlimited - $19/mo
          </button>
        </div>
      </div>

      <div className="flex items-center justify-center gap-2 text-gray-400 text-sm">
        <Shield className="h-4 w-4" />
        <span>Secure payment • Cancel anytime</span>
      </div>
    </div>
  </Modal>
);

export default UpgradeModal;
