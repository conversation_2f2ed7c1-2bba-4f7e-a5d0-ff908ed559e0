<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fix broken Excel Files in seconds, not hours | SheetHealer</title>
  <meta name="description" content="Don't let corrupt Excel files slow you down. Upload your damaged workbook and let <PERSON><PERSON><PERSON><PERSON><PERSON> do the magic. Recover your data with 99.9% accuracy.">
  
  <!-- SEO Meta Tags -->
  <meta name="keywords" content="excel repair, spreadsheet recovery, corrupted excel files, excel file recovery, damaged workbook repair, excel error fix">
  <meta name="author" content="SheetHealer">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="https://sheethealer.com">
  <link rel="icon" href="/favicon.webp" type="image/webp" />
  
  <!-- Preconnect to Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Preload the font stylesheet -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"></noscript>
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://sheethealer.com">
  <meta property="og:title" content="Fix broken Excel Files in seconds, not hours | SheetHealer">
  <meta property="og:description" content="Don't let corrupt Excel files slow you down. Upload your damaged workbook and let SheetHealer do the magic. Recover your data with 99.9% accuracy.">
  <meta property="og:image" content="https://sheethealer.com/heroimage.webp">
  <meta property="og:image:alt" content="SheetHealer Excel File Repair Service">
  <meta property="og:site_name" content="SheetHealer">
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://sheethealer.com">
  <meta property="twitter:title" content="Fix broken Excel Files in seconds, not hours | SheetHealer">
  <meta property="twitter:description" content="Don't let corrupt Excel files slow you down. Upload your damaged workbook and let SheetHealer do the magic. Recover your data with 99.9% accuracy.">
  <meta property="twitter:image" content="https://sheethealer.com/heroimage.webp">
  <meta property="twitter:image:alt" content="SheetHealer Excel File Repair Service">
  
  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "SheetHealer",
    "description": "The fastest way to repair corrupted Excel files. Upload your damaged workbook and let SheetHealer do the magic. Recover your data with 99.9% accuracy.",
    "url": "https://sheethealer.com",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "description": "Excel file recovery service",
      "category": "Data Recovery"
    },
    "provider": {
      "@type": "Organization",
      "name": "SheetHealer",
      "url": "https://sheethealer.com"
    },
    "featureList": [
      "Excel file repair",
      "Spreadsheet recovery",
      "Data restoration",
      "Corrupted file fixing"
    ]
  }
  </script>
  
  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-LJ1XHVGTSW" defer></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    // Note: Do not initialize GA here - consent mode will handle this
  </script>
  
  <style>
    body {
      font-family: 'Segoe UI', Roboto, system-ui, sans-serif;
    }
  </style>
  <link rel="stylesheet" href="/index.css">
</head>
<body class="bg-white text-gray-800 font-sans">
  <div id="root"></div>
<script type="module" src="/index.tsx"></script>
</body>
</html>