import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'outline' | 'secondary';
  fullWidth?: boolean;
}

const base = 'px-5 py-2 rounded-full transition font-medium focus:outline-none';
const variants: Record<Required<ButtonProps>['variant'], string> = {
  primary: 'bg-green-600 hover:bg-white text-white hover:text-gray-900 shadow-sm border border-green-600',
  outline: 'border border-green-600 text-green-600 hover:bg-lime-300 hover:text-gray-900',
  secondary: 'bg-gray-100 hover:bg-lime-300 text-gray-800 hover:text-gray-900',
};

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  fullWidth = false,
  className = '',
  children,
  ...props
}) => {
  const classes = [
    base,
    variants[variant],
    fullWidth ? 'w-full' : '',
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <button {...props} className={classes}>
      {children}
    </button>
  );
};

export default Button; 