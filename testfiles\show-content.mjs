import XLSX from 'xlsx';

function showExcelContent(filePath) {
  try {
    console.log(`\n📊 Content of: ${filePath.split('/').pop()}`);
    console.log('=' .repeat(50));
    
    const workbook = XLSX.readFile(filePath);
    
    workbook.SheetNames.forEach((sheetName, index) => {
      console.log(`\n📄 Sheet ${index + 1}: "${sheetName}"`);
      console.log('-' .repeat(30));
      
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, 
        defval: '',
        blankrows: false 
      });
      
      if (data.length === 0) {
        console.log('   (Empty sheet)');
      } else {
        data.forEach((row, rowIndex) => {
          if (row.some(cell => cell !== '')) { // Only show non-empty rows
            console.log(`   Row ${rowIndex + 1}: [${row.map(cell => 
              typeof cell === 'string' ? `"${cell}"` : cell
            ).join(', ')}]`);
          }
        });
      }
    });
    
  } catch (error) {
    console.log(`❌ Error reading file: ${error.message}`);
  }
}

// Get filename from command line or use default
const fileName = process.argv[2] || 'testfiles/test30_repaired-selfuploaded.xlsx';
showExcelContent(fileName); 