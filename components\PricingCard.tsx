import React from 'react';
import { PricingPlan } from '../types';
import Icon from './Icon';

interface PricingCardProps {
  plan: PricingPlan;
  billingCycle: 'monthly' | 'yearly';
}

const PricingCard: React.FC<PricingCardProps> = ({ plan, billingCycle }) => {
  const handleGetStarted = () => {
    const signupSection = document.querySelector('#hero-signup');
    if (signupSection) {
      signupSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
      // Fallback: scroll to top if hero signup not found
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const displayPrice = billingCycle === 'yearly' && plan.yearlyPrice ? plan.yearlyPrice : plan.price;
  const displayPriceSuffix = billingCycle === 'yearly' && plan.yearlyPriceSuffix ? plan.yearlyPriceSuffix : plan.priceSuffix;

  return (
    <div className={`relative bg-white rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl h-full flex flex-col ${
      plan.isPopular ? 'border-green-500' : 'border-gray-200'
    }`}>
      {plan.isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
          <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap">
            Most Popular
          </span>
        </div>
      )}
      
      <div className="p-4 sm:p-6 lg:p-8 flex flex-col h-full">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-3">{plan.name}</h3>
          <div className="mb-2">
            <span className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900">
        {displayPrice}
            </span>
            <span className="text-gray-600 text-sm sm:text-base ml-1">
              {displayPriceSuffix}
            </span>
          </div>
        </div>

        {/* Features - grows to fill space */}
        <div className="flex-grow">
          <ul className="space-y-3 sm:space-y-4">
        {plan.features.map((feature, index) => (
              <li key={index} className="flex items-start text-left">
                <Icon iconClass="fas fa-check text-green-500 mr-2 sm:mr-3 mt-1 flex-shrink-0 text-sm" />
                <span className="text-gray-700 text-sm sm:text-base leading-relaxed">{feature}</span>
          </li>
        ))}
      </ul>
        </div>

        {/* Button - always at bottom */}
        <div className="mt-6 sm:mt-8">
        <button 
            onClick={handleGetStarted}
            className={`w-full py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-medium text-sm sm:text-base transition-all duration-200 ${
              plan.isPopular 
                ? 'bg-green-500 hover:bg-white text-white hover:text-gray-900 shadow-lg hover:shadow-xl border border-green-500' 
                : 'border border-gray-300 text-gray-700 hover:bg-lime-300 hover:text-gray-900 hover:border-lime-300'
            }`}
        >
          {plan.buttonText}
        </button>
        </div>
      </div>
    </div>
  );
};

export default PricingCard;
