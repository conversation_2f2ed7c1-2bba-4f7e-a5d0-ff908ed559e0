import React, { Component, ErrorInfo, ReactNode } from 'react';
import { log } from '../../lib/logger';

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error | undefined;
  errorInfo?: ErrorInfo | undefined;
  errorId?: string;
  retryCount: number;
}

export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  isolate?: boolean;
  maxRetries?: number;
  context?: string;
}

export class BaseErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      retryCount: 0
    };
  }

  public static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, context = 'unknown' } = this.props;
    const errorId = this.state.errorId || 'unknown';

    log.error('ui', `Error caught in ${context} boundary`, error, {
      errorId,
      context,
      componentStack: errorInfo.componentStack,
      retryCount: this.state.retryCount
    });

    this.setState({ errorInfo });

    if (onError) {
      onError(error, errorInfo, errorId);
    }

    if (process.env.NODE_ENV === 'production') {
      this.reportErrorToService(error, errorInfo, errorId);
    }
  }

  private reportErrorToService = (error: Error, errorInfo: ErrorInfo, errorId: string) => {
    try {
      console.warn('Error reported to service:', { error, errorInfo, errorId });
    } catch (reportingError) {
      console.error('Failed to report error to service:', reportingError);
    }
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const newRetryCount = this.state.retryCount + 1;

    if (newRetryCount <= maxRetries) {
      log.info('ui', `Retrying after error (attempt ${newRetryCount}/${maxRetries})`, {
        errorId: this.state.errorId,
        retryCount: newRetryCount
      });

      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        retryCount: newRetryCount
      });
    }
  };

  private handleReset = () => {
    log.info('ui', 'Error boundary reset', {
      errorId: this.state.errorId
    });

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: 0
    });
  };

  public componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  public render() {
    if (this.state.hasError && this.state.error && this.state.errorInfo) {
      const { fallback } = this.props;

      if (fallback) {
        return fallback(this.state.error, this.state.errorInfo, this.handleRetry);
      }

      return this.renderDefaultFallback();
    }

    return this.props.children;
  }

  private renderDefaultFallback() {
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <h3 className="text-lg font-semibold text-red-800 mb-2">
          Something went wrong
        </h3>
        <p className="text-red-600 mb-4">
          {this.state.error?.message || 'An unexpected error occurred'}
        </p>
        <div className="flex gap-2">
          <button
            onClick={this.handleRetry}
            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            disabled={this.state.retryCount >= (this.props.maxRetries || 3)}
          >
            Retry ({this.state.retryCount}/{this.props.maxRetries || 3})
          </button>
          <button
            onClick={this.handleReset}
            className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
          >
            Reset
          </button>
        </div>
      </div>
    );
  }
}
