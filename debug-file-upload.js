// Debug script to test file upload flow
// Run this with: node debug-file-upload.js

console.log('🔍 Starting file upload flow debug...');

// Test if the development server endpoints are working
async function testEndpoints() {
  const endpoints = [
    'http://localhost:5174/api/excel-processor',
    'http://localhost:5174/api/repair-excel',
    'http://localhost:5174/api/repair-file'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n🌐 Testing ${endpoint}...`);
      const response = await fetch(endpoint, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://localhost:5174'
        }
      });
      
      console.log(`✅ ${endpoint}: ${response.status} ${response.statusText}`);
      console.log(`   CORS headers:`, response.headers.get('Access-Control-Allow-Origin'));
    } catch (error) {
      console.log(`❌ ${endpoint}: ${error.message}`);
    }
  }
}

// Test with a simple file
async function testFileUpload() {
  // Create a simple test file data
  const testData = {
    fileData: 'UEsDBBQAAAAI', // Simple base64
    fileName: 'test.xlsx',
    fileSize: 1024,
    operation: 'analyze'
  };

  const endpoints = [
    'http://localhost:5174/api/excel-processor',
    'http://localhost:5174/api/repair-excel'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n📤 Testing file upload to ${endpoint}...`);
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:5174'
        },
        body: JSON.stringify(testData)
      });
      
      console.log(`Response: ${response.status} ${response.statusText}`);
      const responseText = await response.text();
      console.log(`Body:`, responseText.substring(0, 200));
    } catch (error) {
      console.log(`❌ ${endpoint}: ${error.message}`);
    }
  }
}

// Run tests
setTimeout(async () => {
  await testEndpoints();
  await testFileUpload();
  console.log('\n🎯 Debug complete. Check the results above.');
}, 1000);
