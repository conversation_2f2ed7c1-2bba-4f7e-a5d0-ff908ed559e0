import * as ExcelJS from 'exceljs';
import { AdvancedExcelRepairService } from './advancedExceljsRepair';

interface RepairOptions {
  removeErrors?: boolean;
  fixFormulas?: boolean;
  cleanEmptyRows?: boolean;
  repairCorruption?: boolean;
  repairMergedCells?: boolean;
  tryAdvancedRepair?: boolean;
  initialParseError?: string | undefined;
}

interface RepairLogEntry {
  sheet: string;
  cell?: string;
  issue: string;
  action: string;
  severity: 'low' | 'medium' | 'high';
}

interface RepairResult {
  success: boolean;
  repairedWorkbook: ExcelJS.Workbook | null;
  repairLog: RepairLogEntry[];
  originalIssues: number;
  repairedIssues: number;
}

interface CorruptionCheckResult {
  needsAdvancedRepair: boolean;
  issueCount: number;
}

export class ExcelRepairService {
  static readonly DEFAULT_OPTIONS: Required<Omit<RepairOptions, 'initialParseError'>> = {
    removeErrors: true,
    fixFormulas: true,
    cleanEmptyRows: true,
    repairCorruption: true,
    repairMergedCells: true,
    tryAdvancedRepair: true
  };

  static async analyzeWorkbook(
    workbook: ExcelJS.Workbook | null,
    options: RepairOptions = {},
    file?: File
  ): Promise<RepairResult> {
    const repairLog: RepairLogEntry[] = [];
    let originalIssues = 0;

    if (options.initialParseError) {
      repairLog.push({
        sheet: 'Global',
        issue: `Initial parsing failed: ${options.initialParseError}`,
        action: 'We can fix this issue',
        severity: 'high'
      });
      originalIssues += 1000;
    }

    if (workbook) {
      workbook.eachSheet((worksheet: ExcelJS.Worksheet, sheetId: number) => {
        const errorIssues = this.detectErrorCells(worksheet);
        repairLog.push(...errorIssues);
        originalIssues += errorIssues.length;

        const formulaIssues = this.detectBrokenFormulas(worksheet);
        repairLog.push(...formulaIssues);
        originalIssues += formulaIssues.length;

        const corruptionIssues = this.detectCorruptedCells(worksheet);
        repairLog.push(...corruptionIssues);
        originalIssues += corruptionIssues.length;
      });
    }

    if (originalIssues === 0) {
      repairLog.push({
        sheet: 'Global',
        issue: 'No problems detected - your file is healthy!',
        action: 'We checked your file thoroughly and found no issues',
        severity: 'low'
      });
    } else {
      repairLog.push({
        sheet: 'Global',
        issue: `Analysis complete - found ${originalIssues} issue${originalIssues !== 1 ? 's' : ''} that can be repaired`,
        action: `We scanned your entire file for common Excel problems`,
        severity: 'low'
      });
    }

    return {
      success: true,
      repairedWorkbook: workbook,
      repairLog,
      originalIssues,
      repairedIssues: 0
    };
  }

  static async repairWorkbook(
    workbook: ExcelJS.Workbook | null,
    options: RepairOptions = {},
    file?: File
  ): Promise<RepairResult> {
    const repairOptions = { ...this.DEFAULT_OPTIONS, ...options };
    const repairLog: RepairLogEntry[] = [];
    let originalIssues = 0;
    let repairedIssues = 0;

    if (options.initialParseError) {
      repairLog.push({
        sheet: 'Global',
        issue: `Initial parsing failed: ${options.initialParseError}`,
        action: 'Flagged for advanced repair',
        severity: 'high'
      });
      originalIssues += 1000;
    }

    if (workbook) {
      originalIssues += this.countIssues(workbook);
    }

    if (file && repairOptions.tryAdvancedRepair && (options.initialParseError || (workbook && (await this.checkForSevereCorruption(workbook)).needsAdvancedRepair))) {
      repairLog.push({
        sheet: 'Global',
        issue: 'Severe corruption detected',
        action: 'Attempting advanced repair',
        severity: 'high'
      });

      const advancedResult = await AdvancedExcelRepairService.repairCorruptedExcel(file);
      if (advancedResult.success && 'repairedWorkbook' in advancedResult && advancedResult.repairedWorkbook) {
        const finalRepairedWorkbook = advancedResult.repairedWorkbook;
        const remainingIssuesAfterAdvanced = this.countIssues(finalRepairedWorkbook);

        return {
          success: true,
          repairedWorkbook: finalRepairedWorkbook,
          repairLog: [
            ...repairLog,
            ...advancedResult.repairLog.map(log => ({
              sheet: 'Global',
              issue: log.message,
              action: 'Advanced repair',
              severity: log.level === 'error' ? 'high' as const : log.level === 'warning' ? 'medium' as const : 'low' as const
            }))
          ],
          originalIssues: originalIssues,
          repairedIssues: originalIssues - remainingIssuesAfterAdvanced
        };
      } else {
        return {
          success: false,
          repairedWorkbook: workbook || new ExcelJS.Workbook(),
          repairLog: [
            ...repairLog,
            ...advancedResult.repairLog.map((log: any) => ({
              sheet: 'Global',
              issue: log.message,
              action: 'Advanced repair failed',
              severity: log.level === 'error' ? 'high' as const : log.level === 'warning' ? 'medium' as const : 'low' as const
            }))
          ],
          originalIssues: originalIssues,
          repairedIssues: 0
        };
      }
    }

    const repairedWorkbook = workbook ? await this.cloneWorkbook(workbook) : new ExcelJS.Workbook();

    try {
      repairedWorkbook.eachSheet((worksheet: ExcelJS.Worksheet, sheetId: number) => {
        if (repairOptions.removeErrors) {
          const errorRepairs = this.removeErrorCells(worksheet);
          repairLog.push(...errorRepairs);
        }

        if (repairOptions.fixFormulas) {
          const formulaRepairs = this.fixBrokenFormulas(worksheet);
          repairLog.push(...formulaRepairs);
        }

        if (repairOptions.cleanEmptyRows) {
          const cleanupRepairs = this.cleanEmptyRowsAndColumns(worksheet);
          repairLog.push(...cleanupRepairs);
        }

        if (repairOptions.repairCorruption) {
          const corruptionRepairs = this.repairCorruptedCells(worksheet);
          repairLog.push(...corruptionRepairs);
        }

        if (repairOptions.repairMergedCells) {
          const mergeRepairs = this.repairMergedCells(worksheet);
          repairLog.push(...mergeRepairs);
        }
      });

      const remainingIssues = this.countIssues(repairedWorkbook);
      repairedIssues = originalIssues - remainingIssues;

      return {
        success: true,
        repairedWorkbook,
        repairLog,
        originalIssues,
        repairedIssues
      };

    } catch (error) {
      repairLog.push({
        sheet: 'Global',
        issue: 'Repair failed: An unexpected error occurred during repair.',
        action: 'Failed to complete repair',
        severity: 'high'
      });

      return {
        success: false,
        repairedWorkbook: null,
        repairLog,
        originalIssues,
        repairedIssues: 0
      };
    }
  }

  static detectErrorCells(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    const repairLog: RepairLogEntry[] = [];
    try {
      worksheet.eachRow((row: ExcelJS.Row, rowNumber: number) => {
        row.eachCell((cell: ExcelJS.Cell, colNumber: number) => {
          try {
            // Check for error cells using safer method
            if (cell.type === 6 || // ExcelJS.ValueType.Error numerical value
                (typeof cell.value === 'object' && cell.value && 'error' in cell.value) ||
                (typeof cell.value === 'string' && cell.value.startsWith('#'))) {
              repairLog.push({
                sheet: worksheet.name,
                cell: cell.address,
                issue: `Error value: ${cell.value}`,
                action: 'Detected error cell',
                severity: 'medium'
              });
            }
          } catch (cellError) {
            console.warn(`Error checking cell at ${rowNumber}, ${colNumber}:`, cellError);
          }
        });
      });
    } catch (error) {
      console.warn(`Error detecting error cells in worksheet ${worksheet.name}:`, error);
    }
    return repairLog;
  }

  static detectBrokenFormulas(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    const repairLog: RepairLogEntry[] = [];
    try {
      worksheet.eachRow((row: ExcelJS.Row, rowNumber: number) => {
        row.eachCell((cell: ExcelJS.Cell, colNumber: number) => {
          try {
            // Check for formula cells using safer method
            if ((cell.type === 2 || // ExcelJS.ValueType.Formula numerical value
                 (cell as any).formula) &&
                (typeof cell.value === 'object' && cell.value && 'error' in cell.value)) {
              repairLog.push({
                sheet: worksheet.name,
                cell: cell.address,
                issue: `Broken formula: ${(cell as any).formula || 'Unknown formula'}`,
                action: 'Detected broken formula',
                severity: 'medium'
              });
            }
          } catch (cellError) {
            console.warn(`Error checking formula cell at ${rowNumber}, ${colNumber}:`, cellError);
          }
        });
      });
    } catch (error) {
      console.warn(`Error detecting broken formulas in worksheet ${worksheet.name}:`, error);
    }
    return repairLog;
  }

  static detectCorruptedCells(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    return [];
  }

  static removeErrorCells(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    const repairLog: RepairLogEntry[] = [];
    try {
      worksheet.eachRow((row: ExcelJS.Row, rowNumber: number) => {
        row.eachCell((cell: ExcelJS.Cell, colNumber: number) => {
          try {
            // Check for error cells using safer method
            if (cell.type === 6 || // ExcelJS.ValueType.Error numerical value
                (typeof cell.value === 'object' && cell.value && 'error' in cell.value) ||
                (typeof cell.value === 'string' && cell.value.startsWith('#'))) {
              repairLog.push({
                sheet: worksheet.name,
                cell: cell.address,
                issue: `Error value: ${cell.value}`,
                action: 'Removed error cell',
                severity: 'medium'
              });
              cell.value = null;
            }
          } catch (cellError) {
            console.warn(`Error removing error cell at ${rowNumber}, ${colNumber}:`, cellError);
          }
        });
      });
    } catch (error) {
      console.warn(`Error removing error cells in worksheet ${worksheet.name}:`, error);
    }
    return repairLog;
  }

  static fixBrokenFormulas(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    const repairLog: RepairLogEntry[] = [];
    try {
      worksheet.eachRow((row: ExcelJS.Row, rowNumber: number) => {
        row.eachCell((cell: ExcelJS.Cell, colNumber: number) => {
          try {
            // Check for formula cells using safer method
            if ((cell.type === 2 || // ExcelJS.ValueType.Formula numerical value
                 (cell as any).formula) &&
                (typeof cell.value === 'object' && cell.value && 'error' in cell.value)) {
              repairLog.push({
                sheet: worksheet.name,
                cell: cell.address,
                issue: `Broken formula: ${(cell as any).formula || 'Unknown formula'}`,
                action: 'Reset formula to a safe value',
                severity: 'medium'
              });
              cell.value = 0;
            }
          } catch (cellError) {
            console.warn(`Error fixing formula cell at ${rowNumber}, ${colNumber}:`, cellError);
          }
        });
      });
    } catch (error) {
      console.warn(`Error fixing broken formulas in worksheet ${worksheet.name}:`, error);
    }
    return repairLog;
  }

  static cleanEmptyRowsAndColumns(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    return [];
  }

  static repairCorruptedCells(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    return [];
  }

  static repairMergedCells(worksheet: ExcelJS.Worksheet): RepairLogEntry[] {
    return [];
  }

  static countIssues(workbook: ExcelJS.Workbook): number {
    let issueCount = 0;
    try {
      workbook.eachSheet((worksheet: ExcelJS.Worksheet, sheetId: number) => {
        worksheet.eachRow((row: ExcelJS.Row, rowNumber: number) => {
          row.eachCell((cell: ExcelJS.Cell, colNumber: number) => {
            try {
              // Count error cells using safer method
              if (cell.type === 6 || // ExcelJS.ValueType.Error numerical value
                  (typeof cell.value === 'object' && cell.value && 'error' in cell.value) ||
                  (typeof cell.value === 'string' && cell.value.startsWith('#'))) {
                issueCount++;
              }
              // Count broken formulas
              if ((cell.type === 2 || // ExcelJS.ValueType.Formula numerical value
                   (cell as any).formula) &&
                  (typeof cell.value === 'object' && cell.value && 'error' in cell.value)) {
                issueCount++;
              }
            } catch (cellError) {
              console.warn(`Error counting issues for cell at ${rowNumber}, ${colNumber}:`, cellError);
            }
          });
        });
      });
    } catch (error) {
      console.warn(`Error counting issues:`, error);
    }
    return issueCount;
  }

  static async cloneWorkbook(workbook: ExcelJS.Workbook): Promise<ExcelJS.Workbook> {
    const newWorkbook = new ExcelJS.Workbook();
    const buffer = await workbook.xlsx.writeBuffer();
    await newWorkbook.xlsx.load(buffer);
    return newWorkbook;
  }

  static async checkForSevereCorruption(workbook: ExcelJS.Workbook): Promise<CorruptionCheckResult> {
    return { needsAdvancedRepair: false, issueCount: 0 };
  }
}