import XLSX from 'xlsx';

// Create a workbook with actual Excel errors
const wb = XLSX.utils.book_new();

// Create a worksheet with various Excel errors
const errorData = [
  ['Description', 'Formula', 'Value', 'Notes'],
  ['Division by Zero', '=10/0', '=10/0', 'Should show #DIV/0!'],
  ['Invalid Reference', '=XYZ999', '=XYZ999', 'Should show #NAME?'],
  ['Broken Reference', '=A100:B200', '=A100:B200', 'Should show #REF!'],
  ['Value Error', '=VALUE("text")', '=VALUE("text")', 'Should show #VALUE!'],
  ['Null Error', '=A1 A2', '=A1 A2', 'Should show #NULL!'],
  ['Number Error', '=SQRT(-1)', '=SQRT(-1)', 'Should show #NUM!'],
  ['Circular Reference', '=B7', '=B7', 'May cause circular reference'],
  ['Wrong Function Name', '=SUMM(A1:A5)', '=SUMM(A1:A5)', 'SUMM should be SUM'],
  ['Unbalanced Parentheses', '=SUM(A1:A5', '=SUM(A1:A5', 'Missing closing parenthesis'],
  ['Valid Data', '=SUM(1,2,3)', 6, 'This should work fine']
];

// Create the worksheet
const ws = XLSX.utils.aoa_to_sheet(errorData);

// Manually set formulas that will create errors
ws['B2'] = { f: '10/0', t: 'e', v: '#DIV/0!' };
ws['B3'] = { f: 'XYZ999', t: 'e', v: '#NAME?' };
ws['B4'] = { f: 'A100:B200', t: 'e', v: '#REF!' };
ws['B5'] = { f: 'VALUE("text")', t: 'e', v: '#VALUE!' };
ws['B6'] = { f: 'A1 A2', t: 'e', v: '#NULL!' };
ws['B7'] = { f: 'SQRT(-1)', t: 'e', v: '#NUM!' };
ws['B8'] = { f: 'B8', t: 'e', v: '#REF!' }; // Circular reference
ws['B9'] = { f: 'SUMM(A1:A5)', t: 'e', v: '#NAME?' }; // Wrong function name
ws['B10'] = { f: 'SUM(A1:A5', t: 'e', v: '#NAME?' }; // Unbalanced parentheses
ws['B11'] = { f: 'SUM(1,2,3)', t: 'n', v: 6 }; // Valid formula

// Add the worksheet to workbook
XLSX.utils.book_append_sheet(wb, ws, 'ErrorSheet');

// Create another sheet with mixed issues
const mixedData = [
  ['Name', 'Age', 'Salary', 'Bonus'],
  ['John', 25, 50000, '=C2*0.1'],
  ['Jane', '=INVALID()', 60000, '=C3*0.1'],
  ['Bob', 30, '=D2/0', '=C4*0.1'],
  ['Alice', 28, 55000, '=SUMM(C2:C4)']
];

const ws2 = XLSX.utils.aoa_to_sheet(mixedData);

// Set formulas that will create specific errors
ws2['B3'] = { f: 'INVALID()', t: 'e', v: '#NAME?' };
ws2['C4'] = { f: 'D2/0', t: 'e', v: '#DIV/0!' };
ws2['D2'] = { f: 'C2*0.1', t: 'n', v: 5000 };
ws2['D3'] = { f: 'C3*0.1', t: 'n', v: 6000 };
ws2['D4'] = { f: 'C4*0.1', t: 'e', v: '#DIV/0!' };
ws2['D5'] = { f: 'SUMM(C2:C4)', t: 'e', v: '#NAME?' };

XLSX.utils.book_append_sheet(wb, ws2, 'MixedData');

// Write the file
XLSX.writeFile(wb, 'test-with-real-errors.xlsx');

console.log('✅ Created test-with-real-errors.xlsx with actual Excel errors:');
console.log('   - #DIV/0! errors (division by zero)');
console.log('   - #NAME? errors (invalid function names)');
console.log('   - #REF! errors (broken references)');
console.log('   - #VALUE! errors (wrong data types)');
console.log('   - #NULL! errors (invalid ranges)');
console.log('   - #NUM! errors (invalid numbers)');
console.log('   - Wrong function names (SUMM instead of SUM)');
console.log('   - Unbalanced parentheses');
console.log('   - Circular references');
console.log('\nNow you can test the repair functionality with real errors!'); 