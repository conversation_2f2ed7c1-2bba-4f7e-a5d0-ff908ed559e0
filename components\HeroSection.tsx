import React from 'react';
import { Link } from 'react-router-dom';
import { Rocket, FileSpreadsheet } from 'lucide-react';

const HeroSection: React.FC = () => {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            
            {/* Left Content */}
            <div className="space-y-8">
              {/* Badge */}
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-lime-100 text-lime-800 rounded-full text-sm font-medium">
                <Rocket className="h-4 w-4" />
                v1 Out Now!
              </div>
              
              {/* Main Heading */}
              <div className="space-y-6">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Fix Your
                  <br />
                  <span className="bg-lime-300 px-1 py-0.5 rounded-lg">Corrupted</span>
                  <br />
                  Excel Files Instantly
                </h1>
                
                <p className="text-lg text-gray-600 max-w-lg">
                  Upload your broken .xlsx file and get it repaired in seconds. Our advanced algorithms 
                  fix common corruption issues, recover cell data, and repair broken formulas - all processed securely 
                  in your browser.
                </p>
              </div>
              
              {/* Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  to="/repair"
                  className="inline-flex items-center justify-center px-8 py-4 bg-gray-900 text-white border border-gray-900 rounded-lg font-medium transition-colors hover:bg-lime-300 hover:text-gray-900 hover:border-gray-900"
                >
                  Repair Your File Now
                </Link>
                
                
              </div>

              {/* Stats */}
              <div className="flex items-center gap-8 pt-4">
                <div className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900">99.9%</div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900">5s</div>
                  <div className="text-sm text-gray-600">Avg Repair Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900">256-bit</div>
                  <div className="text-sm text-gray-600">Encryption</div>
                </div>
              </div>
            </div>
            
            {/* Right Visual */}
            <div className="relative">
              <div className="relative">
                {/* Main Hero Image */}
                <div className="aspect-[5/4] lg:aspect-[4/3] relative" style={{
                  backgroundImage: 'url(/heroimage-new1.webp)',
                  backgroundSize: 'contain',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center'
                }}>
                  {/* Gradient Overlay for better visual appeal */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                </div>
                
                {/* Floating elements - Positivus style */}
                <div className="absolute -top-3 -left-3 lg:-top-4 lg:-left-4 bg-lime-300 text-gray-900 rounded-2xl px-3 py-2 lg:px-4 lg:py-2 font-medium text-sm lg:text-base">
                  ✨ File Repaired!
                </div>
                
                <div className="absolute -bottom-3 -right-3 lg:-bottom-4 lg:-right-4 bg-white rounded-2xl px-3 py-2 lg:px-4 lg:py-2 shadow-lg border border-gray-200">
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-3 w-3 lg:h-4 lg:w-4 text-gray-600" />
                    <span className="text-xs lg:text-sm font-medium text-gray-900">Auto-Delete in 24h</span>
                  </div>
                </div>
              </div>
              
              {/* Decorative elements */}
              <div className="absolute -z-10 top-4 right-4 lg:top-8 lg:right-8 w-16 h-16 lg:w-24 lg:h-24 bg-lime-200 rounded-full opacity-50"></div>
              <div className="absolute -z-10 bottom-4 left-4 lg:bottom-8 lg:left-8 w-12 h-12 lg:w-16 lg:h-16 bg-yellow-200 rounded-full opacity-50"></div>
            </div>
          </div>
      </div>
    </section>
    </div>
  );
};

export default HeroSection;
