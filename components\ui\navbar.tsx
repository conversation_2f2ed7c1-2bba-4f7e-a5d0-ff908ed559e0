import { useState } from 'react';
import { motion, MotionConfig } from 'framer-motion';
import { INavbarMenu } from '../../types';

type MenuProps = {
  list: INavbarMenu[];
};

const NavbarUI = ({ list }: MenuProps) => {
  const [hovered, setHovered] = useState<number | null>(null);

  return (
    <MotionConfig transition={{ bounce: 0, type: 'tween' }}>
      <nav className={'relative'}>
        <ul className={'flex items-center'}>
          {list?.map((item) => {
            return (
              <li key={item.id} className={'relative'}>
                <a
                  className={`
                    relative flex items-center justify-center rounded-full px-4 py-1.5 transition-all text-sm font-medium font-body tracking-wide
                    hover:bg-white/20
                    ${hovered === item?.id ? 'bg-white/20' : ''}
                  `}
                  onMouseEnter={() => setHovered(item.id)}
                  onMouseLeave={() => setHovered(null)}
                  href={item?.url}
                  onClick={(e) => {
                    e.preventDefault();
                    const targetId = item?.url.replace('#', '');
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                      targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                  }}
                >
                  {item?.title}
                </a>
                {hovered === item?.id && !item?.dropdown && (
                  <motion.div
                    layout
                    layoutId={`cursor`}
                    className={'absolute h-0.5 w-full bg-foreground'}
                  />
                )}
                {item?.dropdown && hovered === item?.id && (
                  <div
                    className='absolute left-0 top-full'
                    onMouseEnter={() => setHovered(item.id)}
                    onMouseLeave={() => setHovered(null)}
                  >
                    <motion.div
                      layout
                      transition={{ bounce: 0 }}
                      initial={{ y: 10 }}
                      animate={{ y: 0 }}
                      exit={{ y: 10 }}
                      style={{
                        borderRadius: '8px',
                      }}
                      className='mt-2 flex w-56 flex-col rounded-2xl bg-white/20 backdrop-blur-xl border border-white/30 shadow-2xl'
                      layoutId={'cursor'}
                    >
                      {item?.items?.map((nav) => {
                        return (
                          <motion.a
                            key={`link-${nav?.id}`}
                            href={`${nav?.url}`}
                            className={'w-full p-3 hover:bg-white/20 text-sm rounded-xl transition-all duration-200'}
                          >
                            {nav?.title}
                          </motion.a>
                        );
                      })}
                    </motion.div>
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      </nav>
    </MotionConfig>
  );
};

export default NavbarUI;