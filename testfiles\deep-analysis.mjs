import fs from 'fs';
import path from 'path';

class DeepFileAnalyzer {
  static analyzeCorruptedFile(filePath) {
    console.log(`\n🔍 DEEP ANALYSIS: ${path.basename(filePath)}`);
    console.log('=' .repeat(60));
    
    try {
      // Read file as buffer for binary analysis
      const buffer = fs.readFileSync(filePath);
      const fileSize = buffer.length;
      
      console.log(`📁 File Size: ${(fileSize / 1024).toFixed(2)} KB`);
      
      // Check for ZIP signature (Excel files are ZIP archives)
      const zipSignature = buffer.slice(0, 4);
      const hasZipSignature = (
        zipSignature[0] === 0x50 && 
        zipSignature[1] === 0x4B && 
        (zipSignature[2] === 0x03 || zipSignature[2] === 0x05 || zipSignature[2] === 0x07) &&
        (zipSignature[3] === 0x04 || zipSignature[3] === 0x06 || zipSignature[3] === 0x08)
      );
      
      console.log(`📦 ZIP Signature: ${hasZipSignature ? '✅ Valid' : '❌ Missing/Invalid'}`);
      console.log(`   First 4 bytes: ${Array.from(zipSignature).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}`);
      
      // Check for Excel-specific content
      const content = buffer.toString('binary');
      const hasExcelContent = content.includes('xl/') || content.includes('docProps/') || content.includes('_rels/');
      console.log(`📊 Excel Content: ${hasExcelContent ? '✅ Found Excel directory structure' : '❌ No Excel directories found'}`);
      
      // Look for common Excel files within the archive
      const hasWorksheets = content.includes('xl/worksheets/');
      const hasSharedStrings = content.includes('xl/sharedStrings.xml');
      const hasWorkbook = content.includes('xl/workbook.xml');
      const hasStyles = content.includes('xl/styles.xml');
      
      console.log(`📋 Internal Structure:`);
      console.log(`   Worksheets: ${hasWorksheets ? '✅ Found' : '❌ Missing'}`);
      console.log(`   Shared Strings: ${hasSharedStrings ? '✅ Found' : '❌ Missing'}`);
      console.log(`   Workbook XML: ${hasWorkbook ? '✅ Found' : '❌ Missing'}`);
      console.log(`   Styles: ${hasStyles ? '✅ Found' : '❌ Missing'}`);
      
      // Check for common corruption patterns
      const hasNullBytes = buffer.includes(0x00);
      const nullByteRatio = buffer.filter(b => b === 0x00).length / buffer.length;
      
      console.log(`🔍 Corruption Analysis:`);
      console.log(`   Null bytes present: ${hasNullBytes ? '⚠️ Yes' : '✅ No'}`);
      console.log(`   Null byte ratio: ${(nullByteRatio * 100).toFixed(2)}%`);
      
      // Check if file is mostly empty/zeros
      if (nullByteRatio > 0.5) {
        console.log(`   🔥 SEVERE: File is mostly null bytes (${(nullByteRatio * 100).toFixed(1)}%)`);
      } else if (nullByteRatio > 0.1) {
        console.log(`   ⚠️ MODERATE: High null byte content (${(nullByteRatio * 100).toFixed(1)}%)`);
      }
      
      // Look for readable text content
      const readableText = content.replace(/[^\x20-\x7E]/g, '').length;
      const textRatio = readableText / buffer.length;
      console.log(`   Readable text ratio: ${(textRatio * 100).toFixed(2)}%`);
      
      // Determine corruption severity
      let corruptionLevel = 'UNKNOWN';
      if (!hasZipSignature && nullByteRatio > 0.3) {
        corruptionLevel = 'SEVERE - File structure completely damaged';
      } else if (!hasZipSignature) {
        corruptionLevel = 'HIGH - ZIP signature corrupted';
      } else if (!hasExcelContent) {
        corruptionLevel = 'HIGH - Excel structure missing';
      } else if (!hasWorkbook || !hasWorksheets) {
        corruptionLevel = 'MODERATE - Core Excel files missing';
      } else {
        corruptionLevel = 'MILD - Structure intact, possible data corruption';
      }
      
      console.log(`\n🎯 CORRUPTION ASSESSMENT: ${corruptionLevel}`);
      
      // Repair recommendations
      console.log(`\n💡 REPAIR STRATEGY:`);
      if (corruptionLevel.includes('SEVERE')) {
        console.log(`   → Advanced repair needed (binary reconstruction)`);
        console.log(`   → Data recovery may be limited`);
        console.log(`   → Consider manual data extraction`);
      } else if (corruptionLevel.includes('HIGH')) {
        console.log(`   → Advanced repair with ZIP reconstruction`);
        console.log(`   → Good chance of data recovery`);
      } else {
        console.log(`   → Standard repair should work`);
        console.log(`   → High chance of full recovery`);
      }
      
      return {
        fileName: path.basename(filePath),
        fileSize,
        hasZipSignature,
        hasExcelContent,
        corruptionLevel,
        nullByteRatio,
        textRatio,
        canTryStandardRepair: hasZipSignature && hasExcelContent,
        needsAdvancedRepair: !hasZipSignature || !hasExcelContent
      };
      
    } catch (error) {
      console.log(`❌ Error analyzing file: ${error.message}`);
      return {
        fileName: path.basename(filePath),
        error: error.message,
        analysisFailure: true
      };
    }
  }
}

// Command line usage
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log(`
🔍 Deep File Corruption Analysis Tool

Usage:
  node deep-analysis.mjs <corrupted-file.xlsx>
  
This tool analyzes corrupted Excel files at the binary level to:
- Check ZIP signature validity
- Examine internal Excel structure
- Assess corruption severity
- Recommend repair strategy
  `);
} else {
  DeepFileAnalyzer.analyzeCorruptedFile(args[0]);
} 