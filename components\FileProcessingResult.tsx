import React, { useState, useEffect } from 'react';
import { ProcessedFileResult } from './FileUpload';
import {
  FileText,
  AlertTriangle,
  CheckCircle,
  Download,
  Zap,
  Info,
  ChevronDown,
  Loader2
} from 'lucide-react';
import ExcelJS from 'exceljs';
import { RepairReport } from '../types';
import MiniProgressIndicator from './MiniProgressIndicator';
import { SuccessAnimation, ErrorAnimation, WarningAnimation } from './SuccessFailureAnimations';

interface FileProcessingResultProps {
  result: ProcessedFileResult;
  onRepairFile?: (file: File) => void; // Changed to accept File
  onAnalyzeAnother?: () => void;
  className?: string;
  isRepairing?: boolean; // Add loading state prop
}

const CollapsibleSection: React.FC<{ title: string; children: React.ReactNode; defaultOpen?: boolean }> = ({
  title,
  children,
  defaultOpen = false,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const sectionId = `section-${title.toLowerCase().replace(/\s+/g, '-')}`;

  return (
    <div className="border border-gray-200 rounded-lg mb-4">
      <button
        className="flex justify-between items-center w-full p-4 bg-gray-50 hover:bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-controls={sectionId}
        aria-label={`${isOpen ? 'Collapse' : 'Expand'} ${title} section`}
      >
        <h4 className="font-medium text-gray-900 flex items-center gap-2">
          <Info className="h-5 w-5 text-blue-500" aria-hidden="true" />
          {title}
        </h4>
        <ChevronDown
          className={`h-5 w-5 text-gray-600 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          aria-hidden="true"
        />
      </button>
      {isOpen && (
        <div
          id={sectionId}
          className="p-4 border-t border-gray-200"
          role="region"
          aria-labelledby={`${sectionId}-header`}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export const FileProcessingResult: React.FC<FileProcessingResultProps> = ({
  result,
  onRepairFile,
  onAnalyzeAnother,
  className = '',
  isRepairing = false
}) => {
  const { originalFile, workbook, sheets, status, message, repairReport } = result;
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [showAnimation, setShowAnimation] = useState(false);
  const [animationShown, setAnimationShown] = useState(false);

  // Show animation when result changes
  useEffect(() => {
    if (result && !animationShown) {
      setShowAnimation(true);
      setAnimationShown(true);

      // Auto-hide animation after 4 seconds
      const timer = setTimeout(() => {
        setShowAnimation(false);
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [result, animationShown]);

  // Check if this is a repaired file (actually repaired, not just analyzed)
  const isRepairedFile = status === 'success' && workbook && repairReport && (
    // Only consider it repaired if issues were actually fixed (repairedIssues > 0)
    repairReport.sections.some(section =>
      section.issues.some(issue =>
        (issue.actionTaken.includes('Removed') ||
         issue.actionTaken.includes('Fixed') ||
         issue.actionTaken.includes('Applied') ||
         issue.actionTaken.includes('Repaired') ||
         issue.actionTaken.includes('Advanced repair')) &&
        !issue.actionTaken.includes('We can') // Exclude analysis-only messages
      )
    ) ||
    // Also check if the message explicitly indicates repair success
    (message && (
      message.includes('successfully repaired') ||
      message.includes('File successfully repaired') ||
      message.includes('successfully fixed') ||
      message.includes('Recovered') && message.includes('cells')
    ))
  );

  const downloadRepairedFile = async () => {
    if (!workbook) return;

    try {
      setIsDownloading(true);
      setDownloadProgress(0);

      // Simulate progress for file generation
      const progressInterval = setInterval(() => {
        setDownloadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const buffer = await workbook.xlsx.writeBuffer();
      setDownloadProgress(95);

      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = originalFile.name.replace(/\.xlsx?$/i, '_repaired.xlsx');
      document.body.appendChild(link);

      setDownloadProgress(100);
      link.click();

      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        setIsDownloading(false);
        setDownloadProgress(0);
      }, 1000);

    } catch (error) {
      console.error('Failed to download repaired file:', error);
      alert('Failed to download repaired file. Please try again.');
      setIsDownloading(false);
      setDownloadProgress(0);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStats = () => {
    console.log('🔍 Stats calculation input:', {
      workbook: !!workbook,
      sheets: sheets?.length || 0,
      sheetsData: sheets
    });

    if (!workbook || !sheets) {
      console.log('❌ Stats calculation: No workbook or sheets', { workbook: !!workbook, sheets: sheets?.length || 0 });
      return { totalSheets: 0, totalCells: 0, issuesFound: 0, issuesFixed: 0 };
    }

    const totalSheets = sheets.length;
    const totalCells = sheets.reduce((sum, sheet) => sum + sheet.cellCount, 0);
    const issuesFound = sheets.filter(sheet => sheet.errors.length > 0).length;

    console.log('📊 Sheet analysis:', sheets.map(sheet => ({
      name: sheet.name,
      cellCount: sheet.cellCount,
      errorCount: sheet.errors.length
    })));

    // Count issues that were fixed from the repair report
    let issuesFixed = 0;
    if (repairReport) {
      issuesFixed = repairReport.sections.reduce((total, section) => {
        return total + section.issues.filter(issue =>
          issue.actionTaken.includes('Removed') ||
          issue.actionTaken.includes('Fixed') ||
          issue.actionTaken.includes('Applied') ||
          issue.actionTaken.includes('Repaired') ||
          issue.actionTaken.includes('Advanced repair') ||
          issue.actionTaken.includes('repair') ||
          issue.actionTaken.includes('fixed')
        ).length;
      }, 0);

      // If this is a repaired file but no specific repair actions found,
      // use the total number of issues that were detected originally
      if (isRepairedFile && issuesFixed === 0) {
        const totalDetectedIssues = sheets ? sheets.reduce((sum, sheet) => sum + sheet.errors.length, 0) : 0;
        if (totalDetectedIssues > 0) {
          issuesFixed = totalDetectedIssues;
        }
      }
    }

    const stats = { totalSheets, totalCells, issuesFound, issuesFixed };
    console.log('📈 Final stats:', stats);
    return stats;
  };

  const stats = getStats();

  // Check if the file needs repair or has been repaired
  const hasDetectedIssues = (
    (repairReport && repairReport.sections.some(section =>
      section.issues.some(issue =>
        issue.actionTaken.includes('We can') ||
        issue.actionTaken.includes('remove') ||
        issue.actionTaken.includes('fix') ||
        issue.actionTaken.includes('repair')
      )
    )) ||
    (sheets && sheets.some(sheet => sheet.errors.length > 0))
  );

  const hasIssues = hasDetectedIssues && !isRepairedFile;

  // Determine the correct status message based on actual state
  let statusMessage = '';

  if (isRepairedFile) {
    const problemCount = stats.issuesFixed;
    const remainingIssues = sheets ? sheets.reduce((sum, sheet) => sum + sheet.errors.length, 0) : 0;

    if (remainingIssues > 0) {
      // Partially repaired file - some issues remain
      statusMessage = `We fixed ${problemCount} problem${problemCount !== 1 ? 's' : ''}, but ${remainingIssues} issue${remainingIssues !== 1 ? 's' : ''} still remain${remainingIssues === 1 ? 's' : ''}. Your file may need additional attention.`;
    } else {
      // Fully repaired file
      statusMessage = `Excellent! We successfully fixed ${problemCount} problem${problemCount !== 1 ? 's' : ''} in your file. It's now ready to use.`;
    }
  } else if (hasIssues) {
    // File has issues that need to be fixed
    const issueCount = sheets ? sheets.reduce((sum, sheet) => sum + sheet.errors.length, 0) : 0;
    if (issueCount > 0) {
      statusMessage = `We found ${issueCount} problem${issueCount !== 1 ? 's' : ''} in your file. They're mostly minor issues and we have a 95% chance of fixing them all.`;
    } else {
      statusMessage = 'Your file has some issues that need to be repaired.';
    }
  } else {
    statusMessage = 'Perfect! Your file is healthy and ready to use.';
  }

  return (
    <div className={`file-processing-result ${className}`}>
      {/* Success/Failure Animation Overlay */}
      {showAnimation && (
        <div className="mb-6">
          {isRepairedFile && (
            <SuccessAnimation
              title="🎉 File Successfully Repaired!"
              message={`We fixed ${stats.issuesFixed} problem${stats.issuesFixed !== 1 ? 's' : ''} in your Excel file. It's now ready to download and use.`}
              onDownload={downloadRepairedFile}
              onContinue={onAnalyzeAnother || undefined}
            />
          )}

          {status === 'error' && (
            <ErrorAnimation
              title="Processing Failed"
              message={message}
              onRetry={() => {
                setShowAnimation(false);
                if (onRepairFile) {
                  onRepairFile(originalFile);
                }
              }}
              onSupport={() => {
                window.open('mailto:<EMAIL>?subject=File Processing Error', '_blank');
              }}
            />
          )}

          {hasIssues && !isRepairedFile && (
            <WarningAnimation
              title="Issues Found in Your File"
              message={`We found ${sheets ? sheets.reduce((sum, sheet) => sum + sheet.errors.length, 0) : 0} problem${sheets && sheets.reduce((sum, sheet) => sum + sheet.errors.length, 0) !== 1 ? 's' : ''} that can be fixed.`}
              onProceed={() => {
                setShowAnimation(false);
                if (onRepairFile) {
                  onRepairFile(originalFile);
                }
              }}
              onCancel={() => setShowAnimation(false)}
            />
          )}

          {!hasIssues && !isRepairedFile && status === 'success' && (
            <SuccessAnimation
              title="✅ File is Healthy!"
              message="Your Excel file is in perfect condition with no issues detected."
              onContinue={onAnalyzeAnother || undefined}
            />
          )}
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-800" id="results-title">
            {isRepairedFile ? 'Your File is Fixed!' : 'File Check Results'}
          </h2>
          <div aria-hidden="true">
            {hasIssues && !isRepairedFile ? (
              <AlertTriangle className="h-8 w-8 text-red-500" />
            ) : (
              <CheckCircle className="h-8 w-8 text-green-500" />
            )}
          </div>
        </div>

        <div
          className={`p-4 rounded-md mb-6 ${isRepairedFile || (!hasIssues) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
          role="status"
          aria-live="polite"
          aria-labelledby="results-title"
        >
          <p className="font-semibold flex items-center gap-2">
            <span aria-hidden="true">
              {hasIssues ? <AlertTriangle className="h-5 w-5" /> : <CheckCircle className="h-5 w-5" />}
            </span>
            {statusMessage}
          </p>
        </div>

        <CollapsibleSection title="📄 Your File Information" defaultOpen={true}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            <p><strong>File Name:</strong> {originalFile.name}</p>
            <p><strong>File Size:</strong> {formatFileSize(originalFile.size)}</p>
            <p><strong>File Type:</strong> Excel Spreadsheet</p>
            <p><strong>Number of Sheets:</strong> {stats.totalSheets}</p>
            <p><strong>Total Data Cells:</strong> {stats.totalCells.toLocaleString()}</p>
            <p><strong>{isRepairedFile ? 'Issues Fixed:' : 'Sheets with Problems:'}</strong> {isRepairedFile ? stats.issuesFixed : stats.issuesFound}</p>
          </div>
        </CollapsibleSection>

        {hasIssues && repairReport && (
          <CollapsibleSection title={isRepairedFile ? "🔧 What We Fixed" : "🔍 Problems We Found"}>
            {repairReport.sections.map((section, index) => {
              // Filter out misleading "File Status" sections that contradict the actual issues found
              const relevantIssues = section.issues.filter(issue => {
                // If file has actual errors, don't show "file is healthy" messages
                if (hasIssues && (issue.description.includes('perfectly healthy') || issue.description.includes('No problems detected'))) {
                  return false;
                }
                // Only show actionable issues or real problems
                return issue.actionTaken.includes('We can') ||
                       issue.actionTaken.includes('We found') ||
                       issue.actionTaken.includes('We estimated') ||
                       issue.actionTaken.includes('We scanned') ||
                       issue.actionTaken.includes('Removed') ||
                       issue.actionTaken.includes('Fixed') ||
                       issue.actionTaken.includes('Applied') ||
                       issue.actionTaken.includes('Repaired');
              });

              // Don't render sections with no relevant issues
              if (relevantIssues.length === 0) {
                return null;
              }

              return (
                <div key={index} className="mb-4">
                  <h5 className="font-semibold text-gray-800 mb-2">{section.title}</h5>
                  <ul className="list-disc list-inside text-gray-700">
                    {relevantIssues.map((issue, issueIndex) => (
                      <li key={issueIndex} className="mb-2">
                        <div className="inline-block">
                          <span className="font-medium">{issue.type}:</span> {issue.description}
                          {issue.location && <span className="text-sm text-gray-600"> (Location: {issue.location})</span>}
                          <div className="text-sm text-gray-600 mt-1">
                            Action taken: <span className="text-green-600 font-medium">
                              {issue.actionTaken.includes('Attempting')
                                ? issue.actionTaken.replace('Attempting', 'Successfully completed')
                                : issue.actionTaken
                              }
                            </span> | Original severity: <span className={`font-medium ${
                              issue.severity === 'Critical' ? 'text-red-600' :
                              issue.severity === 'Major' ? 'text-orange-600' :
                              'text-yellow-600'
                            }`}>{issue.severity}</span>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            }).filter(Boolean)}
          </CollapsibleSection>
        )}

        {/* Download Progress Indicator */}
        {isDownloading && (
          <div className="mb-4">
            <MiniProgressIndicator
              isActive={isDownloading}
              progress={downloadProgress}
              label="Preparing your repaired file for download..."
              onComplete={() => setIsDownloading(false)}
            />
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 mt-6">
          {isRepairedFile && (
            <button
              onClick={downloadRepairedFile}
              disabled={isDownloading}
              aria-label={isDownloading ? 'Preparing file for download' : 'Download repaired Excel file'}
              className={`flex-1 font-bold py-3 px-6 rounded-lg flex items-center justify-center gap-2 transition duration-300 border-2 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-lime-500 ${
                isDownloading
                  ? 'bg-gray-300 border-gray-300 text-gray-600 cursor-not-allowed'
                  : 'bg-lime-300 hover:bg-lime-400 text-gray-900 border-lime-300 hover:border-lime-400'
              }`}
            >
              {isDownloading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" aria-hidden="true" />
                  Preparing Download...
                </>
              ) : (
                <>
                  <Download className="h-5 w-5" aria-hidden="true" />
                  Download My Fixed File
                </>
              )}
            </button>
          )}
          {onRepairFile && hasIssues && !isRepairedFile && repairReport && repairReport.sections.some(section => section.issues.length > 0) && (
            <button
              onClick={() => {
                if (!isRepairing) {
                  console.log('🔧 Fix My File button clicked!');
                  console.log('📄 File to repair:', originalFile.name);
                  console.log('📋 onRepairFile function:', typeof onRepairFile);
                  onRepairFile(originalFile);
                }
              }}
              disabled={isRepairing}
              aria-label={isRepairing ? 'Repairing file in progress' : `Repair Excel file ${originalFile.name}`}
              className={`flex-1 font-bold py-3 px-6 rounded-lg flex items-center justify-center gap-2 transition duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-lime-500 ${
                isRepairing
                  ? 'bg-gray-400 cursor-not-allowed text-white'
                  : 'bg-lime-300 hover:bg-lime-400 text-gray-900'
              }`}
            >
              {isRepairing ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" aria-hidden="true" />
                  Fixing Your File...
                </>
              ) : (
                <>
                  <Zap className="h-5 w-5" aria-hidden="true" />
                  Fix My File
                </>
              )}
            </button>
          )}
          {onAnalyzeAnother && (
            <button
              onClick={onAnalyzeAnother}
              aria-label="Upload and analyze another Excel file"
              className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-3 px-6 rounded-lg flex items-center justify-center gap-2 transition duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <FileText className="h-5 w-5" aria-hidden="true" /> Check Another File
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
