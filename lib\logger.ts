/**
 * Comprehensive logging system for SheetHealer
 * Provides structured logging with different levels, contexts, and output targets
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';
export type LogContext = 'file-upload' | 'file-repair' | 'api' | 'ui' | 'security' | 'performance' | 'user' | 'system' | 'analytics';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  context: LogContext;
  message: string;
  data?: Record<string, any>;
  userId?: string | undefined;
  sessionId?: string;
  requestId?: string;
  error?: Error;
  stack?: string | undefined;
}

interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  enableLocalStorage: boolean;
  maxLocalStorageEntries: number;
  remoteEndpoint?: string | undefined;
  sessionId: string;
  userId?: string | undefined;
}

class Logger {
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private readonly LOG_LEVELS: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4
  };

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: 'info',
      enableConsole: true,
      enableRemote: false,
      enableLocalStorage: true,
      maxLocalStorageEntries: 1000,
      sessionId: this.generateSessionId(),
      ...config
    };

    // Initialize session
    this.info('system', 'Logger initialized', { config: this.config });
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    return this.LOG_LEVELS[level] >= this.LOG_LEVELS[this.config.level];
  }

  private createLogEntry(
    level: LogLevel,
    context: LogContext,
    message: string,
    data?: Record<string, any>,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      context,
      message,
      sessionId: this.config.sessionId,
      userId: this.config.userId,
    };

    if (data) {
      entry.data = this.sanitizeData(data);
    }

    if (error) {
      entry.error = error;
      entry.stack = error.stack;
    }

    return entry;
  }

  private sanitizeData(data: Record<string, any>): Record<string, any> {
    const sanitized = { ...data };

    // Remove sensitive information
    const sensitiveKeys = ['password', 'token', 'apiKey', 'secret', 'fileData'];
    sensitiveKeys.forEach(key => {
      if (key in sanitized) {
        sanitized[key] = '[REDACTED]';
      }
    });

    // Truncate large strings
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'string' && sanitized[key].length > 1000) {
        sanitized[key] = sanitized[key].substring(0, 1000) + '... [TRUNCATED]';
      }
    });

    return sanitized;
  }

  private outputToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return;

    const emoji = this.getLogEmoji(entry.level, entry.context);
    const prefix = `${emoji} [${entry.level.toUpperCase()}] [${entry.context}]`;
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();

    const logMethod = entry.level === 'error' || entry.level === 'fatal' ? console.error :
                     entry.level === 'warn' ? console.warn :
                     entry.level === 'debug' ? console.debug :
                     console.log;

    if (entry.error) {
      logMethod(`${prefix} ${timestamp} - ${entry.message}`, entry.data || {}, entry.error);
    } else {
      logMethod(`${prefix} ${timestamp} - ${entry.message}`, entry.data || {});
    }
  }

  private getLogEmoji(level: LogLevel, context: LogContext): string {
    const contextEmojis: Record<LogContext, string> = {
      'file-upload': '📤',
      'file-repair': '🔧',
      'api': '🌐',
      'ui': '🎨',
      'security': '🔒',
      'performance': '⚡',
      'user': '👤',
      'system': '⚙️',
      'analytics': '📊'
    };

    const levelEmojis: Record<LogLevel, string> = {
      debug: '🐛',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌',
      fatal: '💀'
    };

    return `${contextEmojis[context]} ${levelEmojis[level]}`;
  }

  private saveToLocalStorage(entry: LogEntry): void {
    if (!this.config.enableLocalStorage || typeof window === 'undefined') return;

    try {
      const storageKey = 'sheethealer_logs';
      const existingLogs = JSON.parse(localStorage.getItem(storageKey) || '[]');

      existingLogs.push(entry);

      // Keep only the most recent entries
      if (existingLogs.length > this.config.maxLocalStorageEntries) {
        existingLogs.splice(0, existingLogs.length - this.config.maxLocalStorageEntries);
      }

      localStorage.setItem(storageKey, JSON.stringify(existingLogs));
    } catch (error) {
      console.warn('Failed to save log to localStorage:', error);
    }
  }

  private async sendToRemote(entry: LogEntry): Promise<void> {
    if (!this.config.enableRemote || !this.config.remoteEndpoint) return;

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      });
    } catch (error) {
      console.warn('Failed to send log to remote endpoint:', error);
    }
  }

  private log(level: LogLevel, context: LogContext, message: string, data?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(level)) return;

    const entry = this.createLogEntry(level, context, message, data, error);

    // Add to buffer
    this.logBuffer.push(entry);

    // Output to different targets
    this.outputToConsole(entry);
    this.saveToLocalStorage(entry);

    if (this.config.enableRemote) {
      this.sendToRemote(entry).catch(() => {
        // Silently fail remote logging to avoid infinite loops
      });
    }

    // Keep buffer size manageable
    if (this.logBuffer.length > 100) {
      this.logBuffer.shift();
    }
  }

  // Public logging methods
  debug(context: LogContext, message: string, data?: Record<string, any>): void {
    this.log('debug', context, message, data);
  }

  info(context: LogContext, message: string, data?: Record<string, any>): void {
    this.log('info', context, message, data);
  }

  warn(context: LogContext, message: string, data?: Record<string, any>): void {
    this.log('warn', context, message, data);
  }

  error(context: LogContext, message: string, error?: Error, data?: Record<string, any>): void {
    this.log('error', context, message, data, error);
  }

  fatal(context: LogContext, message: string, error?: Error, data?: Record<string, any>): void {
    this.log('fatal', context, message, data, error);
  }

  // Specialized logging methods
  fileUploadStart(fileName: string, fileSize: number): void {
    this.info('file-upload', 'File upload started', {
      fileName,
      fileSize,
      fileSizeMB: (fileSize / 1024 / 1024).toFixed(2)
    });
  }

  fileUploadComplete(fileName: string, duration: number): void {
    this.info('file-upload', 'File upload completed', {
      fileName,
      duration,
      durationSeconds: (duration / 1000).toFixed(2)
    });
  }

  fileUploadError(fileName: string, error: Error): void {
    this.error('file-upload', 'File upload failed', error, { fileName });
  }

  fileRepairStart(fileName: string, operation: 'analyze' | 'repair'): void {
    this.info('file-repair', `File ${operation} started`, { fileName, operation });
  }

  fileRepairComplete(fileName: string, operation: 'analyze' | 'repair', result: any): void {
    this.info('file-repair', `File ${operation} completed`, {
      fileName,
      operation,
      success: result.success,
      issuesFound: result.originalIssues,
      issuesFixed: result.repairedIssues
    });
  }

  fileRepairError(fileName: string, operation: 'analyze' | 'repair', error: Error): void {
    this.error('file-repair', `File ${operation} failed`, error, { fileName, operation });
  }

  apiRequest(method: string, url: string, duration?: number): void {
    this.info('api', 'API request', {
      method,
      url,
      duration: duration ? `${duration}ms` : undefined
    });
  }

  apiError(method: string, url: string, error: Error, statusCode?: number): void {
    this.error('api', 'API request failed', error, {
      method,
      url,
      statusCode
    });
  }

  userAction(action: string, data?: Record<string, any>): void {
    this.info('user', `User action: ${action}`, data);
  }

  securityEvent(event: string, data?: Record<string, any>): void {
    this.warn('security', `Security event: ${event}`, data);
  }

  performanceMetric(metric: string, value: number, unit: string = 'ms'): void {
    this.info('performance', `Performance metric: ${metric}`, {
      metric,
      value,
      unit,
      formattedValue: `${value}${unit}`
    });
  }

  // Utility methods
  setUserId(userId: string): void {
    this.config.userId = userId;
    this.info('system', 'User ID set', { userId });
  }

  setLogLevel(level: LogLevel): void {
    const oldLevel = this.config.level;
    this.config.level = level;
    this.info('system', 'Log level changed', { oldLevel, newLevel: level });
  }

  enableRemoteLogging(endpoint: string): void {
    this.config.enableRemote = true;
    this.config.remoteEndpoint = endpoint;
    this.info('system', 'Remote logging enabled', { endpoint });
  }

  disableRemoteLogging(): void {
    this.config.enableRemote = false;
    this.info('system', 'Remote logging disabled');
  }

  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  exportLogs(): string {
    const logs = this.getRecentLogs(1000);
    return JSON.stringify(logs, null, 2);
  }

  clearLogs(): void {
    this.logBuffer = [];
    if (typeof window !== 'undefined') {
      localStorage.removeItem('sheethealer_logs');
    }
    this.info('system', 'Logs cleared');
  }
}

// Create singleton instance
const loggerConfig: any = {
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  enableConsole: true,
  enableLocalStorage: true,
  enableRemote: process.env.NODE_ENV === 'production'
};

if (process.env.NODE_ENV === 'production') {
  loggerConfig.remoteEndpoint = '/api/logs';
}

const logger = new Logger(loggerConfig);

export default logger;

// Export convenience functions
export const log = {
  debug: (context: LogContext, message: string, data?: Record<string, any>) =>
    logger.debug(context, message, data),

  info: (context: LogContext, message: string, data?: Record<string, any>) =>
    logger.info(context, message, data),

  warn: (context: LogContext, message: string, data?: Record<string, any>) =>
    logger.warn(context, message, data),

  error: (context: LogContext, message: string, error?: Error, data?: Record<string, any>) =>
    logger.error(context, message, error, data),

  fatal: (context: LogContext, message: string, error?: Error, data?: Record<string, any>) =>
    logger.fatal(context, message, error, data),

  // Specialized methods
  fileUpload: {
    start: (fileName: string, fileSize: number) => logger.fileUploadStart(fileName, fileSize),
    complete: (fileName: string, duration: number) => logger.fileUploadComplete(fileName, duration),
    error: (fileName: string, error: Error) => logger.fileUploadError(fileName, error)
  },

  fileRepair: {
    start: (fileName: string, operation: 'analyze' | 'repair') => logger.fileRepairStart(fileName, operation),
    complete: (fileName: string, operation: 'analyze' | 'repair', result: any) => logger.fileRepairComplete(fileName, operation, result),
    error: (fileName: string, operation: 'analyze' | 'repair', error: Error) => logger.fileRepairError(fileName, operation, error)
  },

  api: {
    request: (method: string, url: string, duration?: number) => logger.apiRequest(method, url, duration),
    error: (method: string, url: string, error: Error, statusCode?: number) => logger.apiError(method, url, error, statusCode)
  },

  user: (action: string, data?: Record<string, any>) => logger.userAction(action, data),
  security: (event: string, data?: Record<string, any>) => logger.securityEvent(event, data),
  performance: (metric: string, value: number, unit?: string) => logger.performanceMetric(metric, value, unit)
};

export { logger };