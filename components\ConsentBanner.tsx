import React from 'react';
import CookieConsent from 'react-cookie-consent';
import ReactGA from 'react-ga4';

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: unknown[]) => void;
  }
}

const GA_MEASUREMENT_ID = 'G-LJ1XHVGTSW';

const handleAccept = () => {
  ReactGA.initialize(GA_MEASUREMENT_ID);
  
  // Update consent mode to granted when user accepts
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('consent', 'update', {
      'analytics_storage': 'granted',
      'ad_storage': 'granted',
      'ad_user_data': 'granted',
      'ad_personalization': 'granted'
    });
  }
};

const ConsentBanner: React.FC = () => {
  return (
    <CookieConsent
      location="bottom"
      buttonText="Accept"
      cookieName="sheethealer-cookie-consent"
      style={{ background: '#2B373B' }}
      buttonStyle={{ color: '#4e503b', fontSize: '13px' }}
      expires={150}
      onAccept={handleAccept}
    >
      This website uses cookies to enhance the user experience and for analytics purposes. By clicking &quot;Accept&quot;, you consent to the use of cookies.
    </CookieConsent>
  );
};

export default ConsentBanner; 