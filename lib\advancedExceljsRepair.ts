import * as ExcelJS from 'exceljs';

interface LogEntry {
  level: 'info' | 'warning' | 'error';
  message: string;
  timestamp: Date;
}

interface Signature {
  signature: Uint8Array;
  position: number;
}

interface SheetData {
  name: string;
  data: (string | string[])[];
  cellCount: number;
}

interface RecoveredData {
  sheets: SheetData[];
}

interface RepairResult {
  success: boolean;
  repairedWorkbook?: ExcelJS.Workbook;
  recoveredData?: RecoveredData;
  repairLog: LogEntry[];
  originalError?: string;
}

export class AdvancedExcelRepairService {
  private static log: LogEntry[] = [];

  private static addLog(level: 'info' | 'warning' | 'error', message: string): void {
    this.log.push({ level, message, timestamp: new Date() });
    console.log(`🔧 [ADVANCED REPAIR ${level.toUpperCase()}] ${message}`);
  }

  static async repairCorruptedExcel(file: File): Promise<RepairResult> {
    this.log = [];
    this.addLog('info', `Starting advanced repair for ${file.name} (${file.size} bytes)`);

    try {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      const zipRepairResult = await this.attemptZipRepair(uint8Array);
      if (zipRepairResult.success) {
        return zipRepairResult;
      }

      const signatureResult = await this.scanForExcelSignatures(uint8Array);
      if (signatureResult.success) {
        return signatureResult;
      }

      const xmlExtractionResult = await this.extractRawXmlData(uint8Array);
      if (xmlExtractionResult.success) {
        return xmlExtractionResult;
      }

      const bruteForceResult = await this.bruteForceDataRecovery(uint8Array);
      if (bruteForceResult.success) {
        return bruteForceResult;
      }

      this.addLog('error', 'All advanced repair methods failed');
      return {
        success: false,
        repairLog: this.log,
        originalError: 'File is too severely corrupted for recovery'
      };

    } catch (error) {
      this.addLog('error', 'Advanced repair failed due to an unexpected error.');
      return {
        success: false,
        repairLog: this.log,
        originalError: 'An unexpected error occurred during advanced repair.'
      };
    }
  }

  private static async attemptZipRepair(data: Uint8Array): Promise<RepairResult> {
    this.addLog('info', 'Scanning for ZIP signatures...');

    const LOCAL_FILE_HEADER = new Uint8Array([0x50, 0x4B, 0x03, 0x04]);
    const CENTRAL_DIR_HEADER = new Uint8Array([0x50, 0x4B, 0x01, 0x02]);
    const END_CENTRAL_DIR = new Uint8Array([0x50, 0x4B, 0x05, 0x06]);

    const signatures = this.findSignatures(data, [LOCAL_FILE_HEADER, CENTRAL_DIR_HEADER, END_CENTRAL_DIR]);

    if (signatures.length === 0) {
      this.addLog('warning', 'No ZIP signatures found');
      return { success: false, repairLog: this.log };
    }

    this.addLog('info', `Found ${signatures.length} ZIP signatures at positions: ${signatures.map(s => s.position).join(', ')}`);

    try {
      const repairedZip = this.reconstructZipFromSignatures(data, signatures);
      if (repairedZip) {
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(repairedZip);
        this.addLog('info', 'Successfully reconstructed ZIP and parsed Excel file');
        return {
          success: true,
          repairedWorkbook: workbook,
          repairLog: this.log
        };
      }
    } catch (error) {
      this.addLog('warning', 'ZIP reconstruction failed due to an unexpected error.');
    }

    return { success: false, repairLog: this.log };
  }

  private static async scanForExcelSignatures(data: Uint8Array): Promise<RepairResult> {
    this.addLog('info', 'Scanning for Excel XML signatures...');

    const dataStr = new TextDecoder('utf-8', { fatal: false }).decode(data);

    const patterns = [
      /<worksheet[^>]*>/gi,
      /<sheetData[^>]*>/gi,
      /<row[^>]*>/gi,
      /<c[^>]*>/gi,
      /<v[^>]*>/gi,
      /<workbook[^>]*>/gi
    ];

    const foundPatterns = patterns.map(pattern => {
      const matches = dataStr.match(pattern);
      return { pattern: pattern.source, count: matches ? matches.length : 0 };
    });

    const totalMatches = foundPatterns.reduce((sum, p) => sum + p.count, 0);

    if (totalMatches === 0) {
      this.addLog('warning', 'No Excel XML patterns found');
      return { success: false, repairLog: this.log };
    }

    this.addLog('info', `Found ${totalMatches} Excel XML patterns: ${foundPatterns.map(p => `${p.pattern}(${p.count})`).join(', ')}`);

    try {
      const extractedData = this.extractXmlData(dataStr);
      if (extractedData.sheets.length > 0) {
        this.addLog('info', `Extracted data from ${extractedData.sheets.length} sheets`);
        const repairedWorkbook = await this.createWorkbookFromRecoveredData(extractedData);
        return {
          success: true,
          repairedWorkbook: repairedWorkbook,
          recoveredData: extractedData,
          repairLog: this.log
        };
      }
    } catch (error) {
      this.addLog('warning', 'XML extraction failed due to an unexpected error.');
    }

    return { success: false, repairLog: this.log };
  }

  private static async extractRawXmlData(data: Uint8Array): Promise<RepairResult> {
    this.addLog('info', 'Attempting raw XML data extraction...');

    try {
      let dataStr = '';
      for (let i = 0; i < data.length; i++) {
        const byte = data[i];
        if ((byte >= 32 && byte <= 126) || byte === 9 || byte === 10 || byte === 13) {
          dataStr += String.fromCharCode(byte);
        } else if (byte < 32) {
          dataStr += ' ';
        }
      }

      const csvLikeData = this.extractCsvLikeData(dataStr);
      if (csvLikeData.length > 0) {
        this.addLog('info', `Extracted ${csvLikeData.length} rows of CSV-like data`);
        const recoveredData: RecoveredData = {
          sheets: [{
            name: 'Recovered Data',
            data: csvLikeData,
            cellCount: csvLikeData.length * (csvLikeData[0]?.length || 0)
          }]
        };
        const repairedWorkbook = await this.createWorkbookFromRecoveredData(recoveredData);
        return {
          success: true,
          repairedWorkbook: repairedWorkbook,
          recoveredData: recoveredData,
          repairLog: this.log
        };
      }

    } catch (error) {
      this.addLog('warning', 'Raw XML extraction failed due to an unexpected error.');
    }

    return { success: false, repairLog: this.log };
  }

  private static async bruteForceDataRecovery(data: Uint8Array): Promise<RepairResult> {
    this.addLog('info', 'Attempting brute force data recovery...');

    try {
      const textData = this.extractAnyTextData(data);

      if (textData.length > 0) {
        this.addLog('info', `Recovered ${textData.length} text fragments`);
        const recoveredData: RecoveredData = {
          sheets: [{
            name: 'Recovered_Text',
            data: textData.map(text => [text || 'Recovered data fragment']),
            cellCount: textData.length
          }]
        };
        const repairedWorkbook = await this.createWorkbookFromRecoveredData(recoveredData);
        return {
          success: true,
          repairedWorkbook: repairedWorkbook,
          recoveredData: recoveredData,
          repairLog: this.log
        };
      }

      this.addLog('info', 'No recoverable text found, creating recovery report');
      const fileSize = data.length;
      const recoveryReport: string[][] = [
        ['Excel File Recovery Report'],
        [''],
        ['Status', 'File was severely corrupted'],
        ['Original Size', `${(fileSize / 1024).toFixed(2)} KB`],
        ['Recovery Method', 'Advanced binary analysis'],
        ['Data Recovery', 'No readable content could be extracted'],
        [''],
        ['Recommendations'],
        ['1. Try uploading the original file if available'],
        ['2. Check if file was completely overwritten'],
        ['3. Consider professional data recovery services'],
        [''],
        ['Note', 'This file has been converted to a working Excel format'],
        ['', 'You can now add your own data to replace the corrupted content']
      ];

      const recoveredData: RecoveredData = {
        sheets: [{
          name: 'Recovery_Report',
          data: recoveryReport,
          cellCount: recoveryReport.length * 2
        }]
      };
      const repairedWorkbook = await this.createWorkbookFromRecoveredData(recoveredData);
      return {
        success: true,
        repairedWorkbook: repairedWorkbook,
        recoveredData: recoveredData,
        repairLog: this.log
      };

    } catch (error) {
      this.addLog('warning', 'Brute force recovery failed due to an unexpected error.');
    }

    return { success: false, repairLog: this.log };
  }

  private static findSignatures(data: Uint8Array, signatures: Uint8Array[]): Signature[] {
    const found: Signature[] = [];

    for (const signature of signatures) {
      for (let i = 0; i <= data.length - signature.length; i++) {
        let match = true;
        for (let j = 0; j < signature.length; j++) {
          if (data[i + j] !== signature[j]) {
            match = false;
            break;
          }
        }
        if (match) {
          found.push({ signature, position: i });
        }
      }
    }

    return found.sort((a, b) => a.position - b.position);
  }

  private static reconstructZipFromSignatures(data: Uint8Array, signatures: Signature[]): Uint8Array | null {
    if (signatures.length === 0) return null;

    const firstSig = signatures[0];
    const lastSig = signatures[signatures.length - 1];

    const start = firstSig.position;
    const end = Math.min(lastSig.position + 1024, data.length);

    return data.slice(start, end);
  }

  private static extractXmlData(dataStr: string): RecoveredData {
    const sheets: SheetData[] = [];

    const worksheetMatches = dataStr.match(/<worksheet[^>]*>[\s\S]*?<\/worksheet>/gi);

    if (worksheetMatches) {
      worksheetMatches.forEach((worksheet, index) => {
        const data = this.parseWorksheetXml(worksheet);
        if (data.length > 0) {
          sheets.push({
            name: `Sheet${index + 1}`,
            data,
            cellCount: data.length * (data[0]?.length || 0)
          });
        }
      });
    }

    return { sheets };
  }

  private static parseWorksheetXml(worksheetXml: string): string[][] {
    const rows: string[][] = [];

    const rowMatches = worksheetXml.match(/<row[^>]*>[\s\S]*?<\/row>/gi);

    if (rowMatches) {
      rowMatches.forEach(rowXml => {
        const cellMatches = rowXml.match(/<c[^>]*>[\s\S]*?<\/c>/gi);
        const rowData: string[] = [];

        if (cellMatches) {
          cellMatches.forEach(cellXml => {
            const valueMatch = cellXml.match(/<v[^>]*>(.*?)<\/v>/i);
            if (valueMatch) {
              rowData.push(valueMatch[1]);
            }
          });
        }

        if (rowData.length > 0) {
          rows.push(rowData);
        }
      });
    }

    return rows;
  }

  private static extractCsvLikeData(text: string): string[][] {
    const lines = text.split(/[\r\n]+/);
    const data: string[][] = [];

    for (const line of lines) {
      if (line.trim().length === 0) continue;

      let values: string[] = [];

      if (line.includes('\t')) {
        values = line.split('\t');
      } else if (line.includes(',')) {
        values = line.split(',');
      } else if (line.includes(';')) {
        values = line.split(';');
      } else {
        values = [line.trim()];
      }

      const cleanValues = values
        .map(v => v.trim())
        .filter(v => v.length > 0 && /^[a-zA-Z0-9\s\-_.]+$/.test(v));

      if (cleanValues.length > 0) {
        data.push(cleanValues);
      }
    }

    return data.slice(0, 100);
  }

  private static extractAnyTextData(data: Uint8Array): string[] {
    const textFragments: string[] = [];
    let currentFragment = '';

    for (let i = 0; i < data.length; i++) {
      const byte = data[i];

      if (byte >= 32 && byte <= 126) {
        currentFragment += String.fromCharCode(byte);
      } else {
        if (currentFragment.length >= 3) {
          textFragments.push(currentFragment.trim());
        }
        currentFragment = '';
      }
    }

    if (currentFragment.length >= 3) {
      textFragments.push(currentFragment.trim());
    }

    return textFragments
      .filter(fragment =>
        fragment &&
        fragment.length >= 3 &&
        /[a-zA-Z0-9]/.test(fragment) &&
        !/^[\x00-\x1F\x7F-\xFF]+$/.test(fragment)
      )
      .map(fragment => fragment || 'Text fragment')
      .slice(0, 50);
  }

  private static async createWorkbookFromRecoveredData(recoveredData: RecoveredData): Promise<ExcelJS.Workbook> {
    const workbook = new ExcelJS.Workbook();
    
    for (const sheetData of recoveredData.sheets) {
      const worksheet = workbook.addWorksheet(sheetData.name);
      
      // Add the recovered data to the worksheet
      if (sheetData.data && sheetData.data.length > 0) {
        sheetData.data.forEach((row, rowIndex) => {
          if (Array.isArray(row)) {
            row.forEach((cellValue, colIndex) => {
              try {
                worksheet.getCell(rowIndex + 1, colIndex + 1).value = cellValue || '';
              } catch (error) {
                console.warn(`Failed to set cell value at row ${rowIndex + 1}, col ${colIndex + 1}:`, error);
              }
            });
          } else {
            // Single value row
            try {
              worksheet.getCell(rowIndex + 1, 1).value = row || '';
            } catch (error) {
              console.warn(`Failed to set cell value at row ${rowIndex + 1}, col 1:`, error);
            }
          }
        });
      }
    }
    
    return workbook;
  }

  static generateAdvancedRepairSummary(result: RepairResult): string {
    if (!result.success) {
      return `Advanced repair failed: ${result.originalError || 'Unknown error'}`;
    }

    if (result.repairedWorkbook) {
      const sheetCount = result.repairedWorkbook.worksheets.length;
      return `Successfully repaired Excel file with ${sheetCount} sheet(s) using ZIP reconstruction`;
    }

    if (result.recoveredData) {
      const totalCells = result.recoveredData.sheets.reduce((sum, sheet) => sum + sheet.cellCount, 0);
      const sheetCount = result.recoveredData.sheets.length;
      return `Partially recovered data: ${sheetCount} sheet(s) with ${totalCells} cells using advanced extraction`;
    }

    return 'Advanced repair completed but no data was recovered';
  }
}