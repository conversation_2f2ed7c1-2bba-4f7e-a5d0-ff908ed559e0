import { VercelRequest, VercelResponse } from '@vercel/node';
import * as ExcelJS from 'exceljs';
import { ExcelRepairService } from '../lib/exceljsRepair';
import { AdvancedExcelRepairService } from '../lib/advancedExceljsRepair';
import { RepairReport } from '../types';
import {
  FileUploadSchema,
  validateInput,
  validateInputWithDetails,
  FileProcessingSuccessSchema,
  FileProcessingErrorSchema,
  RepairReportSchema,
  isValidRepairReport
} from '../lib/validation';
import { rateLimitMiddleware, RATE_LIMITS, detectSuspiciousActivity } from '../lib/rateLimiter';
import { validateOrigin } from '../lib/csrf';

// Increase the max body size for file uploads
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
};

interface ProcessorRequest {
  fileData: string; // Base64 encoded file data
  fileName: string;
  fileSize: number;
  operation: 'analyze' | 'repair'; // Unified operation type
}

interface ProcessorResponse {
  success: boolean;
  repairedFileData?: string;
  originalFileName?: string;
  repairedFileName?: string;
  repairSummary?: RepairReport;
  error?: string;
  details?: any;
}

export default async function handler(
  req: VercelRequest,
  res: VercelResponse
): Promise<void> {
  // Enable CORS - Support for development and production
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    res.status(405).json({ success: false, error: 'Method not allowed' });
    return;
  }

  // SECURITY: Rate limiting
  const rateLimitCheck = rateLimitMiddleware('excel-processor', RATE_LIMITS.FILE_PROCESSING);
  if (!rateLimitCheck(req, res)) {
    return; // Rate limit exceeded, response already sent
  }

  // SECURITY: Detect suspicious activity
  if (detectSuspiciousActivity(req)) {
    res.status(403).json({
      success: false,
      error: 'Suspicious activity detected'
    });
    return;
  }

  // SECURITY: Origin validation (basic CSRF protection)
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'https://sheethealer.com',
    'https://www.sheethealer.com'
  ];

  if (!validateOrigin(req, allowedOrigins)) {
    res.status(403).json({
      success: false,
      error: 'Invalid origin - request not allowed from this domain'
    });
    return;
  }

  try {
    // SECURITY: Validate input using enhanced Zod schema
    const validation = validateInputWithDetails(FileUploadSchema, req.body);

    if (!validation.success) {
      const errorResponse = {
        success: false as const,
        error: 'Invalid input data',
        details: validation.error
      };

      // Validate error response against schema
      const errorValidation = FileProcessingErrorSchema.safeParse(errorResponse);
      if (!errorValidation.success) {
        console.error('Error response validation failed:', errorValidation.error);
      }

      res.status(400).json(errorResponse);
      return;
    }

    const validatedData = validation.data;
    const { fileData, fileName, fileSize, operation = 'analyze' } = validatedData;

    // Additional security checks
    if (fileSize > 50 * 1024 * 1024) {
      res.status(400).json({
        success: false,
        error: 'File size exceeds 50MB limit'
      });
      return;
    }

    console.log(`🔄 Starting ${operation} operation for file:`, fileName);

    // Convert base64 to Buffer
    const buffer = Buffer.from(fileData, 'base64');

    // Try to read the workbook
    let workbook: ExcelJS.Workbook | null = null;
    let parseError: string | null = null;

    console.log('📖 Reading Excel workbook...');
    try {
      workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(buffer);
      console.log('✅ Successfully read Excel workbook');
    } catch (error) {
      const originalError = error instanceof Error ? error.message : 'Unknown parse error';
      console.error('❌ Failed to parse Excel file:', originalError);

      // Convert technical errors to user-friendly messages
      if (originalError.includes('cellContainer') || originalError.includes('Cannot read properties of undefined')) {
        parseError = 'File appears to be corrupted or damaged. We can try to repair it.';
      } else if (originalError.includes('zip') || originalError.includes('archive')) {
        parseError = 'File format appears to be corrupted. We can attempt to recover the data.';
      } else if (originalError.includes('XML') || originalError.includes('parse')) {
        parseError = 'File contains corrupted data structures. We can try to fix them.';
      } else if (originalError.includes('Invalid file signature') || originalError.includes('not a valid')) {
        parseError = 'File format is not recognized as a valid Excel file.';
      } else {
        parseError = 'File has structural issues that we can attempt to repair.';
      }
    }

    // Create a File object for repair services
    const file = new File([buffer], fileName, { type: 'application/octet-stream' });

    let result: any;
    let repairedBase64 = '';

    if (operation === 'analyze') {
      console.log('🔍 Analysis mode: Checking for problems without repairing...');
      result = await ExcelRepairService.analyzeWorkbook(workbook, parseError ? { initialParseError: parseError } : {}, file);

      // For analysis, we still return the original workbook if it exists
      if (workbook) {
        result.repairedWorkbook = workbook;
      }
    } else {
      console.log('🔧 Repair mode: Checking and repairing problems...');
      result = await performRepairWithFallback(workbook, parseError, file);
    }

    if (!result.success) {
      const errorSummary = createErrorSummary(result, operation);

      // Validate repair report
      if (!isValidRepairReport(errorSummary)) {
        console.error('Invalid repair report generated:', errorSummary);
      }

      const errorResponse = {
        success: false as const,
        error: errorSummary.summary,
        repairSummary: errorSummary
      };

      // Validate error response against schema
      const errorValidation = FileProcessingErrorSchema.safeParse(errorResponse);
      if (!errorValidation.success) {
        console.error('Error response validation failed:', errorValidation.error);
      }

      res.status(500).json(errorResponse);
      return;
    }

    // Generate repaired file data for successful operations
    if (result.repairedWorkbook && operation === 'repair') {
      console.log('💾 Writing repaired Excel file...');
      const repairedBuffer = await result.repairedWorkbook.xlsx.writeBuffer();
      console.log('✅ Successfully wrote repaired Excel file');
      repairedBase64 = Buffer.from(repairedBuffer).toString('base64');
    }

    // Create success summary
    const repairSummary = createSuccessSummary(result, operation);

    // Validate repair report
    if (!isValidRepairReport(repairSummary)) {
      console.error('Invalid repair report generated:', repairSummary);
    }

    const response: ProcessorResponse = {
      success: true,
      repairSummary
    };

    // Add file data for repair operations
    if (operation === 'repair' && repairedBase64) {
      response.repairedFileData = repairedBase64;
      response.originalFileName = fileName;
      response.repairedFileName = fileName.replace(/\.xlsx?$/i, '_repaired.xlsx');
    }

    // Validate success response against schema
    const successValidation = FileProcessingSuccessSchema.safeParse(response);
    if (!successValidation.success) {
      console.error('Success response validation failed:', successValidation.error);
    }

    console.log(`🎉 ${operation} operation completed successfully`);
    res.status(200).json(response);

  } catch (error: unknown) {
    console.error(`❌ Excel ${req.body?.operation || 'processing'} error:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown internal server error';
    const errorSummary: RepairReport = {
      summary: `Internal server error: ${errorMessage}`,
      sections: [
        {
          title: 'Server Error',
          issues: [
            {
              type: 'Internal Error',
              description: errorMessage,
              actionTaken: 'Please try again or contact support.',
              severity: 'Critical'
            }
          ]
        }
      ],
      performanceMetrics: {
        analysisTime: 'N/A',
        repairTime: 'N/A'
      }
    };

    // Validate repair report
    if (!isValidRepairReport(errorSummary)) {
      console.error('Invalid error repair report generated:', errorSummary);
    }

    const errorResponse = {
      success: false as const,
      error: errorSummary.summary,
      repairSummary: errorSummary
    };

    // Validate error response against schema
    const errorValidation = FileProcessingErrorSchema.safeParse(errorResponse);
    if (!errorValidation.success) {
      console.error('Error response validation failed:', errorValidation.error);
    }

    res.status(500).json(errorResponse);
  }
}

/**
 * Performs repair with fallback to advanced repair if standard repair fails
 */
async function performRepairWithFallback(
  workbook: ExcelJS.Workbook | null,
  parseError: string | null,
  file: File
): Promise<any> {
  console.log('🔧 Running standard repair algorithms...');

  // Try standard repair first
  const standardResult = await ExcelRepairService.repairWorkbook(
    workbook,
    parseError ? { initialParseError: parseError } : {},
    file
  );

  if (standardResult.success && standardResult.repairedWorkbook) {
    console.log('✅ Standard repair succeeded');
    return standardResult;
  }

  console.log('❌ Standard repair failed, trying advanced repair...');

  // Try advanced repair as fallback
  try {
    const advancedResult = await AdvancedExcelRepairService.repairCorruptedExcel(file);

    if (advancedResult.success && 'repairedWorkbook' in advancedResult && advancedResult.repairedWorkbook) {
      console.log('✅ Advanced repair succeeded');

      // Transform advanced result to match standard result format
      return {
        success: true,
        repairedWorkbook: advancedResult.repairedWorkbook,
        repairLog: [
          {
            issue: 'Advanced Repair Applied',
            action: 'File structure rebuilt and data recovered using advanced techniques',
            severity: 'high',
            sheet: 'Entire file'
          }
        ],
        originalIssues: 1,
        repairedIssues: 1
      };
    }
  } catch (advancedError) {
    console.error('❌ Advanced repair also failed:', advancedError);
  }

  // Both repairs failed, return the standard result with error info
  return standardResult;
}

/**
 * Creates an error summary for failed operations
 */
function createErrorSummary(result: any, operation: string): RepairReport {
  const operationName = operation === 'analyze' ? 'Analysis' : 'Repair';

  return {
    summary: result.repairLog?.length > 0
      ? result.repairLog[result.repairLog.length - 1].issue
      : `${operationName} process failed`,
    sections: [
      {
        title: `${operationName} Log`,
        issues: (result.repairLog || []).map((log: any) => ({
          type: log.issue,
          location: log.cell || log.sheet || 'Unknown',
          description: log.issue,
          actionTaken: log.action,
          severity: log.severity === 'high' ? 'Critical' : log.severity === 'medium' ? 'Major' : 'Minor'
        }))
      }
    ],
    performanceMetrics: {
      analysisTime: 'N/A',
      repairTime: 'N/A'
    }
  };
}

/**
 * Creates a success summary for successful operations
 */
function createSuccessSummary(result: any, operation: string): RepairReport {
  const operationName = operation === 'analyze' ? 'Analysis' : 'Repair';
  const successRate = result.originalIssues > 0
    ? Math.round((result.repairedIssues / result.originalIssues) * 100)
    : 100;

  let summary: string;
  if (operation === 'analyze') {
    summary = result.repairLog?.length > 0
      ? `Analysis completed. Found ${result.repairLog.length} issue${result.repairLog.length !== 1 ? 's' : ''} that can be repaired.`
      : 'Analysis completed. Your file appears to be healthy with no issues detected.';
  } else {
    summary = `Repair completed with ${successRate}% success rate.`;
  }

  return {
    summary,
    sections: [
      {
        title: `${operationName} Results`,
        issues: (result.repairLog || []).map((log: any) => ({
          type: log.issue,
          location: log.cell || log.sheet || 'Unknown',
          description: log.issue,
          actionTaken: log.action,
          severity: log.severity === 'high' ? 'Critical' : log.severity === 'medium' ? 'Major' : 'Minor'
        }))
      }
    ],
    performanceMetrics: {
      analysisTime: 'N/A',
      repairTime: 'N/A'
    }
  };
}