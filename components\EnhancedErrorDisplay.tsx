import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle, 
  RefreshCw, 
  MessageCircle, 
  ChevronDown, 
  ChevronUp,
  ExternalLink,
  Copy,
  CheckCircle
} from 'lucide-react';
import { ErrorDetails } from '../lib/errorMessages';

interface EnhancedErrorDisplayProps {
  error: ErrorDetails & {
    displayMessage: string;
    fullMessage: string;
    actionableGuidance: string[];
    canRetry: boolean;
    canContact: boolean;
  };
  onRetry?: () => void;
  onContact?: () => void;
  className?: string;
  variant?: 'full' | 'compact' | 'inline';
}

export const EnhancedErrorDisplay: React.FC<EnhancedErrorDisplayProps> = ({
  error,
  onRetry,
  onContact,
  className = '',
  variant = 'full'
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [copiedTechnical, setCopiedTechnical] = useState(false);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'border-red-500 bg-red-50 text-red-900';
      case 'high': return 'border-red-400 bg-red-50 text-red-800';
      case 'medium': return 'border-orange-400 bg-orange-50 text-orange-800';
      case 'low': return 'border-yellow-400 bg-yellow-50 text-yellow-800';
      default: return 'border-gray-400 bg-gray-50 text-gray-800';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const copyTechnicalDetails = async () => {
    if (error.technicalDetails) {
      try {
        await navigator.clipboard.writeText(error.technicalDetails);
        setCopiedTechnical(true);
        setTimeout(() => setCopiedTechnical(false), 2000);
      } catch (err) {
        console.error('Failed to copy technical details:', err);
      }
    }
  };

  const renderInline = () => (
    <div className={`flex items-center gap-2 p-3 rounded-lg border ${getSeverityColor(error.severity)} ${className}`}>
      {getSeverityIcon(error.severity)}
      <span className="font-medium">{error.displayMessage}</span>
      {onRetry && error.canRetry && (
        <button
          onClick={onRetry}
          className="ml-auto flex items-center gap-1 px-2 py-1 text-sm bg-white rounded border hover:bg-gray-50 transition-colors"
        >
          <RefreshCw className="h-3 w-3" />
          Retry
        </button>
      )}
    </div>
  );

  const renderCompact = () => (
    <div className={`border rounded-lg ${getSeverityColor(error.severity)} ${className}`}>
      <div className="p-4">
        <div className="flex items-start gap-3">
          {getSeverityIcon(error.severity)}
          <div className="flex-1 min-w-0">
            <h4 className="font-semibold">{error.title}</h4>
            <p className="text-sm mt-1">{error.fullMessage}</p>
            
            {error.actionableGuidance.length > 0 && (
              <div className="mt-3">
                <p className="text-sm font-medium mb-2">What you can do:</p>
                <ul className="text-sm space-y-1">
                  {error.actionableGuidance.slice(0, 2).map((guidance, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      <span>{guidance}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        <div className="flex gap-2 mt-4">
          {onRetry && error.canRetry && (
            <button
              onClick={onRetry}
              className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </button>
          )}
          {onContact && error.canContact && (
            <button
              onClick={onContact}
              className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
            >
              <MessageCircle className="h-4 w-4" />
              Get Help
            </button>
          )}
        </div>
      </div>
    </div>
  );

  const renderFull = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`border rounded-xl ${getSeverityColor(error.severity)} ${className}`}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start gap-4 mb-4">
          <div className="flex-shrink-0 text-2xl">
            {error.icon}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-bold">{error.title}</h3>
            <p className="text-base mt-2">{error.fullMessage}</p>
          </div>
          {getSeverityIcon(error.severity)}
        </div>

        {/* Guidance Section */}
        {error.actionableGuidance.length > 0 && (
          <div className="mb-6">
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              💡 Here's what you can do:
            </h4>
            <div className="space-y-3">
              {error.actionableGuidance.map((guidance, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3 p-3 bg-white bg-opacity-50 rounded-lg"
                >
                  <span className="flex-shrink-0 w-6 h-6 bg-white rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </span>
                  <span className="text-sm">{guidance}</span>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Suggested Actions */}
        {error.suggestedActions && (
          <div className="mb-6">
            <h4 className="font-semibold mb-3">Recommended Actions:</h4>
            <div className="space-y-2">
              {error.suggestedActions.primary && (
                <div className="flex items-center gap-2 text-sm">
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                    Primary
                  </span>
                  <span>{error.suggestedActions.primary}</span>
                </div>
              )}
              {error.suggestedActions.secondary?.map((action, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                    Option
                  </span>
                  <span>{action}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3 mb-4">
          {onRetry && error.canRetry && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onRetry}
              className="flex items-center gap-2 px-4 py-2 bg-lime-500 hover:bg-lime-600 text-white rounded-lg font-medium transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </motion.button>
          )}
          
          {onContact && error.canContact && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onContact}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
            >
              <MessageCircle className="h-4 w-4" />
              Contact Support
            </motion.button>
          )}

          {error.helpUrl && (
            <motion.a
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              href={error.helpUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"
            >
              <ExternalLink className="h-4 w-4" />
              Learn More
            </motion.a>
          )}
        </div>

        {/* Technical Details (Collapsible) */}
        {error.technicalDetails && (
          <div className="border-t border-gray-200 pt-4">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center gap-2 text-sm font-medium hover:underline"
            >
              {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              Technical Details
            </button>
            
            <AnimatePresence>
              {showDetails && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-3"
                >
                  <div className="bg-gray-100 rounded-lg p-3 relative">
                    <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words">
                      {error.technicalDetails}
                    </pre>
                    <button
                      onClick={copyTechnicalDetails}
                      className="absolute top-2 right-2 p-1 hover:bg-gray-200 rounded transition-colors"
                      title="Copy technical details"
                    >
                      {copiedTechnical ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4 text-gray-600" />
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-600 mt-2">
                    Share these details with support for faster assistance.
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}

        {/* Error Metadata */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-4 text-xs text-gray-600">
            <span>Error Code: {error.code}</span>
            <span>Category: {error.category}</span>
            <span>Severity: {error.severity}</span>
          </div>
        </div>
      </div>
    </motion.div>
  );

  switch (variant) {
    case 'inline':
      return renderInline();
    case 'compact':
      return renderCompact();
    default:
      return renderFull();
  }
};

export default EnhancedErrorDisplay;