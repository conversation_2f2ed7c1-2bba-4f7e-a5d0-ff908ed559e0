import path from 'path';
import { defineConfig, loadEnv, Plugin } from 'vite';
import ExcelJS from 'exceljs';

// Import the actual repair services
async function importRepairServices() {
  try {
    const { ExcelRepairService } = await import('./lib/exceljsRepair.ts');
    return { ExcelRepairService };
  } catch (error) {
    console.warn('Could not import repair services, using fallback:', error);
    return null;
  }
}

// API handler plugin for development with separate analysis and repair endpoints
function apiPlugin(): Plugin {
  return {
    name: 'api-plugin',
    configureServer(server) {
      // Unified EXCEL PROCESSOR endpoint - handles both analyze and repair operations
      server.middlewares.use('/api/excel-processor', async (req, res, next) => {
        if (req.method === 'OPTIONS') {
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
          res.statusCode = 200;
          res.end();
          return;
        }

        if (req.method !== 'POST') {
          res.statusCode = 405;
          res.setHeader('Content-Type', 'application/json');
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.end(JSON.stringify({ success: false, error: 'Method not allowed' }));
          return;
        }

        let body = '';
        req.on('data', chunk => {
          body += chunk.toString();
        });

        req.on('end', async () => {
          try {
            const { fileData, fileName, fileSize, operation = 'analyze' } = JSON.parse(body);

            if (!fileData || !fileName) {
              res.statusCode = 400;
              res.setHeader('Content-Type', 'application/json');
              res.setHeader('Access-Control-Allow-Origin', '*');
              res.end(JSON.stringify({
                success: false,
                error: 'Missing required fields: fileData and fileName'
              }));
              return;
            }

            // Validate file size (50MB limit)
            if (fileSize > 50 * 1024 * 1024) {
              res.statusCode = 400;
              res.setHeader('Content-Type', 'application/json');
              res.setHeader('Access-Control-Allow-Origin', '*');
              res.end(JSON.stringify({
                success: false,
                error: 'File size exceeds 50MB limit'
              }));
              return;
            }

            console.log(`🔍 ${operation === 'repair' ? 'Repairing' : 'Analyzing'} file: ${fileName} (${fileSize} bytes)`);

            // Convert base64 to Buffer
            const buffer = Buffer.from(fileData, 'base64');

            // Try to read the workbook
            let workbook: ExcelJS.Workbook | null = null;
            let parseError: string | null = null;

            console.log('📖 Attempting to read Excel workbook...');
            try {
              workbook = new ExcelJS.Workbook();
              await workbook.xlsx.load(buffer);
              console.log('✅ Successfully read Excel workbook');
            } catch (error) {
              parseError = error instanceof Error ? error.message : 'Unknown parse error';
              console.warn('⚠️ Failed to parse Excel file:', parseError);
              workbook = null; // Ensure workbook is null on parse failure
            }

            // Get repair services
            const services = await importRepairServices();
            if (!services) {
              res.statusCode = 500;
              res.setHeader('Content-Type', 'application/json');
              res.setHeader('Access-Control-Allow-Origin', '*');
              res.end(JSON.stringify({
                success: false,
                error: 'Repair services not available'
              }));
              return;
            }

            const file = new File([buffer], fileName, { type: 'application/octet-stream' });
            let result;
            let repairedBase64 = null;

            try {
              if (operation === 'analyze') {
                // Analysis only - no repair
                result = await services.ExcelRepairService.analyzeWorkbook(workbook, parseError ? { initialParseError: parseError } : {}, file);
              } else {
                // Full repair operation
                result = await services.ExcelRepairService.repairWorkbook(workbook, parseError ? { initialParseError: parseError } : {}, file);
                
                // Generate repaired file if successful
                if (result.success && result.repairedWorkbook) {
                  console.log('💾 Writing repaired Excel file...');
                  try {
                    const repairedBuffer = await result.repairedWorkbook.xlsx.writeBuffer();
                    repairedBase64 = Buffer.from(repairedBuffer).toString('base64');
                    console.log('✅ Successfully wrote repaired Excel file');
                  } catch (writeError) {
                    console.warn('⚠️ Failed to write repaired file:', writeError);
                    // Continue without repaired file data
                  }
                }
              }
            } catch (serviceError) {
              console.error('💥 Repair service error:', serviceError);
              // Create a fallback result for severely corrupted files
              result = {
                success: false,
                repairedWorkbook: null,
                repairLog: [{
                  sheet: 'Global',
                  issue: `File processing failed: ${serviceError instanceof Error ? serviceError.message : 'Unknown error'}`,
                  action: 'This file appears to be severely corrupted and cannot be processed with standard methods.',
                  severity: 'high'
                }],
                originalIssues: 1,
                repairedIssues: 0
              };
            }

            // Clean the result to avoid circular references
            const cleanResult = {
              success: result.success,
              repairLog: result.repairLog,
              originalIssues: result.originalIssues,
              repairedIssues: result.repairedIssues,
              summary: result.repairLog?.length > 0 ? 
                (result.repairLog[result.repairLog.length - 1]?.action || 'File processed successfully') :
                'File processed successfully',
              sections: result.repairLog ? [{
                title: operation === 'repair' ? 'Repair Results' : 'Analysis Results',
                issues: result.repairLog.map(log => ({
                  type: log.issue,
                  location: log.cell || log.sheet || 'Unknown',
                  description: log.issue,
                  actionTaken: log.action,
                  severity: log.severity === 'high' ? 'Critical' : log.severity === 'medium' ? 'Major' : 'Minor'
                }))
              }] : [],
              performanceMetrics: {
                analysisTime: 'N/A',
                repairTime: 'N/A'
              }
            };

            const response = {
              success: result.success,
              data: {
                repairSummary: cleanResult,
                repairedFileData: repairedBase64
              },
              originalFileName: fileName,
              repairedFileName: operation === 'repair' ? fileName.replace(/\.xlsx?$/i, '_repaired.xlsx') : null
            };

            console.log(`✅ ${operation === 'repair' ? 'Repair' : 'Analysis'} completed!`);

            res.statusCode = 200;
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.end(JSON.stringify(response));

          } catch (error) {
            console.error('💥 Excel Processor API Error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Internal server error';

            res.statusCode = 500;
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.end(JSON.stringify({
                success: false,
                error: errorMessage,
              }));
          }
        });
      });

      // Legacy REPAIR endpoint for backward compatibility
      server.middlewares.use('/api/repair-file', async (req, res, next) => {
        if (req.method === 'OPTIONS') {
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
          res.statusCode = 200;
          res.end();
          return;
        }

        if (req.method !== 'POST') {
          res.statusCode = 405;
          res.setHeader('Content-Type', 'application/json');
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.end(JSON.stringify({ success: false, error: 'Method not allowed' }));
          return;
        }

        let body = '';
        req.on('data', chunk => {
          body += chunk.toString();
        });

        req.on('end', async () => {
          try {
            const { fileData, fileName, fileSize } = JSON.parse(body);

            if (!fileData || !fileName) {
              res.statusCode = 400;
              res.setHeader('Content-Type', 'application/json');
              res.setHeader('Access-Control-Allow-Origin', '*');
              res.end(JSON.stringify({
                success: false,
                error: 'Missing required fields: fileData and fileName'
              }));
              return;
            }

            console.log(`🔧 Starting repair for: ${fileName} (${fileSize} bytes)`);

            // Convert base64 to Buffer
            const buffer = Buffer.from(fileData, 'base64');

            // Try to read the workbook
            let workbook: ExcelJS.Workbook | null = null;
            let parseError: string | null = null;

            try {
              workbook = new ExcelJS.Workbook();
              await workbook.xlsx.load(buffer);
            } catch (error) {
              parseError = error instanceof Error ? error.message : 'Unknown parse error';
            }

            // Import repair services and perform actual repair
            const services = await importRepairServices();
            if (!services) {
              res.statusCode = 500;
              res.setHeader('Content-Type', 'application/json');
              res.setHeader('Access-Control-Allow-Origin', '*');
              res.end(JSON.stringify({
                success: false,
                error: 'Repair services not available'
              }));
              return;
            }

            // Create a File object for repair services
            const fileBlob = new File([buffer], fileName, { type: 'application/octet-stream' });

            // Perform the actual repair
            console.log('🔧 Executing repair process...');
            const repairResult = await services.ExcelRepairService.repairWorkbook(
              workbook,
              parseError ? { initialParseError: parseError } : {},
              fileBlob
            );

            if (!repairResult.success || !repairResult.repairedWorkbook) {
              console.error('❌ Repair failed');
              res.statusCode = 500;
              res.setHeader('Content-Type', 'application/json');
              res.setHeader('Access-Control-Allow-Origin', '*');
              res.end(JSON.stringify({
                success: false,
                error: 'Repair failed',
                repairSummary: repairResult
              }));
              return;
            }

            // Generate the repaired file
            console.log('💾 Writing repaired Excel file...');
            const repairedBuffer = await repairResult.repairedWorkbook.xlsx.writeBuffer();
            console.log('✅ Successfully wrote repaired Excel file');

            // Convert to base64 for response
            const repairedBase64 = Buffer.from(repairedBuffer).toString('base64');

            const response = {
              success: true,
              repairedFileData: repairedBase64,
              originalFileName: fileName,
              repairedFileName: fileName.replace(/\.xlsx?$/i, '_repaired.xlsx'),
              repairSummary: repairResult
            };

            console.log(`✅ Repair completed successfully!`);

            res.statusCode = 200;
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.end(JSON.stringify(response));

          } catch (error) {
            console.error('💥 Repair API Error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Internal server error';

            res.statusCode = 500;
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.end(JSON.stringify({
              success: false,
              error: errorMessage,
            }));
          }
        });
      });
    }
  };
}

export default defineConfig(({ mode }) => {
    // SECURITY FIX: Removed hardcoded API keys from client bundle
    // API keys should only be used server-side in API routes
    return {
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      plugins: [apiPlugin()],
      build: {
        rollupOptions: {
          output: {
            manualChunks: {
              // React and core libraries
              'react-vendor': ['react', 'react-dom'],

              // Router and navigation
              'router-vendor': ['react-router-dom'],

              // UI and styling libraries
              'ui-vendor': [
                'lucide-react',
                'framer-motion',
                '@radix-ui/react-icons',
                '@radix-ui/react-slot',
                'class-variance-authority',
                'clsx',
                'tailwind-merge',
                'tailwindcss-animate'
              ],

              // Form and validation libraries
              'form-vendor': [
                'react-hook-form',
                '@hookform/resolvers',
                'zod'
              ],

              // File processing and Excel libraries
              'excel-vendor': [
                'exceljs',
                'react-dropzone'
              ],

              // Analytics and tracking
              'analytics-vendor': [
                'react-ga4',
                'js-cookie',
                'react-cookie-consent'
              ],

              // Development tools (only in dev mode)
              ...(mode === 'development' ? {
                'dev-vendor': [
                  '@stagewise/toolbar-react',
                  '@stagewise-plugins/react'
                ]
              } : {}),

              // Other utilities
              'utils-vendor': [
                'resend',
                'react-tweet'
              ]
            }
          }
        },
        // Increase chunk size warning limit since we're optimizing chunks
        chunkSizeWarningLimit: 1000
      }
    };
});
