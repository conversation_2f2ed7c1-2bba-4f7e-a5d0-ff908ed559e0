import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Wifi, WifiOff, <PERSON>fresh<PERSON><PERSON>, AlertTriangle } from 'lucide-react';
import { log } from '../../lib/logger';

interface Props {
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error | undefined;
  errorInfo?: ErrorInfo | undefined;
  isNetworkError: boolean;
  retryCount: number;
}

export class ApiErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      isNetworkError: false,
      retryCount: 0
    };
  }

  public static getDerivedStateFromError(error: Error): Partial<State> {
    const isNetworkError = error.message.includes('fetch') ||
                          error.message.includes('network') ||
                          error.message.includes('timeout') ||
                          error.name === 'TypeError';

    return {
      hasError: true,
      error,
      isNetworkError
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    log.error('api', 'API component error', error, {
      componentStack: errorInfo.componentStack,
      isNetworkError: this.state.isNetworkError,
      retryCount: this.state.retryCount
    });

    this.setState({ errorInfo });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = () => {
    if (this.state.retryCount < 5) {
      log.info('api', 'Retrying API component', {
        retryCount: this.state.retryCount + 1,
        isNetworkError: this.state.isNetworkError
      });

      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        retryCount: this.state.retryCount + 1
      });
    }
  };

  private handleReset = () => {
    log.info('api', 'Resetting API component');

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      isNetworkError: false,
      retryCount: 0
    });
  };

  public render() {
    if (this.state.hasError) {
      const { isNetworkError } = this.state;

      return (
        <div className="border border-orange-200 rounded-lg p-6 bg-orange-50">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                {isNetworkError ? (
                  <WifiOff className="w-6 h-6 text-orange-600" />
                ) : (
                  <AlertTriangle className="w-6 h-6 text-orange-600" />
                )}
              </div>
            </div>

            <h3 className="text-lg font-semibold text-orange-800 mb-2">
              {isNetworkError ? 'Connection Problem' : 'Service Error'}
            </h3>

            <p className="text-orange-600 mb-4">
              {isNetworkError
                ? 'Unable to connect to our servers. Please check your internet connection.'
                : 'Our service is temporarily unavailable. We\'re working to fix this.'
              }
            </p>

            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <button
                onClick={this.handleRetry}
                disabled={this.state.retryCount >= 5}
                className="flex items-center justify-center px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                Retry ({this.state.retryCount}/5)
              </button>

              <button
                onClick={this.handleReset}
                className="flex items-center justify-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors text-sm"
              >
                <Wifi className="w-4 h-4 mr-1" />
                Reset
              </button>
            </div>

            {isNetworkError && (
              <div className="mt-4 pt-4 border-t border-orange-200">
                <p className="text-xs text-orange-500">
                  Check your internet connection and try again.
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}