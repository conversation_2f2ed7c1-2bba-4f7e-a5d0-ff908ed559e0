import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X } from 'lucide-react';

const Navbar: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navLinks = [
    { id: 'features', title: 'About', url: '#features' },
    { id: 'pricing', title: 'Pricing', url: '#pricing' },
    { id: 'faq', title: 'FAQ', url: '#faq' },
  ];

  return (
    <nav className="bg-white shadow-sm border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <img 
              src="/logonew (1).webp" 
              alt="SheetHealer" 
              className="h-12 lg:h-14 w-auto object-contain"
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-10">
            {navLinks.map((link) => (
              <a 
                key={link.id} 
                href={link.url} 
                className="relative text-gray-700 hover:text-gray-900 font-medium text-lg transition-colors group"
            onClick={(e) => {
              e.preventDefault();
                  const targetId = link.url.replace('#', '');
                  const targetElement = document.getElementById(targetId);
                  if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  }
                }}
              >
                {link.title}
                <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-lime-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></span>
              </a>
            ))}
          </div>

          {/* Desktop CTA Button */}
          <Link
            to="/repair"
            className="hidden lg:inline-flex items-center px-6 py-2 bg-gray-900 hover:bg-lime-300 text-white hover:text-gray-900 rounded-lg font-medium transition-colors text-base border border-gray-900"
            >
            Fix my file
          </Link>
            
          {/* Mobile Menu Button */}
            <button
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6 text-gray-900" />
            ) : (
              <Menu className="h-6 w-6 text-gray-900" />
            )}
            </button>
          </div>

        {/* Mobile Menu */}
      {isMobileMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-200">
            <div className="py-4 space-y-2">
              {navLinks.map((link) => (
                <a 
                  key={link.id} 
                  href={link.url} 
                  className="relative block px-4 py-3 text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-lg font-medium transition-colors text-lg group"
                  onClick={(e) => {
                    e.preventDefault();
                    const targetId = link.url.replace('#', '');
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                      targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                >
                  {link.title}
                  <span className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-lime-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></span>
                </a>
              ))}
              <Link
                to="/repair"
                className="block mx-4 mt-4 px-6 py-2 bg-gray-900 hover:bg-lime-300 text-white hover:text-gray-900 rounded-lg font-medium transition-colors text-center text-base"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Fix my file
              </Link>
            </div>
        </div>
      )}
      </div>
    </nav>
  );
};

export default Navbar;
