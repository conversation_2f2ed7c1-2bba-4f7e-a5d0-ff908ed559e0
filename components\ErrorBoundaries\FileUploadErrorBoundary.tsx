import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Upload } from 'lucide-react';
import { log } from '../../lib/logger';

interface Props {
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error | undefined;
  errorInfo?: ErrorInfo | undefined;
  retryCount: number;
}

export class FileUploadErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      retryCount: 0
    };
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, retryCount: 0 };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    log.error('file-upload', 'File upload component error', error, {
      componentStack: errorInfo.componentStack,
      retryCount: this.state.retryCount
    });

    this.setState({ errorInfo });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = () => {
    if (this.state.retryCount < 3) {
      log.info('file-upload', 'Retrying file upload component', {
        retryCount: this.state.retryCount + 1
      });

      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        retryCount: this.state.retryCount + 1
      });
    }
  };

  private handleReset = () => {
    log.info('file-upload', 'Resetting file upload component');

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: 0
    });
  };

  public render() {
    if (this.state.hasError) {
      return (
        <div className="border-2 border-dashed border-red-300 rounded-2xl p-12 bg-red-50">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
            </div>

            <h3 className="text-xl font-bold text-red-800 mb-4">
              File Upload Error
            </h3>

            <p className="text-red-600 mb-6 max-w-md mx-auto">
              Something went wrong with the file upload component. This might be due to a browser compatibility issue or a temporary glitch.
            </p>

            {this.state.error && (
              <div className="bg-red-100 rounded-lg p-4 mb-6 text-left max-w-md mx-auto">
                <h4 className="text-sm font-semibold text-red-800 mb-2">Technical Details:</h4>
                <p className="text-xs text-red-600 font-mono break-all">
                  {this.state.error.message}
                </p>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={this.handleRetry}
                disabled={this.state.retryCount >= 3}
                className="flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry ({this.state.retryCount}/3)
              </button>

              <button
                onClick={this.handleReset}
                className="flex items-center justify-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
              >
                <Upload className="w-4 h-4 mr-2" />
                Reset Upload
              </button>
            </div>

            <div className="mt-6 pt-6 border-t border-red-200">
              <p className="text-sm text-red-500">
                If this problem persists, try refreshing the page or using a different browser.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}