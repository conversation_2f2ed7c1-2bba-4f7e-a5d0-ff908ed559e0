import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { <PERSON>Right, Calculator, Database, FileX, Paintbrush, Link, LayoutGrid } from 'lucide-react';

const FeaturesSection: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Problems We Fix
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            From corrupted formulas to completely broken files, we&apos;ve seen it all. 
            Here are the most common .xlsx issues we repair every day.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          
          {/* Featured Service - Large Card */}
          <div className="lg:col-span-2 bg-gray-900 rounded-3xl p-8 lg:p-12 text-white relative overflow-hidden">
            <div className="grid lg:grid-cols-2 gap-8 items-center relative z-10">
              <div>
                <h3 className="text-2xl lg:text-3xl font-bold mb-4">
                  Unopenable Excel Files
                </h3>
                <p className="text-gray-300 text-lg mb-6 leading-relaxed">
                  Excel crashes or shows error messages when you try to open your file? 
                  This is the most common issue we fix. Upload your broken .xlsx file 
                  and get it working again in seconds.
                </p>
                <RouterLink 
                  to="/repair"
                  className="inline-flex items-center gap-2 bg-lime-400 hover:bg-white text-gray-900 hover:text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors border border-lime-400"
                >
                  <ArrowRight className="h-4 w-4" />
                  Fix File Now
                </RouterLink>
              </div>
              <div className="relative">
                <div className="w-full aspect-square max-w-sm mx-auto bg-lime-400/20 rounded-2xl flex items-center justify-center">
                  <FileX className="h-24 w-24 text-lime-400" />
                </div>
      </div>
    </div>
            
            {/* Background decoration */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 right-10 w-32 h-32 border border-white rounded-full"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 border border-white rounded-full"></div>
            </div>
          </div>
      </div>

        {/* Problem Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          {/* Problem 1 */}
          <div className="bg-lime-100 border border-lime-200 rounded-3xl p-8 relative overflow-hidden group hover:shadow-lg transition-shadow">
            <div className="relative z-10">
              <div className="w-16 h-16 bg-lime-400 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <Calculator className="h-8 w-8 text-gray-900" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Corrupted Formulas
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Broken calculations and #REF/#VALUE errors that prevent 
                your spreadsheet from working correctly.
              </p>
            </div>
            <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-lime-400/20 rounded-full"></div>
          </div>

          {/* Problem 2 */}
          <div className="bg-white border-2 border-gray-900 rounded-3xl p-8 relative overflow-hidden group hover:shadow-lg transition-shadow">
            <div className="relative z-10">
              <div className="w-16 h-16 bg-gray-900 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <Database className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Data & Chart Recovery
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Recover cell values, charts, and tables that have disappeared 
                or become scrambled due to corruption.
              </p>
            </div>
            <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-gray-100 rounded-full"></div>
          </div>

          {/* Problem 3 */}
          <div className="bg-lime-100 border border-lime-200 rounded-3xl p-8 relative overflow-hidden group hover:shadow-lg transition-shadow">
            <div className="relative z-10">
              <div className="w-16 h-16 bg-lime-400 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <Paintbrush className="h-8 w-8 text-gray-900" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Formatting Errors
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Messed up cell styles and display issues that make 
                your data unreadable or unprofessional.
              </p>
            </div>
            <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-lime-400/20 rounded-full"></div>
          </div>

          {/* Problem 4 */}
          <div className="bg-white border-2 border-gray-900 rounded-3xl p-8 relative overflow-hidden group hover:shadow-lg transition-shadow">
            <div className="relative z-10">
              <div className="w-16 h-16 bg-gray-900 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <Link className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Broken External Links
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Fixing broken links to other workbooks or data sources that cause errors on opening.
              </p>
            </div>
            <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-gray-100 rounded-full"></div>
          </div>

          {/* Problem 5 */}
          <div className="bg-lime-100 border border-lime-200 rounded-3xl p-8 relative overflow-hidden group hover:shadow-lg transition-shadow">
            <div className="relative z-10">
              <div className="w-16 h-16 bg-lime-400 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <LayoutGrid className="h-8 w-8 text-gray-900" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Broken Pivot Tables
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Repairing corrupted Pivot Tables that fail to refresh or show incorrect data.
              </p>
            </div>
            <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-lime-400/20 rounded-full"></div>
          </div>

          {/* Problem 6 */}
          <div className="bg-white border-2 border-gray-900 rounded-3xl p-8 relative overflow-hidden group hover:shadow-lg transition-shadow">
            <div className="relative z-10">
              <div className="w-16 h-16 bg-gray-900 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <FileX className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                File Won&apos;t Open
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Excel shows error messages or crashes completely 
                when trying to open your important spreadsheet.
              </p>
            </div>
            <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-gray-100 rounded-full"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;