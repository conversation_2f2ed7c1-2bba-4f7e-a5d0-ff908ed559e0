
# Gemini Project Configuration

## Project Overview

This project is a React application called "sheethealer---excel-file-recovery". It appears to be a tool for recovering and repairing Excel files.

## Tech Stack

- **Language:** TypeScript
- **Framework:** React
- **Build Tool:** Vite
- **Styling:** Tailwind CSS

## Commands

- **Run development server:** `npm run dev`
- **Build for production:** `npm run build`
- **Preview production build:** `npm run preview`

## Key Files

- `vite.config.ts`: Vite configuration
- `tailwind.config.ts`: Tailwind CSS configuration
- `src/App.tsx`: Main application component
- `src/index.tsx`: Application entry point
- `pages/`: Directory containing the application's pages
- `components/`: Directory containing reusable React components
- `lib/`: Directory containing utility and library functions

## General Instructions

- Please follow the existing coding style and conventions.
- Use TypeScript and React for all new components and features.
- Ensure any new components are placed in the `components/` directory.
- For any new pages, add them to the `pages/` directory.
