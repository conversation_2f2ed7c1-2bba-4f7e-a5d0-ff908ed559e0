import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Keyboard, X, HelpCircle } from 'lucide-react';
import { useKeyboardNavigation, KeyboardNavigationManager } from '../lib/keyboardNavigation';

interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  isOpen,
  onClose
}) => {
  const { shortcuts, trapFocus } = useKeyboardNavigation();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', label: 'All Shortcuts' },
    { id: 'file', label: 'File Operations' },
    { id: 'navigation', label: 'Navigation' },
    { id: 'accessibility', label: 'Accessibility' },
    { id: 'general', label: 'General' }
  ];

  const filteredShortcuts = selectedCategory === 'all' 
    ? shortcuts 
    : shortcuts.filter(shortcut => shortcut.category === selectedCategory);

  // Trap focus when modal is open
  useEffect(() => {
    if (isOpen) {
      const modalElement = document.getElementById('shortcuts-modal');
      if (modalElement) {
        const cleanup = trapFocus(modalElement);
        return cleanup;
      }
    }
  }, [isOpen, trapFocus]);

  // Close on Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          id="shortcuts-modal"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
          role="dialog"
          aria-labelledby="shortcuts-title"
          aria-describedby="shortcuts-description"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Keyboard className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 id="shortcuts-title" className="text-xl font-bold text-gray-900">
                  Keyboard Shortcuts
                </h2>
                <p id="shortcuts-description" className="text-sm text-gray-600">
                  Navigate faster with these keyboard shortcuts
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Close keyboard shortcuts help"
            >
              <X className="h-5 w-5 text-gray-600" />
            </button>
          </div>

          {/* Category Filter */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    selectedCategory === category.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  aria-pressed={selectedCategory === category.id}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </div>

          {/* Shortcuts List */}
          <div className="p-6 overflow-y-auto max-h-96">
            {filteredShortcuts.length > 0 ? (
              <div className="space-y-4">
                {filteredShortcuts.map((shortcut, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">
                        {shortcut.description}
                      </div>
                      <div className="text-sm text-gray-600 capitalize">
                        {shortcut.category}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {KeyboardNavigationManager.formatShortcut(shortcut)
                        .split(' + ')
                        .map((key, keyIndex, array) => (
                          <React.Fragment key={keyIndex}>
                            <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-sm font-mono text-gray-700 shadow-sm">
                              {key}
                            </kbd>
                            {keyIndex < array.length - 1 && (
                              <span className="text-gray-400 mx-1">+</span>
                            )}
                          </React.Fragment>
                        ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No shortcuts found
                </h3>
                <p className="text-gray-600">
                  {selectedCategory === 'all' 
                    ? 'No keyboard shortcuts are currently registered.'
                    : `No shortcuts found in the ${categories.find(c => c.id === selectedCategory)?.label} category.`
                  }
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Press <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs font-mono">Esc</kbd> to close
              </div>
              <div className="text-sm text-gray-600">
                {filteredShortcuts.length} shortcut{filteredShortcuts.length !== 1 ? 's' : ''} available
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

// Floating help button component
export const KeyboardShortcutsButton: React.FC = () => {
  const [isHelpOpen, setIsHelpOpen] = useState(false);

  return (
    <>
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsHelpOpen(true)}
        className="fixed bottom-4 right-4 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 z-40"
        aria-label="Show keyboard shortcuts help"
        title="Keyboard Shortcuts (Press ? for help)"
      >
        <Keyboard className="h-5 w-5" />
      </motion.button>

      <KeyboardShortcutsHelp
        isOpen={isHelpOpen}
        onClose={() => setIsHelpOpen(false)}
      />
    </>
  );
};

export default KeyboardShortcutsHelp;