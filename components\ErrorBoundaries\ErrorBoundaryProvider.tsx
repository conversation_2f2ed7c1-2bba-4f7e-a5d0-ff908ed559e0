import React, { ReactNode } from 'react';
import { FileUploadErrorBoundary } from './FileUploadErrorBoundary';
import { ApiErrorBoundary } from './ApiErrorBoundary';
import ErrorBoundary from '../ErrorBoundary';

interface ErrorBoundaryProviderProps {
  children: ReactNode;
  type?: 'global' | 'file-upload' | 'api' | 'form';
}

/**
 * Provides appropriate error boundary based on the component type
 */
export const ErrorBoundaryProvider: React.FC<ErrorBoundaryProviderProps> = ({ 
  children, 
  type = 'global' 
}) => {
  switch (type) {
    case 'file-upload':
      return (
        <FileUploadErrorBoundary>
          {children}
        </FileUploadErrorBoundary>
      );
    
    case 'api':
      return (
        <ApiErrorBoundary>
          {children}
        </ApiErrorBoundary>
      );
    
    case 'form':
      return (
        <ApiErrorBoundary>
          {children}
        </ApiErrorBoundary>
      );
    
    case 'global':
    default:
      return (
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      );
  }
};

/**
 * Higher-order component for wrapping components with appropriate error boundaries
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  boundaryType: 'global' | 'file-upload' | 'api' | 'form' = 'global'
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundaryProvider type={boundaryType}>
      <Component {...props} />
    </ErrorBoundaryProvider>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook for manually triggering error boundaries (useful for testing)
 */
export const useErrorHandler = () => {
  return (error: Error, errorInfo?: any) => {
    // This will trigger the nearest error boundary
    throw error;
  };
};