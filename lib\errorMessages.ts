/**
 * Enhanced Error Message System
 * Provides user-friendly error messages with specific guidance and actionable solutions
 */

export interface ErrorDetails {
  code: string;
  title: string;
  message: string;
  guidance: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'file' | 'network' | 'server' | 'validation' | 'processing' | 'memory';
  icon: string;
  actionable: boolean;
  suggestedActions?: {
    primary?: string;
    secondary?: string[];
  };
  technicalDetails?: string;
  helpUrl?: string;
}

export interface ErrorContext {
  fileName?: string | undefined;
  fileSize?: number;
  fileType?: string;
  operation?: 'upload' | 'analyze' | 'repair' | 'download';
  step?: string | undefined;
  userAgent?: string;
  timestamp?: number;
}

export class EnhancedErrorHandler {
  private static readonly ERROR_CATALOG: Record<string, ErrorDetails> = {
    // File-related errors
    'FILE_TOO_LARGE': {
      code: 'FILE_TOO_LARGE',
      title: 'File Too Large',
      message: 'Your file is too large to process.',
      guidance: [
        'Try compressing your Excel file by removing unused rows and columns',
        'Save your file in .xlsx format instead of .xls for better compression',
        'Split large files into smaller chunks if possible',
        'Remove unnecessary images, charts, or embedded objects'
      ],
      severity: 'medium',
      category: 'file',
      icon: '📏',
      actionable: true,
      suggestedActions: {
        primary: 'Reduce file size and try again',
        secondary: ['Contact support for large file processing', 'Use file compression tools']
      },
      helpUrl: '/help/file-size-limits'
    },

    'FILE_CORRUPTED': {
      code: 'FILE_CORRUPTED',
      title: 'File Appears Corrupted',
      message: 'Your Excel file seems to be corrupted or damaged.',
      guidance: [
        'Try opening the file in Excel to see if it works there',
        'If Excel can open it, save it again and re-upload',
        'Check if the file was completely downloaded before uploading',
        'Try uploading a different version of the file if available'
      ],
      severity: 'high',
      category: 'file',
      icon: '🔧',
      actionable: true,
      suggestedActions: {
        primary: 'Try our repair service',
        secondary: ['Re-save file in Excel', 'Upload a backup copy']
      },
      helpUrl: '/help/corrupted-files'
    },

    'UNSUPPORTED_FORMAT': {
      code: 'UNSUPPORTED_FORMAT',
      title: 'Unsupported File Format',
      message: 'We currently only support .xlsx Excel files.',
      guidance: [
        'Convert your file to .xlsx format using Excel',
        'Open your file in Excel and use "Save As" → Excel Workbook (.xlsx)',
        'We don\'t support .xls, .csv, or other spreadsheet formats yet',
        'Make sure your file has the correct .xlsx extension'
      ],
      severity: 'medium',
      category: 'validation',
      icon: '📄',
      actionable: true,
      suggestedActions: {
        primary: 'Convert to .xlsx format',
        secondary: ['Check file extension', 'Use Excel to convert']
      },
      helpUrl: '/help/supported-formats'
    },

    'FILE_EMPTY': {
      code: 'FILE_EMPTY',
      title: 'File is Empty',
      message: 'The uploaded file appears to be empty or too small.',
      guidance: [
        'Make sure you selected the correct file',
        'Check that the file contains actual Excel data',
        'Verify the file isn\'t corrupted during upload',
        'Try uploading the file again'
      ],
      severity: 'medium',
      category: 'file',
      icon: '📭',
      actionable: true,
      suggestedActions: {
        primary: 'Upload a different file',
        secondary: ['Check file contents', 'Try uploading again']
      }
    },

    // Network-related errors
    'NETWORK_TIMEOUT': {
      code: 'NETWORK_TIMEOUT',
      title: 'Upload Timed Out',
      message: 'Your file upload took too long and timed out.',
      guidance: [
        'Check your internet connection speed',
        'Try uploading during off-peak hours for better speed',
        'Consider reducing your file size if possible',
        'Make sure you have a stable internet connection'
      ],
      severity: 'medium',
      category: 'network',
      icon: '⏱️',
      actionable: true,
      suggestedActions: {
        primary: 'Try again',
        secondary: ['Check internet connection', 'Reduce file size']
      },
      helpUrl: '/help/upload-issues'
    },

    'NETWORK_ERROR': {
      code: 'NETWORK_ERROR',
      title: 'Connection Problem',
      message: 'We couldn\'t connect to our servers.',
      guidance: [
        'Check your internet connection',
        'Try refreshing the page and uploading again',
        'Disable any VPN or proxy that might interfere',
        'Check if your firewall is blocking the connection'
      ],
      severity: 'medium',
      category: 'network',
      icon: '🌐',
      actionable: true,
      suggestedActions: {
        primary: 'Refresh and try again',
        secondary: ['Check internet connection', 'Disable VPN/proxy']
      }
    },

    // Server-related errors
    'SERVER_ERROR': {
      code: 'SERVER_ERROR',
      title: 'Server Error',
      message: 'Something went wrong on our end.',
      guidance: [
        'This is a temporary issue on our servers',
        'Please try again in a few minutes',
        'If the problem persists, contact our support team',
        'Your file is safe and wasn\'t damaged'
      ],
      severity: 'high',
      category: 'server',
      icon: '🔧',
      actionable: true,
      suggestedActions: {
        primary: 'Try again in a few minutes',
        secondary: ['Contact support if issue persists', 'Check our status page']
      },
      helpUrl: '/help/server-issues'
    },

    'RATE_LIMITED': {
      code: 'RATE_LIMITED',
      title: 'Too Many Requests',
      message: 'You\'ve made too many requests. Please wait before trying again.',
      guidance: [
        'Wait a few minutes before uploading another file',
        'We limit requests to ensure good performance for everyone',
        'Consider upgrading to a premium plan for higher limits',
        'Contact support if you need to process many files'
      ],
      severity: 'medium',
      category: 'server',
      icon: '⏳',
      actionable: true,
      suggestedActions: {
        primary: 'Wait and try again',
        secondary: ['Upgrade to premium', 'Contact support']
      },
      helpUrl: '/help/rate-limits'
    },

    // Processing errors
    'PROCESSING_FAILED': {
      code: 'PROCESSING_FAILED',
      title: 'Processing Failed',
      message: 'We couldn\'t process your Excel file.',
      guidance: [
        'Your file might have complex formatting that we can\'t handle yet',
        'Try simplifying your file by removing complex formulas or formatting',
        'Save your file in a newer Excel format (.xlsx)',
        'Contact support with your file for personalized help'
      ],
      severity: 'high',
      category: 'processing',
      icon: '⚙️',
      actionable: true,
      suggestedActions: {
        primary: 'Simplify file and try again',
        secondary: ['Contact support', 'Try different file version']
      },
      helpUrl: '/help/processing-issues'
    },

    'MEMORY_EXCEEDED': {
      code: 'MEMORY_EXCEEDED',
      title: 'File Too Complex',
      message: 'Your file is too complex or large for our current processing limits.',
      guidance: [
        'Try reducing the number of sheets in your workbook',
        'Remove unnecessary formulas, charts, or images',
        'Split large datasets into smaller files',
        'Consider upgrading to premium for higher processing limits'
      ],
      severity: 'medium',
      category: 'memory',
      icon: '🧠',
      actionable: true,
      suggestedActions: {
        primary: 'Simplify your file',
        secondary: ['Upgrade to premium', 'Split into smaller files']
      },
      helpUrl: '/help/file-complexity'
    },

    // Repair-specific errors
    'REPAIR_FAILED': {
      code: 'REPAIR_FAILED',
      title: 'Repair Unsuccessful',
      message: 'We couldn\'t fully repair your Excel file.',
      guidance: [
        'Some corruption might be too severe to fix automatically',
        'Try opening the file in Excel and manually fixing obvious issues',
        'Contact our support team for manual repair assistance',
        'We\'ve done our best to recover what we could'
      ],
      severity: 'high',
      category: 'processing',
      icon: '🔧',
      actionable: true,
      suggestedActions: {
        primary: 'Download partial repair',
        secondary: ['Contact support', 'Manual repair in Excel']
      },
      helpUrl: '/help/repair-limitations'
    },

    'REPAIR_PARTIAL': {
      code: 'REPAIR_PARTIAL',
      title: 'Partial Repair Success',
      message: 'We fixed some issues but couldn\'t repair everything.',
      guidance: [
        'Download the partially repaired file to see what we fixed',
        'Some complex corruption requires manual intervention',
        'The repaired file should work better than the original',
        'Contact support if you need help with remaining issues'
      ],
      severity: 'medium',
      category: 'processing',
      icon: '🔧',
      actionable: true,
      suggestedActions: {
        primary: 'Download repaired file',
        secondary: ['Contact support for remaining issues', 'Manual cleanup']
      },
      helpUrl: '/help/partial-repairs'
    },

    // Generic fallback
    'UNKNOWN_ERROR': {
      code: 'UNKNOWN_ERROR',
      title: 'Something Went Wrong',
      message: 'An unexpected error occurred.',
      guidance: [
        'Try refreshing the page and uploading again',
        'Check your internet connection',
        'If the problem persists, contact our support team',
        'Include details about what you were doing when this happened'
      ],
      severity: 'medium',
      category: 'server',
      icon: '❓',
      actionable: true,
      suggestedActions: {
        primary: 'Try again',
        secondary: ['Refresh page', 'Contact support']
      },
      helpUrl: '/help/general-issues'
    }
  };

  /**
   * Get enhanced error details for a given error
   */
  static getErrorDetails(error: Error | string, context?: ErrorContext): ErrorDetails {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorCode = this.categorizeError(errorMessage, context);

    const baseError = this.ERROR_CATALOG[errorCode] || this.ERROR_CATALOG['UNKNOWN_ERROR'];

    // Enhance with context-specific information
    return {
      ...baseError,
      message: this.personalizeMessage(baseError.message, context),
      guidance: this.personalizeGuidance(baseError.guidance, context),
      technicalDetails: typeof error === 'object' ? error.message : errorMessage
    };
  }

  /**
   * Categorize error based on message and context
   */
  private static categorizeError(message: string, context?: ErrorContext): string {
    const lowerMessage = message.toLowerCase();

    // File size errors
    if (lowerMessage.includes('too large') || lowerMessage.includes('size limit') ||
        lowerMessage.includes('file size') || (context?.fileSize && context.fileSize > 25 * 1024 * 1024)) {
      return 'FILE_TOO_LARGE';
    }

    // File format errors
    if (lowerMessage.includes('unsupported') || lowerMessage.includes('format') ||
        lowerMessage.includes('extension') || lowerMessage.includes('xlsx')) {
      return 'UNSUPPORTED_FORMAT';
    }

    // Empty file errors
    if (lowerMessage.includes('empty') || lowerMessage.includes('too small') ||
        (context?.fileSize && context.fileSize < 100)) {
      return 'FILE_EMPTY';
    }

    // Network timeout errors
    if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
      return 'NETWORK_TIMEOUT';
    }

    // Network connection errors
    if (lowerMessage.includes('network') || lowerMessage.includes('connection') ||
        lowerMessage.includes('fetch') || lowerMessage.includes('cors')) {
      return 'NETWORK_ERROR';
    }

    // Rate limiting
    if (lowerMessage.includes('rate limit') || lowerMessage.includes('too many requests')) {
      return 'RATE_LIMITED';
    }

    // Memory errors
    if (lowerMessage.includes('memory') || lowerMessage.includes('out of memory') ||
        lowerMessage.includes('heap') || lowerMessage.includes('complex')) {
      return 'MEMORY_EXCEEDED';
    }

    // Corruption errors
    if (lowerMessage.includes('corrupt') || lowerMessage.includes('damaged') ||
        lowerMessage.includes('invalid') || lowerMessage.includes('malformed')) {
      return 'FILE_CORRUPTED';
    }

    // Repair errors
    if (lowerMessage.includes('repair') && lowerMessage.includes('failed')) {
      return 'REPAIR_FAILED';
    }

    if (lowerMessage.includes('partial') && lowerMessage.includes('repair')) {
      return 'REPAIR_PARTIAL';
    }

    // Processing errors
    if (lowerMessage.includes('processing') || lowerMessage.includes('analyze') ||
        lowerMessage.includes('parse') || lowerMessage.includes('read')) {
      return 'PROCESSING_FAILED';
    }

    // Server errors
    if (lowerMessage.includes('server') || lowerMessage.includes('500') ||
        lowerMessage.includes('internal') || lowerMessage.includes('service')) {
      return 'SERVER_ERROR';
    }

    return 'UNKNOWN_ERROR';
  }

  /**
   * Personalize error message with context
   */
  private static personalizeMessage(message: string, context?: ErrorContext): string {
    if (!context) return message;

    let personalizedMessage = message;

    if (context.fileName) {
      personalizedMessage = personalizedMessage.replace(
        'Your file',
        `Your file "${context.fileName}"`
      );
    }

    if (context.fileSize) {
      const sizeInMB = (context.fileSize / (1024 * 1024)).toFixed(1);
      personalizedMessage += ` (${sizeInMB}MB)`;
    }

    return personalizedMessage;
  }

  /**
   * Personalize guidance with context
   */
  private static personalizeGuidance(guidance: string[], context?: ErrorContext): string[] {
    if (!context) return guidance;

    return guidance.map(guide => {
      if (context.fileName && guide.includes('your file')) {
        return guide.replace('your file', `"${context.fileName}"`);
      }
      return guide;
    });
  }

  /**
   * Format error for display
   */
  static formatErrorForDisplay(error: Error | string, context?: ErrorContext) {
    const details = this.getErrorDetails(error, context);

    return {
      ...details,
      displayMessage: `${details.icon} ${details.title}`,
      fullMessage: details.message,
      actionableGuidance: details.guidance.slice(0, 3), // Limit to top 3 guidance items
      canRetry: details.actionable && ['NETWORK_TIMEOUT', 'NETWORK_ERROR', 'SERVER_ERROR'].includes(details.code),
      canContact: details.severity === 'high' || details.severity === 'critical'
    };
  }

  /**
   * Log error with context for debugging
   */
  static logError(error: Error | string, context?: ErrorContext) {
    const details = this.getErrorDetails(error, context);

    console.error('Enhanced Error:', {
      code: details.code,
      category: details.category,
      severity: details.severity,
      message: details.message,
      technicalDetails: details.technicalDetails,
      context,
      timestamp: new Date().toISOString()
    });
  }
}

// Utility functions for common error scenarios
export const createFileError = (message: string, fileName?: string, fileSize?: number) => {
  const context: ErrorContext = {
    operation: 'upload',
    timestamp: Date.now()
  };
  if (fileName) context.fileName = fileName;
  if (fileSize) context.fileSize = fileSize;
  return EnhancedErrorHandler.formatErrorForDisplay(message, context);
};

export const createNetworkError = (message: string, operation?: 'upload' | 'analyze' | 'repair') => {
  const context: ErrorContext = {
    timestamp: Date.now()
  };
  if (operation) context.operation = operation;
  return EnhancedErrorHandler.formatErrorForDisplay(message, context);
};

export const createProcessingError = (message: string, fileName?: string, step?: string) => {
  const context: ErrorContext = {
    operation: 'analyze',
    timestamp: Date.now()
  };
  if (fileName) context.fileName = fileName;
  if (step) context.step = step;
  return EnhancedErrorHandler.formatErrorForDisplay(message, context);
};