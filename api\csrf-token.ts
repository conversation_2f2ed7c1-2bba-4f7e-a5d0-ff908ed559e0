import { VercelRequest, VercelResponse } from '@vercel/node';
import { handleCSRFTokenRequest } from '../lib/csrf';
import { CsrfTokenResponseSchema } from '../lib/validation';

export default function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Access-Control-Expose-Headers', 'X-CSRF-Token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Wrap the CSRF handler to add validation
  const originalJson = res.json.bind(res);
  res.json = function(data: any) {
    // Validate CSRF token response if it contains a token
    if (data && typeof data === 'object' && 'csrfToken' in data) {
      const tokenResponse = { token: data.csrfToken };
      const validation = CsrfTokenResponseSchema.safeParse(tokenResponse);
      if (!validation.success) {
        console.error('CSRF token response validation failed:', validation.error);
      }
    }
    return originalJson(data);
  };

  handleCSRFTokenRequest(req, res);
}