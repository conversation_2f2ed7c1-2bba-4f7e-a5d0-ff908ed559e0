
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

interface PageHeaderProps {
  showBackButton?: boolean;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  showBackButton = true 
}) => {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-4xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {showBackButton && (
            <Link 
              to="/" 
              className="inline-flex items-center text-gray-700 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          )}
          
          <Link 
            to="/" 
            className="flex items-center hover:opacity-80 transition-opacity duration-200 ml-auto"
          >
            <img 
              src="/logonew (1).webp" 
              alt="SheetHealer Logo" 
              className="h-10 w-auto max-w-[200px] object-contain"
            />
          </Link>
        </div>
      </div>
    </header>
  );
};

export default PageHeader;