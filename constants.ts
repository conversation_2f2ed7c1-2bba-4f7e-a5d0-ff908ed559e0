import { NavItem, StatItem, HowItWorksStep, FeatureItem, FAQ, PricingPlan, FooterLinkGroup, INavbarMenu } from './types';

export const NAV_LINKS: NavItem[] = [
  { label: 'Features', href: '#' },
  { label: 'How it works', href: '#' },
  { label: 'Pricing', href: '#' },
];

export const NAV_MENU_LINKS: INavbarMenu[] = [
  { id: 1, title: 'Features', url: '#features' },
  { id: 2, title: 'How It Works', url: '#how-it-works' },
  { id: 3, title: 'Pricing', url: '#pricing' },
  { id: 4, title: 'FAQ', url: '#faq' },
];

export const STATS_DATA: StatItem[] = [
  { value: '99.9%', label: 'Success Rate' },
  { value: '5s', label: 'Avg Repair Time' },
  { value: '256-bit', label: 'Encryption' },
  { value: '24h', label: 'Auto-Delete Files' },
];

export const HOW_IT_WORKS_STEPS: HowItWorksStep[] = [
  { iconClass: 'Upload', title: '1. Upload your file', description: "Drag and drop your broken workbook. It's encrypted while in transit and storage." },
  { iconClass: 'Cloud', title: '2. We fix it in the cloud', description: "Our engine scans for corruption, patches the damage, and rechecks the file all in a few seconds." },
  { iconClass: 'Download', title: '3. Download and get back to work', description: "Grab the repaired file instantly. Get a clear summary of what was fixed." },
];

export const PROBLEMS_FIXED_DATA: FeatureItem[] = [
  { iconClass: "fas fa-calculator", title: "Corrupted formulas", description: "Broken calculations and #REF/#VALUE errors" },
  { iconClass: "fas fa-database", title: "Missing data", description: "Scrambled or incomplete cell values" },
  { iconClass: "fas fa-file-excel", title: "Unopenable files", description: "Excel crashes or shows error messages" },
  { iconClass: "fas fa-paint-brush", title: "Formatting errors", description: "Messed up cell styles and display issues" },
  { iconClass: "fas fa-lock", title: "Password recovery", description: "Forgotten passwords on protected files" },
  { iconClass: "fas fa-weight-hanging", title: "Large files", description: "Workbooks that crash or freeze Excel" },
];

export const WHY_SHEETHEALER_DATA: FeatureItem[] = [
  { iconClass: "fas fa-trophy", title: "High success rate", description: "Repairs files that won't open in Excel" },
  { iconClass: "fas fa-bolt", title: "Fast repairs", description: "Small workbooks fixed in under 5 seconds" },
  { iconClass: "fas fa-cloud", title: "Zero install", description: "100% web-based, secure edge runtime" },
  { iconClass: "fas fa-list", title: "Repair report", description: "Clear summary of what we fixed in your file" },
  { iconClass: "fas fa-lock", title: "Privacy first", description: "Files encrypted at rest, auto-purged after 24h" },
  { iconClass: "fas fa-file-excel", title: "Excel files supported", description: "Works with .xlsx files (the most common Excel format)" },
];

export const FAQ_DATA: FAQ[] = [
  { question: "What file types are supported?", answer: "We currently support .xlsx files, which covers the vast majority of modern Excel files." },
  { question: "How fast is the repair process?", answer: "Most files under 10MB are fixed in under 5 seconds. Larger or severely damaged files may take longer." },
  { question: "Is my data safe and secure?", answer: "Yes. Files are encrypted in transit and at rest, stored in private buckets, and auto-deleted after 24 hours. Your data is never modified—only corrupt structure is rebuilt." },
  { question: "What are the pricing options?", answer: "One-Time Fix: $9 per file for single repairs. Unlimited Monthly: $19/month for unlimited repairs with priority processing and detailed reports. All plans include secure processing and instant downloads." },
  { question: "Do I need to install software?", answer: "No installation required. SheetHealer is 100% web-based and works on any modern browser." },
  { question: "What if a repair fails?", answer: "No charge for failed repairs. We provide error details and next steps, including manual support when needed." },
];

export const PRICING_PLANS_DATA: PricingPlan[] = [
  {
    name: "One-Time Fix",
    price: "$9",
    priceSuffix: "/file",
    features: ["Single .xlsx file repair", "Standard processing speed", "Basic repair report", "Email support", "24-hour file storage"],
    buttonText: "Fix a Single File",
    buttonClass: "border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 bg-white font-semibold text-base leading-normal",
  },
  {
    name: "Unlimited Monthly",
    price: "$19",
    priceSuffix: "/month",
    features: ["Unlimited file repairs", "Priority processing", "Detailed repair reports", "Priority email support", "File history (30 days)"],
    buttonText: "Go Unlimited",
    isPopular: true,
    buttonClass: "border-2 border-green-600 text-green-600 hover:bg-green-50 hover:text-green-700 bg-white font-semibold text-base leading-normal",
    borderColor: "border-green-500",
    textColor: "text-green-500",
  },
];

export const FOOTER_LINKS_DATA: FooterLinkGroup[] = [
  {
    title: "Product",
    links: [
      { label: "Features", href: "#" },
      { label: "Pricing", href: "#" },
      { label: "Roadmap", href: "#" },
      { label: "Changelog", href: "#" },
    ],
  },
  {
    title: "Resources",
    links: [
      { label: "Documentation", href: "#" },
      { label: "Blog", href: "#" },
      { label: "Support", href: "#" },
      { label: "API", href: "#" },
    ],
  },
  {
    title: "Company",
    links: [
      { label: "About", href: "#" },
      { label: "Careers", href: "#" },
      { label: "Privacy", href: "#" },
      { label: "Terms", href: "#" },
    ],
  },
];
