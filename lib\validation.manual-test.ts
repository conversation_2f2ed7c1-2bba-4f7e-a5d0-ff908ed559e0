/**
 * Manual validation tests for all schemas
 * Run this file with: npx ts-node lib/validation.manual-test.ts
 */

import {
  FileUploadSchema,
  EmailFormSchema,
  RepairIssueSchema,
  RepairReportSchema,
  FileProcessingSuccessSchema,
  FileProcessingErrorSchema,
  ApiSuccessSchema,
  ApiErrorSchema,
  FileProcessingStateSchema,
  FileUploadStateSchema,
  ValidationErrorSchema,
  ApiErrorDetailSchema,
  AppConfigSchema,
  ProgressIndicatorPropsSchema,
  ErrorDisplayPropsSchema,
  CsrfTokenResponseSchema,
  EmailSubmissionSuccessSchema,
  EmailSubmissionErrorSchema,
  EnvironmentSchema,
  validateInput,
  validateInputWithDetails,
  validateEnvironment,
  isValidRepairReport,
  isValidFileProcessingResponse,
  isValidApiError,
  isValidEmailFormData,
  isValidFileUploadData,
  sanitizeString,
  sanitizeEmail,
  validateFileExtension,
  validateFileSize,
  validateBase64
} from './validation';

// Test runner
class ValidationTester {
  private passedTests = 0;
  private failedTests = 0;
  private testResults: Array<{ name: string; passed: boolean; error?: string }> = [];

  test(name: string, testFn: () => void | boolean): void {
    try {
      const result = testFn();
      const passed = result !== false;
      
      if (passed) {
        this.passedTests++;
        console.log(`✅ ${name}`);
      } else {
        this.failedTests++;
        console.log(`❌ ${name}`);
      }
      
      this.testResults.push({ name, passed });
    } catch (error) {
      this.failedTests++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log(`❌ ${name} - Error: ${errorMessage}`);
      this.testResults.push({ name, passed: false, error: errorMessage });
    }
  }

  expect(actual: any): {
    toBe: (expected: any) => boolean;
    toContain: (expected: any) => boolean;
    toBeTruthy: () => boolean;
    toBeFalsy: () => boolean;
    toBeGreaterThan: (expected: number) => boolean;
    toBeLessThan: (expected: number) => boolean;
  } {
    return {
      toBe: (expected: any) => actual === expected,
      toContain: (expected: any) => {
        if (typeof actual === 'string') {
          return actual.includes(expected);
        }
        if (Array.isArray(actual)) {
          return actual.includes(expected);
        }
        return false;
      },
      toBeTruthy: () => !!actual,
      toBeFalsy: () => !actual,
      toBeGreaterThan: (expected: number) => actual > expected,
      toBeLessThan: (expected: number) => actual < expected
    };
  }

  summary(): void {
    console.log('\n' + '='.repeat(50));
    console.log(`Test Summary: ${this.passedTests} passed, ${this.failedTests} failed`);
    console.log('='.repeat(50));
    
    if (this.failedTests > 0) {
      console.log('\nFailed tests:');
      this.testResults
        .filter(result => !result.passed)
        .forEach(result => {
          console.log(`- ${result.name}${result.error ? ` (${result.error})` : ''}`);
        });
    }
  }
}

const tester = new ValidationTester();

// File Upload Schema Tests
tester.test('FileUploadSchema - valid data', () => {
  const validData = {
    fileData: 'UEsDBBQAAAAIAA==',
    fileName: 'test.xlsx',
    fileSize: 1024,
    operation: 'analyze' as const
  };
  
  const result = FileUploadSchema.safeParse(validData);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('FileUploadSchema - invalid file extension', () => {
  const invalidData = {
    fileData: 'UEsDBBQAAAAIAA==',
    fileName: 'test.pdf',
    fileSize: 1024
  };
  
  const result = FileUploadSchema.safeParse(invalidData);
  return tester.expect(result.success).toBeFalsy();
});

tester.test('FileUploadSchema - file too large', () => {
  const invalidData = {
    fileData: 'UEsDBBQAAAAIAA==',
    fileName: 'test.xlsx',
    fileSize: 60 * 1024 * 1024 // 60MB
  };
  
  const result = FileUploadSchema.safeParse(invalidData);
  return tester.expect(result.success).toBeFalsy();
});

// Email Form Schema Tests
tester.test('EmailFormSchema - valid data', () => {
  const validData = {
    email: '<EMAIL>',
    name: 'John Doe',
    pricingModel: 'premium',
    frequency: 'weekly',
    urgency: 'high',
    payment: 'credit-card',
    painPoints: 'Excel files keep getting corrupted'
  };
  
  const result = EmailFormSchema.safeParse(validData);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('EmailFormSchema - invalid email', () => {
  const invalidData = {
    email: 'invalid-email',
    name: 'John Doe'
  };
  
  const result = EmailFormSchema.safeParse(invalidData);
  return tester.expect(result.success).toBeFalsy();
});

tester.test('EmailFormSchema - email normalization', () => {
  const data = {
    email: '  <EMAIL>  ',
    name: 'John Doe'
  };
  
  const result = EmailFormSchema.safeParse(data);
  if (result.success) {
    return tester.expect(result.data.email).toBe('<EMAIL>');
  }
  return false;
});

// Repair Report Schema Tests
tester.test('RepairReportSchema - valid report', () => {
  const validReport = {
    summary: 'Repair completed successfully',
    sections: [
      {
        title: 'Issues Found',
        issues: [
          {
            type: 'Formula Error',
            location: 'Sheet1!A1',
            description: 'Invalid formula reference',
            actionTaken: 'Fixed formula reference',
            severity: 'Major' as const
          }
        ]
      }
    ],
    performanceMetrics: {
      analysisTime: '2.5s',
      repairTime: '1.2s'
    }
  };
  
  const result = RepairReportSchema.safeParse(validReport);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('RepairReportSchema - invalid severity', () => {
  const invalidReport = {
    summary: 'Repair completed',
    sections: [
      {
        title: 'Issues',
        issues: [
          {
            type: 'Error',
            description: 'Test error',
            actionTaken: 'Fixed',
            severity: 'Invalid' // Invalid severity
          }
        ]
      }
    ],
    performanceMetrics: {
      analysisTime: '1s'
    }
  };
  
  const result = RepairReportSchema.safeParse(invalidReport);
  return tester.expect(result.success).toBeFalsy();
});

// File Processing Response Tests
tester.test('FileProcessingSuccessSchema - valid success response', () => {
  const successResponse = {
    success: true as const,
    repairedFileData: 'UEsDBBQAAAAIAA==',
    originalFileName: 'test.xlsx',
    repairedFileName: 'test_repaired.xlsx',
    repairSummary: {
      summary: 'Success',
      sections: [],
      performanceMetrics: {
        analysisTime: '1s'
      }
    }
  };
  
  const result = FileProcessingSuccessSchema.safeParse(successResponse);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('FileProcessingErrorSchema - valid error response', () => {
  const errorResponse = {
    success: false as const,
    error: 'File processing failed',
    details: 'Invalid file format'
  };
  
  const result = FileProcessingErrorSchema.safeParse(errorResponse);
  return tester.expect(result.success).toBeTruthy();
});

// App Config Schema Tests
tester.test('AppConfigSchema - valid configuration', () => {
  const validConfig = {
    maxFileSize: 50 * 1024 * 1024,
    allowedFileTypes: ['.xlsx', '.xls'],
    apiTimeout: 30000,
    rateLimits: {
      fileProcessing: 5,
      emailForm: 3
    },
    features: {
      offlineSupport: true,
      analytics: false,
      errorReporting: true
    }
  };
  
  const result = AppConfigSchema.safeParse(validConfig);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('AppConfigSchema - negative values rejected', () => {
  const invalidConfig = {
    maxFileSize: -1,
    allowedFileTypes: ['.xlsx'],
    apiTimeout: 30000,
    rateLimits: {
      fileProcessing: 5,
      emailForm: 3
    },
    features: {
      offlineSupport: true,
      analytics: false,
      errorReporting: true
    }
  };
  
  const result = AppConfigSchema.safeParse(invalidConfig);
  return tester.expect(result.success).toBeFalsy();
});

// Environment Schema Tests
tester.test('EnvironmentSchema - valid environment', () => {
  const validEnv = {
    RESEND_API_KEY: 'test-api-key',
    TO_EMAIL_ADDRESS: '<EMAIL>',
    FROM_EMAIL_ADDRESS: '<EMAIL>',
    NODE_ENV: 'development' as const
  };
  
  const result = EnvironmentSchema.safeParse(validEnv);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('EnvironmentSchema - invalid email addresses', () => {
  const invalidEnv = {
    RESEND_API_KEY: 'test-api-key',
    TO_EMAIL_ADDRESS: 'invalid-email',
    FROM_EMAIL_ADDRESS: '<EMAIL>'
  };
  
  const result = EnvironmentSchema.safeParse(invalidEnv);
  return tester.expect(result.success).toBeFalsy();
});

// Validation Helper Tests
tester.test('validateInput - success case', () => {
  const result = validateInput(EmailFormSchema, {
    email: '<EMAIL>',
    name: 'John Doe'
  });
  
  return tester.expect(result.success).toBeTruthy();
});

tester.test('validateInput - error case', () => {
  const result = validateInput(EmailFormSchema, {
    email: 'invalid-email',
    name: 'John Doe'
  });
  
  return tester.expect(result.success).toBeFalsy();
});

tester.test('validateInputWithDetails - detailed errors', () => {
  const result = validateInputWithDetails(FileUploadSchema, {
    fileData: '',
    fileName: 'test.pdf',
    fileSize: -1
  });
  
  if (!result.success) {
    return tester.expect(result.error).toContain('fileData') &&
           tester.expect(result.error).toContain('fileName') &&
           tester.expect(result.error).toContain('fileSize');
  }
  return false;
});

// Type Guard Tests
tester.test('isValidRepairReport - valid report', () => {
  const validReport = {
    summary: 'Test',
    sections: [],
    performanceMetrics: {
      analysisTime: '1s'
    }
  };
  
  return tester.expect(isValidRepairReport(validReport)).toBeTruthy();
});

tester.test('isValidRepairReport - invalid report', () => {
  const invalidReport = {
    summary: 123, // Should be string
    sections: [],
    performanceMetrics: {
      analysisTime: '1s'
    }
  };
  
  return tester.expect(isValidRepairReport(invalidReport)).toBeFalsy();
});

tester.test('isValidFileProcessingResponse - success response', () => {
  const validResponse = {
    success: true,
    message: 'Success'
  };
  
  return tester.expect(isValidFileProcessingResponse(validResponse)).toBeTruthy();
});

tester.test('isValidFileProcessingResponse - error response', () => {
  const validResponse = {
    success: false,
    error: 'Error occurred'
  };
  
  return tester.expect(isValidFileProcessingResponse(validResponse)).toBeTruthy();
});

tester.test('isValidFileProcessingResponse - invalid response', () => {
  const invalidResponse = {
    success: 'maybe' // Should be boolean
  };
  
  return tester.expect(isValidFileProcessingResponse(invalidResponse)).toBeFalsy();
});

// Sanitization Tests
tester.test('sanitizeString - removes HTML and control chars', () => {
  const input = '  <script>alert("xss")</script>Hello\x00World  ';
  const result = sanitizeString(input);
  return tester.expect(result).toBe('scriptalert("xss")/scriptHelloWorld');
});

tester.test('sanitizeString - trims whitespace', () => {
  const input = '  Hello World  ';
  const result = sanitizeString(input);
  return tester.expect(result).toBe('Hello World');
});

tester.test('sanitizeEmail - lowercase and trim', () => {
  const input = '  <EMAIL>  ';
  const result = sanitizeEmail(input);
  return tester.expect(result).toBe('<EMAIL>');
});

tester.test('sanitizeEmail - removes invalid characters', () => {
  const input = 'test<script>@example.com';
  const result = sanitizeEmail(input);
  return tester.expect(result).toBe('<EMAIL>');
});

// File Validation Tests
tester.test('validateFileExtension - accepts .xlsx', () => {
  return tester.expect(validateFileExtension('test.xlsx')).toBeTruthy() &&
         tester.expect(validateFileExtension('TEST.XLSX')).toBeTruthy();
});

tester.test('validateFileExtension - rejects non-.xlsx', () => {
  return tester.expect(validateFileExtension('test.pdf')).toBeFalsy() &&
         tester.expect(validateFileExtension('test.xls')).toBeFalsy() &&
         tester.expect(validateFileExtension('test.txt')).toBeFalsy();
});

tester.test('validateFileSize - accepts valid sizes', () => {
  return tester.expect(validateFileSize(1024)).toBeTruthy() &&
         tester.expect(validateFileSize(50 * 1024 * 1024)).toBeTruthy(); // 50MB
});

tester.test('validateFileSize - rejects invalid sizes', () => {
  return tester.expect(validateFileSize(0)).toBeFalsy() &&
         tester.expect(validateFileSize(-1)).toBeFalsy() &&
         tester.expect(validateFileSize(60 * 1024 * 1024)).toBeFalsy(); // 60MB
});

tester.test('validateBase64 - accepts valid base64', () => {
  return tester.expect(validateBase64('UEsDBBQAAAAIAA==')).toBeTruthy() &&
         tester.expect(validateBase64('SGVsbG8gV29ybGQ=')).toBeTruthy();
});

tester.test('validateBase64 - rejects invalid base64', () => {
  return tester.expect(validateBase64('invalid-base64!')).toBeFalsy() &&
         tester.expect(validateBase64('SGVsbG8gV29ybGQ')).toBeFalsy(); // Missing padding
});

// Edge Cases and Security Tests
tester.test('EmailFormSchema - extremely long strings rejected', () => {
  const longString = 'a'.repeat(10000);
  const result = EmailFormSchema.safeParse({
    email: '<EMAIL>',
    name: longString
  });
  
  return tester.expect(result.success).toBeFalsy();
});

tester.test('FileUploadSchema - null/undefined values rejected', () => {
  const result1 = FileUploadSchema.safeParse(null);
  const result2 = FileUploadSchema.safeParse(undefined);
  const result3 = FileUploadSchema.safeParse({});
  
  return tester.expect(result1.success).toBeFalsy() &&
         tester.expect(result2.success).toBeFalsy() &&
         tester.expect(result3.success).toBeFalsy();
});

tester.test('EmailFormSchema - malicious input rejected', () => {
  const maliciousData = {
    email: '<EMAIL>',
    name: '"><script>alert("xss")</script>',
    painPoints: 'DROP TABLE users; --'
  };
  
  const result = EmailFormSchema.safeParse(maliciousData);
  return tester.expect(result.success).toBeFalsy(); // Should fail due to invalid name characters
});

tester.test('EmailFormSchema - unicode characters accepted', () => {
  const unicodeData = {
    email: '<EMAIL>',
    name: 'José María O\'Connor-Smith'
  };
  
  const result = EmailFormSchema.safeParse(unicodeData);
  return tester.expect(result.success).toBeTruthy();
});

// Performance Tests
tester.test('FileUploadSchema - large valid data performance', () => {
  const largeValidData = {
    fileData: 'U'.repeat(1000) + '==', // Large but valid base64
    fileName: 'large-file.xlsx',
    fileSize: 1024 * 1024 // 1MB
  };
  
  const start = performance.now();
  const result = FileUploadSchema.safeParse(largeValidData);
  const end = performance.now();
  
  return tester.expect(result.success).toBeTruthy() &&
         tester.expect(end - start).toBeLessThan(100); // Should complete in under 100ms
});

tester.test('FileUploadSchema - invalid data memory efficiency', () => {
  const invalidData = {
    fileData: 'invalid'.repeat(10000),
    fileName: 'test.xlsx',
    fileSize: 1024
  };
  
  // This should fail validation without consuming excessive memory
  const result = FileUploadSchema.safeParse(invalidData);
  return tester.expect(result.success).toBeFalsy();
});

// Run all tests and show summary
console.log('🧪 Running Validation Schema Tests...\n');
tester.summary();

// Export for potential use in other files
export { ValidationTester };