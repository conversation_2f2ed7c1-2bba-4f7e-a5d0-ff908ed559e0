import React, { useEffect, useState } from 'react';

interface ConfettiProps {
  active: boolean;
  duration?: number;
}

interface ConfettiPiece {
  id: number;
  x: number;
  y: number;
  rotation: number;
  color: string;
  size: number;
  velocityX: number;
  velocityY: number;
  rotationSpeed: number;
}

const Confetti: React.FC<ConfettiProps> = ({ active, duration = 3000 }) => {
  const [pieces, setPieces] = useState<ConfettiPiece[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  const colors = [
    '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
    '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43'
  ];

  const createConfettiPiece = (id: number): ConfettiPiece => {
    return {
      id,
      x: Math.random() * window.innerWidth,
      y: -10,
      rotation: Math.random() * 360,
      color: colors[Math.floor(Math.random() * colors.length)],
      size: Math.random() * 8 + 4,
      velocityX: (Math.random() - 0.5) * 4,
      velocityY: Math.random() * 3 + 2,
      rotationSpeed: (Math.random() - 0.5) * 10
    };
  };

  useEffect(() => {
    if (!active) {
      setIsVisible(false);
      setPieces([]);
      return;
    }

    setIsVisible(true);
    
    // Create initial confetti pieces
    const initialPieces = Array.from({ length: 50 }, (_, i) => createConfettiPiece(i));
    setPieces(initialPieces);

    // Animation loop
    const animationInterval = setInterval(() => {
      setPieces(prevPieces => 
        prevPieces.map(piece => ({
          ...piece,
          x: piece.x + piece.velocityX,
          y: piece.y + piece.velocityY,
          rotation: piece.rotation + piece.rotationSpeed,
          velocityY: piece.velocityY + 0.1 // gravity
        })).filter(piece => piece.y < window.innerHeight + 10)
      );
    }, 16);

    // Clean up after duration
    const cleanup = setTimeout(() => {
      setIsVisible(false);
      clearInterval(animationInterval);
      setTimeout(() => setPieces([]), 500);
    }, duration);

    return () => {
      clearInterval(animationInterval);
      clearTimeout(cleanup);
    };
  }, [active, duration]);

  if (!isVisible || pieces.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-[9999] overflow-hidden">
      {pieces.map(piece => (
        <div
          key={piece.id}
          className="absolute transition-opacity duration-500"
          style={{
            left: `${piece.x}px`,
            top: `${piece.y}px`,
            width: `${piece.size}px`,
            height: `${piece.size}px`,
            backgroundColor: piece.color,
            transform: `rotate(${piece.rotation}deg)`,
            borderRadius: Math.random() > 0.5 ? '50%' : '0%',
            opacity: isVisible ? 1 : 0
          }}
        />
      ))}
    </div>
  );
};

export default Confetti;