import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { ExcelRepairService } from '../lib/exceljsRepair.mjs';
import ExcelJS from 'exceljs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runTest() {
  try {
    // 1. Read a test file
    const testFilePath = path.resolve(__dirname, '../testfiles/test-normal.xlsx');
    const buffer = fs.readFileSync(testFilePath);
    console.log('Successfully read test file.');

    // 2. Process the file with the exceljs repair service
    console.log('Processing file with exceljs repair service...');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);
    const repairResult = await ExcelRepairService.repairWorkbook(workbook);
    console.log('File processed successfully.');

    // 3. Write the processed file to a new file for verification
    if (repairResult.success && repairResult.repairedWorkbook) {
      const outputFilePath = path.resolve(__dirname, '../testfiles/test-repaired-with-exceljs.xlsx');
      const repairedBuffer = await repairResult.repairedWorkbook.xlsx.writeBuffer();
      fs.writeFileSync(outputFilePath, Buffer.from(repairedBuffer));
      console.log(`Successfully wrote repaired file to: ${outputFilePath}`);
    } else {
      console.error('Repair failed:', repairResult.repairLog);
    }

    console.log('\nTest complete. Please inspect the output file to verify the results.');

  } catch (error) {
    console.error('An error occurred during the test:', error);
  }
}

runTest();
