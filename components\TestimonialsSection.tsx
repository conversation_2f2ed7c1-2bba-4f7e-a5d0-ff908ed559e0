import { motion } from 'framer-motion';
import Icon from './Icon';



interface ProblemSolution {
  problem: string;
  solution: string;
  icon: string;
  stat: string;
}

const problemSolutions: ProblemSolution[] = [
  {
    problem: "Corrupted files blocking critical deadlines",
    solution: "Instant repair in under 5 seconds",
    icon: "fas fa-clock",
    stat: "95% success rate"
  },
  {
    problem: "Lost hours trying manual recovery methods",
    solution: "Automated repair with AI diagnostics",
    icon: "fas fa-robot",
    stat: "Zero data loss"
  },
  {
    problem: "Complex files with macros won't open",
    solution: "VBA-compatible repair technology",
    icon: "fas fa-code",
    stat: "All formats supported"
  }
];

const TestimonialsSection: React.FC = () => {
  

  return (
    <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Solving Real Spreadsheet Problems</h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Built to address the most common Excel corruption issues professionals face daily
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {problemSolutions.map((item, index) => (
            <motion.div
              key={index}
              className="bg-white/20 backdrop-blur-xl p-6 rounded-xl border border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-300 hover:border-green-300/50"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -5 }}
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-red-100/50 backdrop-blur-xl rounded-full flex items-center justify-center mr-4 border border-white/30">
                  <Icon iconClass={item.icon} className="text-red-600 text-lg" />
                </div>
                <div className="bg-green-100/50 backdrop-blur-xl px-3 py-1 rounded-full border border-white/30">
                  <span className="text-green-700 font-semibold text-sm">{item.stat}</span>
                </div>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Problem:</h3>
              <p className="text-gray-700 mb-4">{item.problem}</p>
              <h3 className="font-semibold text-green-600 mb-2">Our Solution:</h3>
              <p className="text-gray-700">{item.solution}</p>
            </motion.div>
          ))}
        </div>
        
        {/* Use case badges */}
        <div className="text-center">
          <p className="text-gray-500 mb-6">Perfect for professionals in</p>
          <div className="flex flex-wrap justify-center items-center gap-4 opacity-70">
            <div className="bg-blue-100/50 backdrop-blur-xl px-4 py-2 rounded-lg border border-white/30">
              <span className="font-semibold text-blue-700">Finance & Accounting</span>
            </div>
            <div className="bg-purple-100/50 backdrop-blur-xl px-4 py-2 rounded-lg border border-white/30">
              <span className="font-semibold text-purple-700">Data Analysis</span>
            </div>
            <div className="bg-green-100/50 backdrop-blur-xl px-4 py-2 rounded-lg border border-white/30">
              <span className="font-semibold text-green-700">Operations</span>
            </div>
            <div className="bg-orange-100/50 backdrop-blur-xl px-4 py-2 rounded-lg border border-white/30">
              <span className="font-semibold text-orange-700">Research</span>
            </div>
            <div className="bg-indigo-100/50 backdrop-blur-xl px-4 py-2 rounded-lg border border-white/30">
              <span className="font-semibold text-indigo-700">Project Management</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;