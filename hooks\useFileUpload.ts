import { useState, useCallback, useRef, useEffect } from 'react';
import ExcelJS from 'exceljs';
import { MemoryManager } from '../lib/memoryManager';
import { analyzeFile, ApiTimeoutError } from '../lib/apiClient';
import { useProgressiveLoader, LoadingSequences } from '../lib/loadingStates';
import { EnhancedErrorHandler, createFileError, createNetworkError, createProcessingError } from '../lib/errorMessages';
import { ProcessedFileResult } from '../components/FileUpload';
import { log } from '../lib/logger';

const SUPPORTED_FORMATS = ['.xlsx'];

interface UseFileUploadOptions {
  onFileProcessed?: (result: ProcessedFileResult) => void;
  onError?: (error: any) => void;
}

interface FileUploadState {
  isDragOver: boolean;
  dragDepth: number;
  draggedFileType: string | null;
  isValidDrop: boolean;
  isProcessing: boolean;
  uploadedFile: File | null;
  processingStatus: string;
  processingProgress: number;
  memoryUsage: { percentage: number; available: string } | null;
  currentError: any;
  showDetailedProgress: boolean;
  currentOperation: 'analyze' | 'repair';
}

export const useFileUpload = (options: UseFileUploadOptions = {}) => {
  const { onFileProcessed, onError } = options;

  const [state, setState] = useState<FileUploadState>({
    isDragOver: false,
    dragDepth: 0,
    draggedFileType: null,
    isValidDrop: true,
    isProcessing: false,
    uploadedFile: null,
    processingStatus: '',
    processingProgress: 0,
    memoryUsage: null,
    currentError: null,
    showDetailedProgress: false,
    currentOperation: 'analyze'
  });

  const processingSessionRef = useRef<{ processId: string; cleanup: () => void; monitor: (callback: (stats: any) => void) => NodeJS.Timeout } | null>(null);
  const progressiveLoader = useProgressiveLoader();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (processingSessionRef.current) {
        processingSessionRef.current.cleanup();
        processingSessionRef.current = null;
      }
    };
  }, []);

  const updateState = useCallback((updates: Partial<FileUploadState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const validateFile = useCallback((file: File): { isValid: boolean; error?: any } => {
    // Use memory manager to check if file can be processed
    const canProcess = MemoryManager.canProcessFile(file.size);
    if (!canProcess.allowed) {
      const error = createFileError(
        canProcess.reason || 'File cannot be processed due to memory constraints.',
        file.name,
        file.size
      );
      return { isValid: false, error };
    }

    // Check for minimum file size (empty files)
    if (file.size < 100) {
      const error = createFileError(
        'File appears to be empty or too small to be a valid Excel file.',
        file.name,
        file.size
      );
      return { isValid: false, error };
    }

    // Check file extension
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!SUPPORTED_FORMATS.includes(fileExtension)) {
      const error = createFileError(
        'Unsupported file format. We only support .xlsx files currently.',
        file.name,
        file.size
      );
      return { isValid: false, error };
    }

    return { isValid: true };
  }, []);

  const analyzeWorkbook = useCallback((workbook: ExcelJS.Workbook): ProcessedFileResult['sheets'] => {
    // Get memory-aware analysis limits
    const analysisLimits = MemoryManager.getCellAnalysisLimits();

    return workbook.worksheets.slice(0, analysisLimits.maxSheets).map(worksheet => {
      const sheetName = worksheet.name;
      try {
        // Handle missing or corrupted worksheets
        if (!worksheet) {
          return {
            name: sheetName,
            cellCount: 0,
            hasFormulas: false,
            errors: []
          };
        }

        // Safe range calculation for ExcelJS
        let cellCount = 0;
        try {
          // ExcelJS uses different structure - count actual cells
          let maxRow = 0;
          let maxCol = 0;

          worksheet.eachRow((row, rowNumber) => {
            maxRow = Math.max(maxRow, rowNumber);
            row.eachCell((cell, colNumber) => {
              maxCol = Math.max(maxCol, colNumber);
            });
          });

          cellCount = maxRow * maxCol;
        } catch (rangeError) {
          console.warn(`Range calculation failed for sheet ${sheetName}:`, rangeError);
          // Fallback: count by iterating
          cellCount = 0;
          try {
            worksheet.eachRow((row) => {
              row.eachCell(() => {
                cellCount++;
              });
            });
          } catch {
            cellCount = 0;
          }
        }

        let hasFormulas = false;
        const errors: string[] = [];

        // Check for formulas and errors using ExcelJS structure
        let cellsChecked = 0;
        const maxCellsToCheck = Math.min(analysisLimits.maxCells, 5000);

        worksheet.eachRow((row, rowNumber) => {
          if (cellsChecked >= maxCellsToCheck) return;

          row.eachCell((cell, colNumber) => {
            if (cellsChecked >= maxCellsToCheck) return;
            cellsChecked++;

            try {
              // Check for formulas
              if (cell.type === ExcelJS.ValueType.Formula) {
                hasFormulas = true;

                // Check for formula errors
                if (typeof cell.value === 'object' && cell.value && 'error' in cell.value) {
                  const cellAddress = `${String.fromCharCode(64 + colNumber)}${rowNumber}`;
                  errors.push(`Sheet ${sheetName}, Cell ${cellAddress}: Formula error - ${cell.formula}`);
                  console.log(`Detected formula error in ${sheetName}!${cellAddress}: ${cell.formula}`);
                }
              }

              // Check for error values
              if (cell.type === ExcelJS.ValueType.Error) {
                const cellAddress = `${String.fromCharCode(64 + colNumber)}${rowNumber}`;
                const errorValue = cell.value || '#ERROR!';
                errors.push(`Sheet ${sheetName}, Cell ${cellAddress}: ${errorValue}`);
                console.log(`Detected Excel error in ${sheetName}!${cellAddress}: ${errorValue}`);
              }

            } catch (_error) {
              console.warn(`Error accessing cell at row ${rowNumber}, col ${colNumber} in sheet ${sheetName}:`, _error);
            }
          });
        });

        console.log(`Sheet ${sheetName} analysis: hasFormulas=${hasFormulas}, errors=${errors.length}`);
        return {
          name: sheetName,
          cellCount,
          hasFormulas,
          errors
        };
      } catch (sheetError) {
        console.warn(`Analysis failed for sheet ${sheetName}:`, sheetError);
        return {
          name: sheetName,
          cellCount: 0,
          hasFormulas: false,
          errors: []
        };
      }
    });
  }, []);

  const processFile = useCallback(async (file: File) => {
    log.fileUpload.start(file.name, file.size);
    const startTime = Date.now();

    updateState({
      isProcessing: true,
      processingProgress: 0,
      currentError: null,
      showDetailedProgress: true,
      currentOperation: 'analyze'
    });

    // Start progressive loading
    progressiveLoader.start(LoadingSequences.fileUpload);

    // Create processing session with memory management
    let processingSession;
    try {
      processingSession = MemoryManager.createProcessingSession(file.size);
      processingSessionRef.current = processingSession;

      // Start memory monitoring
      const memoryMonitor = processingSession.monitor((stats) => {
        updateState({
          memoryUsage: {
            percentage: Math.round(stats.percentage),
            available: MemoryManager.formatBytes(stats.available)
          }
        });
      });

      // Step 1: Upload
      progressiveLoader.updateStepProgress(50, 'Securely uploading your file...');
      updateState({ processingStatus: '📤 Securely uploading your file...', processingProgress: 10 });
      await new Promise(resolve => setTimeout(resolve, 800));
      progressiveLoader.updateStepProgress(100);
      progressiveLoader.nextStep();

      // Step 2: Validate
      progressiveLoader.updateStepProgress(30, 'Checking file format and integrity...');
      updateState({ processingStatus: '🔍 Getting ready to check your file...', processingProgress: 20 });
      await new Promise(resolve => setTimeout(resolve, 600));
      progressiveLoader.updateStepProgress(100);
      progressiveLoader.nextStep();

      // Step 3: Analyze
      progressiveLoader.updateStepProgress(20, 'Scanning for issues and errors...');
      updateState({ processingStatus: '🔬 Checking your file for problems...', processingProgress: 30 });

      const arrayBuffer = await file.arrayBuffer();
      const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

      progressiveLoader.updateStepProgress(60, 'Analyzing file content...');
      updateState({ processingProgress: 70, processingStatus: '📋 Getting your results ready...' });

      progressiveLoader.updateStepProgress(80, 'Processing analysis results...');

      // Use API client with timeout support for analysis
      const apiResponse = await analyzeFile('/api/excel-processor', base64Data, file.name, file.size, 'analyze');

      progressiveLoader.updateStepProgress(100);
      progressiveLoader.nextStep();

      if (!apiResponse.success) {
        // Handle specific error cases for better user experience
        const errorMessage = apiResponse.error || 'Something went wrong while checking your file.';

        // If the error indicates corruption but we can still try to repair
        if (errorMessage.includes('corrupted') || errorMessage.includes('damaged') ||
            errorMessage.includes('structural issues') || errorMessage.includes('data structures')) {
          // Don't throw an error - instead create a result that shows issues found
          console.log('🔧 File has issues but may be repairable:', errorMessage);

          const result: ProcessedFileResult = {
            originalFile: file,
            workbook: null,
            fileInfo: {
              name: file.name,
              size: file.size,
              type: file.type,
              lastModified: new Date(file.lastModified)
            },
            sheets: [{
              name: 'File Analysis',
              cellCount: 0,
              hasFormulas: false,
              errors: ['File corruption detected']
            }],
            status: 'warning',
            message: errorMessage,
            repairReport: {
              summary: errorMessage,
              sections: [{
                title: 'File Corruption Detected',
                issues: [{
                  type: 'Corruption',
                  description: errorMessage,
                  actionTaken: 'We can try to repair this file',
                  severity: 'Major'
                }]
              }],
              performanceMetrics: {
                analysisTime: '0s',
                repairTime: 'N/A'
              }
            }
          };

          const duration = Date.now() - startTime;
          log.fileUpload.complete(file.name, duration);
          console.log('⚠️ File analysis completed with warnings:', result);
          onFileProcessed?.(result);
          return;
        }

        throw new Error(errorMessage);
      }

      // Step 4: Process results
      progressiveLoader.updateStepProgress(50, 'Preparing your analysis report...');
      updateState({ processingProgress: 90, processingStatus: '✨ Almost done...' });

      // The backend now returns a comprehensive repairSummary (RepairReport)
      const repairReport = apiResponse.data?.repairSummary;
      const repairedWorkbookData = apiResponse.data?.repairedFileData;

      let finalWorkbook: ExcelJS.Workbook | null = null;
      if (repairedWorkbookData) {
        const binaryString = atob(repairedWorkbookData);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        finalWorkbook = new ExcelJS.Workbook();
        await finalWorkbook.xlsx.load(bytes.buffer);
      }

      // Analyze the original file for display statistics
      let originalWorkbook: ExcelJS.Workbook | null = null;
      let sheets: ProcessedFileResult['sheets'] = [];

      try {
        console.log('📋 Starting original file analysis...');
        const originalArrayBuffer = await file.arrayBuffer();
        originalWorkbook = new ExcelJS.Workbook();
        await originalWorkbook.xlsx.load(originalArrayBuffer);

        if (originalWorkbook && originalWorkbook.worksheets && originalWorkbook.worksheets.length > 0) {
          sheets = analyzeWorkbook(originalWorkbook);
          console.log('📊 Original file analysis successful:', {
            hasWorkbook: !!originalWorkbook,
            sheetNames: originalWorkbook.worksheets.map(ws => ws.name),
            sheetsAnalyzed: sheets.length,
            sheetsData: sheets
          });
        } else {
          console.warn('⚠️ Original workbook has no sheets or is corrupted');
          throw new Error('Invalid workbook structure');
        }
      } catch (analysisError) {
        console.warn('⚠️ Could not analyze original file:', analysisError);
        console.log('🔄 Falling back to repaired workbook analysis...');

        // Fallback to repaired workbook if original can't be analyzed
        if (finalWorkbook && finalWorkbook.worksheets && finalWorkbook.worksheets.length > 0) {
          sheets = analyzeWorkbook(finalWorkbook);
          console.log('📊 Fallback analysis successful:', {
            hasWorkbook: !!finalWorkbook,
            sheetNames: finalWorkbook.worksheets.map(ws => ws.name),
            sheetsAnalyzed: sheets.length
          });
        } else {
          console.error('❌ Both original and repaired workbooks failed analysis');
          // Create minimal fallback data
          sheets = [{
            name: 'Sheet1',
            cellCount: 1,
            hasFormulas: false,
            errors: []
          }];
        }
      }

      let status: ProcessedFileResult['status'];
      let message: string;

      if (apiResponse.success) {
        status = 'success';
        message = repairReport?.summary || 'File processed successfully.';
      } else {
        status = 'error';
        message = repairReport?.summary || apiResponse.error || 'File processing failed.';
      }

      progressiveLoader.updateStepProgress(100, 'Analysis complete!');
      updateState({ processingProgress: 100, processingStatus: '✅ File check complete!' });

      // Complete progressive loading
      progressiveLoader.complete();
      await new Promise(resolve => setTimeout(resolve, 200));

      const result: ProcessedFileResult = {
        originalFile: file,
        workbook: originalWorkbook || finalWorkbook,
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: new Date(file.lastModified)
        },
        sheets,
        status,
        message,
        repairReport: repairReport,
      };

      const duration = Date.now() - startTime;
      log.fileUpload.complete(file.name, duration);
      log.fileRepair.complete(file.name, 'analyze', result);
      console.log('✅ File analysis complete:', result);
      onFileProcessed?.(result);

    } catch (error: unknown) {
      log.fileUpload.error(file.name, error instanceof Error ? error : new Error(String(error)));
      console.error('❌ File processing failed:', error);

      // Create enhanced error with context
      let enhancedError;
      if (error instanceof ApiTimeoutError) {
        enhancedError = createNetworkError(
          'File processing timed out. Please try again with a smaller file or check your internet connection.',
          'analyze'
        );
      } else {
        enhancedError = createProcessingError(
          error instanceof Error ? error.message : 'Failed to process file',
          file.name,
          progressiveLoader.currentStep || undefined
        );
      }

      // Log error for debugging
      const errorContext: any = {
        fileName: file.name,
        fileSize: file.size,
        operation: 'analyze'
      };
      if (progressiveLoader.currentStep) {
        errorContext.step = progressiveLoader.currentStep;
      }
      EnhancedErrorHandler.logError(error instanceof Error ? error : new Error(String(error)), errorContext);

      updateState({ currentError: enhancedError, processingStatus: 'Processing failed', processingProgress: 100 });
      onError?.(enhancedError);
    } finally {
      // Clean up processing session
      if (processingSessionRef.current) {
        processingSessionRef.current.cleanup();
        processingSessionRef.current = null;
      }

      // Ensure progressive loading is completed or cancelled
      if (progressiveLoader.isLoading) {
        progressiveLoader.cancel();
      }

      updateState({ isProcessing: false, memoryUsage: null });
    }
  }, [analyzeWorkbook, onFileProcessed, onError, progressiveLoader, updateState]);

  const handleFileUpload = useCallback((file: File) => {
    // Prevent duplicate processing
    if (state.isProcessing) {
      console.warn('⚠️ Upload already in progress, ignoring duplicate request');
      return;
    }

    log.user('file-upload-initiated', { fileName: file.name, fileSize: file.size, fileType: file.type });

    const validation = validateFile(file);
    if (!validation.isValid) {
      log.error('file-upload', 'File validation failed', validation.error, { fileName: file.name });
      console.error('❌ File validation failed:', validation.error);
      updateState({ currentError: validation.error, uploadedFile: file });
      onError?.(validation.error);
      return;
    }

    updateState({ uploadedFile: file, currentError: null });
    processFile(file);
  }, [validateFile, processFile, updateState, onError, state.isProcessing]);

  const reset = useCallback(() => {
    console.log('🧹 Resetting file upload state');

    // Clean up any active processing session
    if (processingSessionRef.current) {
      try {
        processingSessionRef.current.cleanup();
      } catch (error) {
        console.warn('Error cleaning up processing session:', error);
      } finally {
        processingSessionRef.current = null;
      }
    }

    setState({
      isDragOver: false,
      dragDepth: 0,
      draggedFileType: null,
      isValidDrop: true,
      isProcessing: false,
      uploadedFile: null,
      processingStatus: '',
      processingProgress: 0,
      memoryUsage: null,
      currentError: null,
      showDetailedProgress: false,
      currentOperation: 'analyze'
    });

    // Cancel progressive loading
    try {
      progressiveLoader.cancel();
    } catch (error) {
      console.warn('Error canceling progressive loader:', error);
    }
  }, [progressiveLoader]);

  // Drag and drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Prevent processing if already in progress
    if (state.isProcessing) {
      return;
    }

    updateState({ dragDepth: state.dragDepth + 1 });

    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      const item = e.dataTransfer.items[0];
      if (item.kind === 'file') {
        const fileType = item.type;
        updateState({ draggedFileType: fileType });

        // Check if it's a valid Excel file
        const isValid = fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                       fileType === 'application/vnd.ms-excel';
        updateState({ isValidDrop: isValid, isDragOver: true });
      }
    } else {
      // Fallback when we can't detect file type during drag
      updateState({ isDragOver: true, isValidDrop: true });
    }
  }, [state.dragDepth, state.isProcessing, updateState]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const newDepth = state.dragDepth - 1;
    updateState({ dragDepth: newDepth });

    if (newDepth === 0) {
      updateState({
        isDragOver: false,
        draggedFileType: null,
        isValidDrop: true
      });
    }
  }, [state.dragDepth, updateState]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Update drag effect based on validity
    e.dataTransfer.dropEffect = state.isValidDrop ? 'copy' : 'none';
  }, [state.isValidDrop]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Prevent processing if already in progress
    if (state.isProcessing) {
      console.warn('⚠️ File processing already in progress, ignoring drop');
      return;
    }

    updateState({
      isDragOver: false,
      dragDepth: 0,
      draggedFileType: null,
      isValidDrop: true
    });

    const file = e.dataTransfer.files?.[0];
    if (file) {
      console.log('📁 File dropped:', file.name, file.size, file.type);
      handleFileUpload(file);
    }
  }, [handleFileUpload, updateState, state.isProcessing]);

  return {
    // State
    ...state,

    // Actions
    handleFileUpload,
    reset,
    processFile,

    // Drag and drop handlers
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,

    // Utilities
    validateFile,
    analyzeWorkbook
  };
};