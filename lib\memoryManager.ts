/**
 * Memory Management Utilities for File Processing
 * Provides memory monitoring, limits, and cleanup functionality
 */

export interface MemoryStats {
  used: number;
  total: number;
  percentage: number;
  available: number;
}

export interface ProcessingLimits {
  maxFileSize: number;
  maxMemoryUsage: number;
  maxProcessingTime: number;
  maxCellsToAnalyze: number;
}

export class MemoryManager {
  private static readonly DEFAULT_LIMITS: ProcessingLimits = {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    maxMemoryUsage: 512 * 1024 * 1024, // 512MB
    maxProcessingTime: 5 * 60 * 1000, // 5 minutes
    maxCellsToAnalyze: 10000, // Limit cell analysis for performance
  };

  private static processingTimeouts = new Map<string, NodeJS.Timeout>();
  private static activeProcesses = new Map<string, { startTime: number; fileSize: number }>();

  /**
   * Get current memory usage statistics
   */
  static getMemoryStats(): MemoryStats {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
        available: memory.jsHeapSizeLimit - memory.usedJSHeapSize,
      };
    }

    // Fallback for environments without memory API
    return {
      used: 0,
      total: 0,
      percentage: 0,
      available: this.DEFAULT_LIMITS.maxMemoryUsage,
    };
  }

  /**
   * Check if file processing should be allowed based on memory and size limits
   */
  static canProcessFile(fileSize: number, customLimits?: Partial<ProcessingLimits>): {
    allowed: boolean;
    reason?: string;
    limits: ProcessingLimits;
  } {
    const limits = { ...this.DEFAULT_LIMITS, ...customLimits };
    const memoryStats = this.getMemoryStats();

    // Check file size limit
    if (fileSize > limits.maxFileSize) {
      return {
        allowed: false,
        reason: `File size (${this.formatBytes(fileSize)}) exceeds maximum allowed size (${this.formatBytes(limits.maxFileSize)})`,
        limits,
      };
    }

    // Check available memory
    if (memoryStats.available < fileSize * 3) { // Need at least 3x file size for processing
      return {
        allowed: false,
        reason: `Insufficient memory available. Need at least ${this.formatBytes(fileSize * 3)}, but only ${this.formatBytes(memoryStats.available)} available`,
        limits,
      };
    }

    // Check current memory usage
    if (memoryStats.percentage > 80) {
      return {
        allowed: false,
        reason: `Memory usage too high (${memoryStats.percentage.toFixed(1)}%). Please refresh the page and try again`,
        limits,
      };
    }

    return { allowed: true, limits };
  }

  /**
   * Start tracking a file processing operation
   */
  static startProcessing(processId: string, fileSize: number, customLimits?: Partial<ProcessingLimits>): {
    success: boolean;
    timeoutId?: NodeJS.Timeout;
    error?: string | undefined;
  } {
    const limits = { ...this.DEFAULT_LIMITS, ...customLimits };

    // Check if we can process this file
    const canProcess = this.canProcessFile(fileSize, customLimits);
    if (!canProcess.allowed) {
      const errorResult: { success: false; error?: string | undefined } = { success: false };
      if (canProcess.reason) errorResult.error = canProcess.reason;
      return errorResult;
    }

    // Set up processing timeout
    const timeoutId = setTimeout(() => {
      this.forceCleanup(processId, 'Processing timeout exceeded');
    }, limits.maxProcessingTime);

    this.processingTimeouts.set(processId, timeoutId);
    this.activeProcesses.set(processId, {
      startTime: Date.now(),
      fileSize,
    });

    console.log(`🚀 Started processing ${processId} (${this.formatBytes(fileSize)})`);
    return { success: true, timeoutId };
  }

  /**
   * Clean up resources for a processing operation
   */
  static endProcessing(processId: string): void {
    const timeout = this.processingTimeouts.get(processId);
    if (timeout) {
      clearTimeout(timeout);
      this.processingTimeouts.delete(processId);
    }

    const process = this.activeProcesses.get(processId);
    if (process) {
      const duration = Date.now() - process.startTime;
      console.log(`✅ Completed processing ${processId} in ${duration}ms`);
      this.activeProcesses.delete(processId);
    }

    // Trigger garbage collection if available
    this.requestGarbageCollection();
  }

  /**
   * Force cleanup of a processing operation (e.g., on timeout)
   */
  static forceCleanup(processId: string, reason: string): void {
    console.warn(`🧹 Force cleanup of ${processId}: ${reason}`);
    this.endProcessing(processId);

    // Additional cleanup for forced termination
    this.requestGarbageCollection();
  }

  /**
   * Clean up all active processes (e.g., on page unload)
   */
  static cleanupAll(): void {
    console.log('🧹 Cleaning up all active processes');

    for (const [processId] of this.activeProcesses) {
      this.endProcessing(processId);
    }

    this.processingTimeouts.clear();
    this.activeProcesses.clear();
    this.requestGarbageCollection();
  }

  /**
   * Request garbage collection if available
   */
  static requestGarbageCollection(): void {
    if (typeof window !== 'undefined' && 'gc' in window) {
      try {
        (window as any).gc();
        console.log('🗑️ Garbage collection requested');
      } catch (error) {
        // Garbage collection not available or failed
      }
    }
  }

  /**
   * Get processing limits for cell analysis based on available memory
   */
  static getCellAnalysisLimits(): { maxCells: number; maxSheets: number } {
    const memoryStats = this.getMemoryStats();
    const availableMemoryMB = memoryStats.available / (1024 * 1024);

    // Adjust limits based on available memory
    if (availableMemoryMB > 200) {
      return { maxCells: 15000, maxSheets: 50 };
    } else if (availableMemoryMB > 100) {
      return { maxCells: 10000, maxSheets: 25 };
    } else if (availableMemoryMB > 50) {
      return { maxCells: 5000, maxSheets: 10 };
    } else {
      return { maxCells: 2000, maxSheets: 5 };
    }
  }

  /**
   * Monitor memory usage during processing
   */
  static monitorMemoryUsage(processId: string, callback: (stats: MemoryStats) => void): NodeJS.Timeout {
    return setInterval(() => {
      const stats = this.getMemoryStats();
      callback(stats);

      // Auto-cleanup if memory usage gets too high
      if (stats.percentage > 90) {
        console.warn(`⚠️ High memory usage detected (${stats.percentage.toFixed(1)}%) during ${processId}`);
        this.forceCleanup(processId, 'High memory usage');
      }
    }, 1000); // Check every second
  }

  /**
   * Format bytes to human-readable string
   */
  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Create a processing session with automatic cleanup
   */
  static createProcessingSession(fileSize: number, customLimits?: Partial<ProcessingLimits>) {
    const processId = `process_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startResult = this.startProcessing(processId, fileSize, customLimits);

    if (!startResult.success) {
      throw new Error(startResult.error);
    }

    return {
      processId,
      limits: { ...this.DEFAULT_LIMITS, ...customLimits },
      cleanup: () => this.endProcessing(processId),
      monitor: (callback: (stats: MemoryStats) => void) => this.monitorMemoryUsage(processId, callback),
    };
  }
}

// Set up cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    MemoryManager.cleanupAll();
  });

  window.addEventListener('unload', () => {
    MemoryManager.cleanupAll();
  });
}