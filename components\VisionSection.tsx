import React from 'react';
import Icon from './Icon';

const VisionSection: React.FC = () => {
  const handleGetStarted = () => {
    const signupSection = document.querySelector('#hero-signup');
    if (signupSection) {
      signupSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
      // Fallback: scroll to top if hero signup not found
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return (
    <section className="py-16 sm:py-20 md:py-24 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
          <h2 className="font-display text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-gray-900">
            Ready to fix your Excel files?
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 mb-8 sm:mb-12 max-w-3xl mx-auto">
            Join thousands of users who trust <PERSON><PERSON>H<PERSON><PERSON> to recover their important spreadsheets quickly and securely.
            </p>
          
            <button 
            onClick={handleGetStarted}
                          className="inline-flex items-center bg-gray-900 hover:bg-lime-300 text-white hover:text-gray-900 px-8 py-4 rounded-lg font-medium text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 border border-gray-900"
            >
            <Icon iconClass="fas fa-play mr-3" />
            Start Free
            </button>
          
          <p className="mt-6 text-sm text-gray-500">
            No credit card required • Free tier available • 99.7% success rate
          </p>
        </div>
      </div>
    </section>
  );
};

export default VisionSection;
