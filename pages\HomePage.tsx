import React from 'react';
import HeroSection from '../components/HeroSection';
import HowItWorksSection from '../components/HowItWorksSection';
import FeaturesSection from '../components/FeaturesSection';
import PricingSection from '../components/PricingSection';
import FAQSection from '../components/FAQSection';
import Footer from '../components/Footer';

const HomePage: React.FC = () => {
  return (
    <>
      <main>
        <HeroSection />
        
        <section id="features" className="mt-16 lg:mt-24">
          <FeaturesSection />
        </section>
        
        <section id="how-it-works" className="mt-16 lg:mt-24">
          <HowItWorksSection />
        </section>
        
        <section id="pricing" className="mt-16 lg:mt-24">
          <PricingSection />
        </section>
        
        <section id="faq" className="mt-16 lg:mt-24">
          <FAQSection />
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export default HomePage;