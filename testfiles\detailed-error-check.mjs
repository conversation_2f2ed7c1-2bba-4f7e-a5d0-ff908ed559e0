import fs from 'fs';
import * as XLSX from 'xlsx';

// Improved error detection that shows actual Excel error values
function detectErrorsDetailed(worksheet, sheetName) {
  const issues = [];
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');

  for (let R = range.s.r; R <= range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
      const cell = worksheet[cellAddress];

      if (cell && cell.t === 'e') {
        // Try to get the actual error value
        let errorValue = 'Unknown error';
        
        // Check different properties that might contain the error
        if (cell.v !== undefined && cell.v !== null) {
          errorValue = cell.v;
        } else if (cell.w !== undefined && cell.w !== null) {
          errorValue = cell.w;
        } else if (cell.f !== undefined && cell.f !== null) {
          // Look for error patterns in formula
          if (cell.f.includes('/0')) errorValue = '#DIV/0!';
          else if (cell.f.includes('INVALID') || cell.f.includes('SUMM(')) errorValue = '#NAME?';
          else if (cell.f.includes('#REF!')) errorValue = '#REF!';
          else errorValue = '#ERROR!';
        }
        
        issues.push({
          sheet: sheetName,
          cell: cellAddress,
          errorValue: errorValue,
          cellData: {
            type: cell.t,
            value: cell.v,
            formula: cell.f,
            displayValue: cell.w,
            rawCell: cell
          }
        });
        
        console.log(`🔍 Error cell ${sheetName}!${cellAddress}:`);
        console.log(`   Type: ${cell.t}`);
        console.log(`   Value: ${cell.v}`);
        console.log(`   Formula: ${cell.f}`);
        console.log(`   Display: ${cell.w}`);
        console.log(`   Detected as: ${errorValue}`);
        console.log(`   Raw cell:`, cell);
        console.log('');
      }
    }
  }

  return issues;
}

async function testDetailedErrorDetection(fileName) {
  console.log(`\n🔍 Detailed error analysis for: ${fileName}`);
  
  try {
    const data = fs.readFileSync(fileName);
    const workbook = XLSX.read(data, { 
      type: 'buffer',
      cellDates: true,
      cellNF: true,
      cellStyles: true,
      cellFormula: true,
      sheetStubs: true,
      WTF: true
    });

    console.log(`📊 Workbook loaded with ${workbook.SheetNames.length} sheets: ${workbook.SheetNames.join(', ')}`);

    let totalIssues = 0;
    const allIssues = [];

    for (const sheetName of workbook.SheetNames) {
      console.log(`\n📋 Analyzing sheet: ${sheetName}`);
      const worksheet = workbook.Sheets[sheetName];
      
      const errorIssues = detectErrorsDetailed(worksheet, sheetName);
      allIssues.push(...errorIssues);
      totalIssues += errorIssues.length;
      console.log(`  - Error cells found: ${errorIssues.length}`);
    }

    console.log(`\n📈 Total error cells found: ${totalIssues}`);
    
    if (totalIssues === 0) {
      console.log('✅ No error cells detected - file appears healthy!');
    } else {
      console.log('⚠️ Error cells detected:');
      allIssues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue.sheet}!${issue.cell}: ${issue.errorValue}`);
      });
    }

  } catch (error) {
    console.error('❌ Error testing file:', error.message);
  }
}

// Test both files
console.log('🧪 Starting detailed error detection tests...');

// Test the corrupted file
if (fs.existsSync('test-corrupted-excel.xlsx')) {
  await testDetailedErrorDetection('test-corrupted-excel.xlsx');
} else {
  console.log('⚠️ test-corrupted-excel.xlsx not found');
}

// Test the file with real errors
if (fs.existsSync('test-with-real-errors.xlsx')) {
  await testDetailedErrorDetection('test-with-real-errors.xlsx');
} else {
  console.log('⚠️ test-with-real-errors.xlsx not found');
}

console.log('\n✅ Detailed error detection tests completed!');
