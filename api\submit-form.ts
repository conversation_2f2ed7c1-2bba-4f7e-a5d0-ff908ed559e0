import type { VercelRequest, VercelResponse } from '@vercel/node';
import { Resend } from 'resend';
import {
  EmailFormSchema,
  validateInputWithDetails,
  sanitizeString,
  sanitizeEmail,
  EmailSubmissionSuccessSchema,
  EmailSubmissionErrorSchema
} from '../lib/validation';
import { rateLimitMiddleware, RATE_LIMITS, detectSuspiciousActivity } from '../lib/rateLimiter';
import { validateOrigin } from '../lib/csrf';

const resend = new Resend(process.env.RESEND_API_KEY);
const toEmail = process.env.TO_EMAIL_ADDRESS;
const fromEmail = process.env.FROM_EMAIL_ADDRESS;

export default async function handler(
  req: VercelRequest,
  res: VercelResponse,
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  if (!toEmail || !fromEmail) {
    console.error('Missing TO_EMAIL_ADDRESS or FROM_EMAIL_ADDRESS environment variables');
    return res.status(500).json({ error: 'Server configuration error.' });
  }

  // SECURITY: Rate limiting
  const rateLimitCheck = rateLimitMiddleware('submit-form', RATE_LIMITS.EMAIL_FORM);
  if (!rateLimitCheck(req, res)) {
    return; // Rate limit exceeded, response already sent
  }

  // SECURITY: Detect suspicious activity
  if (detectSuspiciousActivity(req)) {
    return res.status(403).json({
      error: 'Suspicious activity detected'
    });
  }

  // SECURITY: Origin validation (basic CSRF protection)
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'https://sheethealer.com',
    'https://www.sheethealer.com'
  ];

  if (!validateOrigin(req, allowedOrigins)) {
    return res.status(403).json({
      error: 'Invalid origin - request not allowed from this domain'
    });
  }

  try {
    // SECURITY: Validate input using enhanced Zod schema
    const validation = validateInputWithDetails(EmailFormSchema, req.body);

    if (!validation.success) {
      const errorResponse = {
        error: 'Invalid input data',
        details: validation.error
      };

      // Validate error response against schema
      const errorValidation = EmailSubmissionErrorSchema.safeParse(errorResponse);
      if (!errorValidation.success) {
        console.error('Error response validation failed:', errorValidation.error);
      }

      return res.status(400).json(errorResponse);
    }

    const data = validation.data;

    // Additional sanitization
    const sanitizedData = {
      ...data,
      email: sanitizeEmail(data.email),
      name: sanitizeString(data.name),
      painPoints: data.painPoints ? sanitizeString(data.painPoints) : undefined
    };

    // 1. Send notification email to admin
    const sendAdminEmailPromise = resend.emails.send({
      from: `SheetHealer Early Access <${fromEmail}>`,
      to: [toEmail],
      subject: '🚀 New Early Access Signup!',
      html: `
        <div style="font-family: Arial, sans-serif; line-height: 1.6;">
          <h2 style="color: #333;">New Early Access Signup for SheetHealer</h2>
          <p>A new user has signed up for early access. Here are their details:</p>
          <ul>
            <li><strong>Name:</strong> ${sanitizedData.name}</li>
            <li><strong>Email:</strong> ${sanitizedData.email}</li>
            <li><strong>Chosen Pricing Model:</strong> ${sanitizedData.pricingModel || 'Not provided'}</li>
            <li><strong>Usage Frequency:</strong> ${sanitizedData.frequency || 'Not provided'}</li>
            <li><strong>Urgency:</strong> ${sanitizedData.urgency || 'Not provided'}</li>
            <li><strong>Preferred Payment:</strong> ${sanitizedData.payment || 'Not provided'}</li>
            <li><strong>Pain Points:</strong></li>
          </ul>
          <pre style="background-color: #f4f4f4; padding: 10px; border-radius: 5px;">${sanitizedData.painPoints || 'Not provided'}</pre>
        </div>
      `,
    });

    // 2. Send confirmation email to the user
    const sendConfirmationEmailPromise = resend.emails.send({
      from: `SheetHealer <${fromEmail}>`,
      to: [sanitizedData.email],
      subject: "You're on the list! 🎉",
      html: `
        <div style="font-family: Arial, sans-serif; line-height: 1.6;">
          <h2 style="color: #333;">Thanks for signing up, ${sanitizedData.name}!</h2>
          <p>You've been added to the early access waitlist for SheetHealer. We're thrilled to have you on board!</p>
          <p>We'll be in touch soon with updates and your exclusive access.</p>
          <br>
          <p>Best,</p>
          <p>The SheetHealer Team</p>
        </div>
      `
    });

    const [adminEmailResult, userEmailResult] = await Promise.all([
      sendAdminEmailPromise,
      sendConfirmationEmailPromise
    ]);


    if (adminEmailResult.error) {
      console.error('Error sending admin email:', adminEmailResult.error);

      const errorResponse = { error: 'Failed to send admin notification.' };

      // Validate error response against schema
      const errorValidation = EmailSubmissionErrorSchema.safeParse(errorResponse);
      if (!errorValidation.success) {
        console.error('Error response validation failed:', errorValidation.error);
      }

      return res.status(400).json(errorResponse);
    }

    if (userEmailResult.error) {
        console.error('Error sending confirmation email:', userEmailResult.error);
        // We don't return an error to the user here, as the main action (admin notification) succeeded.
        // We just log it. The user's form submission is considered successful.
    }

    const successResponse = { message: 'Form submitted successfully!' };

    // Validate success response against schema
    const successValidation = EmailSubmissionSuccessSchema.safeParse(successResponse);
    if (!successValidation.success) {
      console.error('Success response validation failed:', successValidation.error);
    }

    return res.status(200).json(successResponse);
  } catch (error) {
    console.error(error);
    
    const errorResponse = { error: 'An unknown error occurred.' };
    
    // Validate error response against schema
    const errorValidation = EmailSubmissionErrorSchema.safeParse(errorResponse);
    if (!errorValidation.success) {
      console.error('Error response validation failed:', errorValidation.error);
    }
    
    return res.status(500).json(errorResponse);
  }
}