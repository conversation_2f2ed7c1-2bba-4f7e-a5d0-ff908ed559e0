import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  Search, 
  Wrench, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  FileText,
  Zap,
  Shield,
  Database,
  Cpu,
  HardDrive
} from 'lucide-react';

interface ProcessingStep {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  estimatedDuration: number; // in milliseconds
  subSteps?: string[];
}

interface DetailedProgressIndicatorProps {
  currentStep: string | null;
  progress: number;
  fileName?: string;
  fileSize?: number;
  operation: 'analyze' | 'repair';
  isActive: boolean;
  onComplete?: () => void;
  className?: string;
}

const PROCESSING_STEPS = {
  analyze: [
    {
      id: 'upload',
      label: 'Secure Upload',
      description: 'Encrypting and uploading your file safely',
      icon: <Upload className="h-5 w-5" />,
      estimatedDuration: 2000,
      subSteps: [
        'Encrypting file data',
        'Establishing secure connection',
        'Transferring file chunks',
        'Verifying upload integrity'
      ]
    },
    {
      id: 'validate',
      label: 'File Validation',
      description: 'Checking file format and structure',
      icon: <Shield className="h-5 w-5" />,
      estimatedDuration: 1000,
      subSteps: [
        'Validating file format',
        'Checking file integrity',
        'Scanning for malware',
        'Verifying Excel structure'
      ]
    },
    {
      id: 'analyze',
      label: 'Deep Analysis',
      description: 'Scanning for errors and corruption',
      icon: <Search className="h-5 w-5" />,
      estimatedDuration: 3000,
      subSteps: [
        'Reading worksheet data',
        'Analyzing formulas',
        'Checking cell references',
        'Detecting corruption patterns',
        'Generating analysis report'
      ]
    },
    {
      id: 'process',
      label: 'Results Processing',
      description: 'Preparing your analysis report',
      icon: <FileText className="h-5 w-5" />,
      estimatedDuration: 1500,
      subSteps: [
        'Compiling findings',
        'Generating recommendations',
        'Creating repair preview',
        'Finalizing report'
      ]
    }
  ],
  repair: [
    {
      id: 'prepare',
      label: 'Repair Setup',
      description: 'Preparing repair environment',
      icon: <Cpu className="h-5 w-5" />,
      estimatedDuration: 1000,
      subSteps: [
        'Initializing repair engine',
        'Loading repair algorithms',
        'Setting up memory buffers',
        'Preparing backup systems'
      ]
    },
    {
      id: 'backup',
      label: 'Data Backup',
      description: 'Creating safety backup of your file',
      icon: <HardDrive className="h-5 w-5" />,
      estimatedDuration: 800,
      subSteps: [
        'Creating file snapshot',
        'Backing up original data',
        'Verifying backup integrity',
        'Setting restore points'
      ]
    },
    {
      id: 'repair',
      label: 'Active Repair',
      description: 'Fixing detected issues',
      icon: <Wrench className="h-5 w-5" />,
      estimatedDuration: 4000,
      subSteps: [
        'Repairing corrupted cells',
        'Fixing formula references',
        'Restoring worksheet structure',
        'Cleaning invalid data',
        'Optimizing file structure'
      ]
    },
    {
      id: 'validate',
      label: 'Repair Validation',
      description: 'Ensuring repair was successful',
      icon: <CheckCircle className="h-5 w-5" />,
      estimatedDuration: 1200,
      subSteps: [
        'Testing repaired file',
        'Validating data integrity',
        'Checking formula accuracy',
        'Verifying file compatibility'
      ]
    },
    {
      id: 'finalize',
      label: 'Finalization',
      description: 'Preparing your repaired file',
      icon: <Zap className="h-5 w-5" />,
      estimatedDuration: 800,
      subSteps: [
        'Optimizing file size',
        'Applying final touches',
        'Generating repair report',
        'Preparing download'
      ]
    }
  ]
};

export const DetailedProgressIndicator: React.FC<DetailedProgressIndicatorProps> = ({
  currentStep,
  progress,
  fileName,
  fileSize,
  operation,
  isActive,
  onComplete,
  className = ''
}) => {
  const [currentSubStep, setCurrentSubStep] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);

  const steps = PROCESSING_STEPS[operation];
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const currentStepData = steps[currentStepIndex];

  // Timer for elapsed time
  useEffect(() => {
    if (!isActive) return;

    const startTime = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Date.now() - startTime);
    }, 100);

    return () => clearInterval(interval);
  }, [isActive]);

  // Estimate remaining time based on progress
  useEffect(() => {
    if (progress > 5 && elapsedTime > 1000) {
      const totalEstimated = (elapsedTime / progress) * 100;
      const remaining = totalEstimated - elapsedTime;
      setEstimatedTimeRemaining(Math.max(0, remaining));
    }
  }, [progress, elapsedTime]);

  // Simulate sub-step progression
  useEffect(() => {
    if (!currentStepData?.subSteps || !isActive) return;

    const subStepDuration = currentStepData.estimatedDuration / currentStepData.subSteps.length;
    const interval = setInterval(() => {
      setCurrentSubStep(prev => {
        const next = prev + 1;
        return next >= currentStepData.subSteps!.length ? 0 : next;
      });
    }, subStepDuration);

    return () => clearInterval(interval);
  }, [currentStepData, isActive]);

  const formatTime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m ${seconds % 60}s`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (!isActive) return null;

  return (
    <div className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {operation === 'analyze' ? 'Analyzing File' : 'Repairing File'}
          </h3>
          {fileName && (
            <p className="text-sm text-gray-600 mt-1">
              {fileName} {fileSize && `(${formatFileSize(fileSize)})`}
            </p>
          )}
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-lime-600">{Math.round(progress)}%</div>
          <div className="text-xs text-gray-500">
            {elapsedTime > 0 && formatTime(elapsedTime)}
          </div>
        </div>
      </div>

      {/* Overall Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
          <motion.div
            className="h-3 rounded-full bg-gradient-to-r from-lime-400 to-lime-500 shadow-sm"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: 'easeOut' }}
          />
        </div>
        {estimatedTimeRemaining && estimatedTimeRemaining > 1000 && (
          <div className="flex items-center justify-between mt-2 text-xs text-gray-600">
            <span>Progress: {Math.round(progress)}%</span>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>~{formatTime(estimatedTimeRemaining)} remaining</span>
            </div>
          </div>
        )}
      </div>

      {/* Step Progress */}
      <div className="space-y-3">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = step.id === currentStep;
          const isPending = index > currentStepIndex;

          return (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`flex items-start gap-4 p-3 rounded-lg transition-all ${
                isCurrent
                  ? 'bg-lime-50 border border-lime-200'
                  : isCompleted
                  ? 'bg-green-50 border border-green-200'
                  : 'bg-gray-50 border border-gray-200'
              }`}
            >
              {/* Step Icon */}
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                isCompleted
                  ? 'bg-green-500 text-white'
                  : isCurrent
                  ? 'bg-lime-500 text-white'
                  : 'bg-gray-300 text-gray-600'
              }`}>
                {isCompleted ? (
                  <CheckCircle className="h-4 w-4" />
                ) : isCurrent ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                  >
                    {step.icon}
                  </motion.div>
                ) : (
                  step.icon
                )}
              </div>

              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <div className={`font-medium ${
                  isCurrent ? 'text-lime-900' : isCompleted ? 'text-green-900' : 'text-gray-600'
                }`}>
                  {step.label}
                </div>
                <div className={`text-sm mt-1 ${
                  isCurrent ? 'text-lime-700' : isCompleted ? 'text-green-700' : 'text-gray-500'
                }`}>
                  {step.description}
                </div>

                {/* Sub-steps for current step */}
                <AnimatePresence>
                  {isCurrent && step.subSteps && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mt-3 space-y-1"
                    >
                      {step.subSteps.map((subStep, subIndex) => (
                        <motion.div
                          key={subIndex}
                          initial={{ opacity: 0.5 }}
                          animate={{ 
                            opacity: subIndex === currentSubStep ? 1 : 0.5,
                            scale: subIndex === currentSubStep ? 1.02 : 1
                          }}
                          className={`text-xs flex items-center gap-2 ${
                            subIndex === currentSubStep ? 'text-lime-800 font-medium' : 'text-lime-600'
                          }`}
                        >
                          <div className={`w-1.5 h-1.5 rounded-full ${
                            subIndex === currentSubStep ? 'bg-lime-500' : 'bg-lime-300'
                          }`} />
                          {subStep}
                          {subIndex === currentSubStep && (
                            <motion.div
                              className="w-1 h-1 bg-lime-500 rounded-full"
                              animate={{ scale: [1, 1.5, 1] }}
                              transition={{ duration: 1, repeat: Infinity }}
                            />
                          )}
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Step Status */}
              <div className="flex-shrink-0 text-xs">
                {isCompleted && (
                  <span className="text-green-600 font-medium">✓ Done</span>
                )}
                {isCurrent && (
                  <span className="text-lime-600 font-medium">Active</span>
                )}
                {isPending && (
                  <span className="text-gray-400">Pending</span>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Processing Stats */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {currentStepIndex + 1}/{steps.length}
            </div>
            <div className="text-xs text-gray-600">Steps</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {elapsedTime > 0 ? formatTime(elapsedTime) : '0s'}
            </div>
            <div className="text-xs text-gray-600">Elapsed</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {estimatedTimeRemaining && estimatedTimeRemaining > 1000 
                ? formatTime(estimatedTimeRemaining) 
                : 'Calculating...'}
            </div>
            <div className="text-xs text-gray-600">Remaining</div>
          </div>
        </div>
      </div>

      {/* Pulse Animation */}
      <div className="flex items-center justify-center mt-4">
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-1.5 h-1.5 bg-lime-400 rounded-full"
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 1.2,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default DetailedProgressIndicator;