import React from 'react';
import Icon from './Icon';
import { FAQ } from '../types';

interface FAQItemProps {
  faq: FAQ;
}

const FAQItem: React.FC<FAQItemProps> = ({ faq }) => {
  return (
    <div className="bg-white/20 backdrop-blur-xl p-6 rounded-xl border border-white/30 shadow-2xl">
      <details className="group">
        <summary className="flex justify-between items-center cursor-pointer list-none">
          <h3 className="text-lg font-medium text-gray-900">{faq.question}</h3>
          <Icon iconClass="fas fa-chevron-down text-gray-500 group-open:rotate-180 transition-transform" />
        </summary>
        <div className="mt-4 text-gray-700">
          {faq.answer}
        </div>
      </details>
    </div>
  );
};

export default FAQItem;
