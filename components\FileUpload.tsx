import React, { useRef, useImperative<PERSON>andle, useEffect } from 'react';
import { Upload, CheckCircle, FolderOpen, X, FileText } from 'lucide-react';
import { motion } from 'framer-motion';
import ProgressiveLoader from './ProgressiveLoader';
import EnhancedErrorDisplay from './EnhancedErrorDisplay';
import DetailedProgressIndicator from './DetailedProgressIndicator';
import SuccessFailureAnimations from './SuccessFailureAnimations';
import { useFileUpload } from '../hooks/useFileUpload';
import { useFileRepair } from '../hooks/useFileRepair';

interface FileUploadProps {
  onFileProcessed: (result: ProcessedFileResult) => void;
  className?: string;
}

import { RepairReport } from '../types';

export interface ProcessedFileResult {
  originalFile: File;
  workbook: any | null; // Using any instead of ExcelJS.Workbook for now
  fileInfo: {
    name: string;
    size: number;
    type: string;
    lastModified: Date;
  };
  sheets: Array<{
    name: string;
    cellCount: number;
    hasFormulas: boolean;
    errors: string[];
  }>;
  status: 'success' | 'error' | 'warning';
  message: string;
  repairReport?: RepairReport;
}

export interface FileUploadHandles {
  repairFile: (file: File) => Promise<ProcessedFileResult>;
  reset: () => void;
}

export const FileUpload = React.forwardRef<FileUploadHandles, FileUploadProps>(({ onFileProcessed, className = '' }, ref) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Use custom hooks for file upload and repair functionality
  const fileUpload = useFileUpload({
    onFileProcessed,
    onError: (error) => {
      console.error('File upload error:', error);
    }
  });

  const fileRepair = useFileRepair({
    onRepairComplete: onFileProcessed,
    onError: (error) => {
      console.error('File repair error:', error);
    }
  });

  // Mobile detection state
  const [isMobile, setIsMobile] = React.useState(false);
  const [showMobileUploadModal, setShowMobileUploadModal] = React.useState(false);
  const [showSuccessAnimation, setShowSuccessAnimation] = React.useState(false);

  // Mobile detection effect
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                           window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const reset = React.useCallback(() => {
    console.log('🧹 Resetting FileUpload component state');

    // Reset both hooks with proper cleanup
    try {
      fileUpload.reset();
      fileRepair.reset();
    } catch (error) {
      console.warn('Error during hook reset:', error);
    }

    // Reset local state
    setShowMobileUploadModal(false);
    setShowSuccessAnimation(false);

    // Clear file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [fileUpload, fileRepair]);

  useImperativeHandle(ref, () => ({
    repairFile: fileRepair.repairFile,
    reset,
  }));

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      console.log('📁 File selected:', file.name, file.size, file.type);
      setShowMobileUploadModal(false);
      
      // Ensure we're not already processing
      if (!fileUpload.isProcessing) {
        fileUpload.handleFileUpload(file);
      } else {
        console.warn('⚠️ File upload already in progress, ignoring new file');
      }
    }
    
    // Clear the input value to allow re-selecting the same file
    e.target.value = '';
  };

  const openMobileUpload = () => {
    if (isMobile) {
      setShowMobileUploadModal(true);
    } else {
      // Prevent double triggering by adding a small delay
      setTimeout(() => {
        fileInputRef.current?.click();
      }, 0);
    }
  };

  const removeFile = () => {
    reset();
  };

  return (
    <div className={`${className}`}>
      {/* Success Animation Overlay */}
      {showSuccessAnimation && (
        <div className="mb-6">
          <SuccessFailureAnimations
            status="success"
            title="🎉 Upload Successful!"
            message="Your file has been uploaded and is being processed."
            autoHide={true}
            duration={3000}
          />
        </div>
      )}

      {/* Enhanced Upload Zone */}
      {!fileUpload.uploadedFile && (
        <div
          role="button"
          tabIndex={0}
          aria-label="File upload area. Drop Excel files here or click to browse."
          aria-describedby="upload-instructions"
          className={`border-2 border-dashed rounded-2xl p-12 transition-all duration-300 relative overflow-hidden focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-lime-500 ${
            fileUpload.isDragOver
              ? fileUpload.isValidDrop
                ? 'border-lime-400 bg-lime-50 scale-105 shadow-lg'
                : 'border-red-400 bg-red-50 scale-105 shadow-lg'
              : 'border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50'
          }`}
          onDrop={fileUpload.handleDrop}
          onDragOver={fileUpload.handleDragOver}
          onDragEnter={fileUpload.handleDragEnter}
          onDragLeave={fileUpload.handleDragLeave}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            openMobileUpload();
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              e.stopPropagation();
              openMobileUpload();
            }
          }}
        >
          {/* Animated Background */}
          {fileUpload.isDragOver && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className={`absolute inset-0 rounded-2xl ${
                fileUpload.isValidDrop
                  ? 'bg-gradient-to-br from-lime-100 to-green-100'
                  : 'bg-gradient-to-br from-red-100 to-orange-100'
              }`}
            />
          )}

          {/* Floating particles effect */}
          {fileUpload.isDragOver && fileUpload.isValidDrop && (
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-lime-400 rounded-full"
                  initial={{
                    x: Math.random() * 400,
                    y: Math.random() * 300,
                    opacity: 0,
                    scale: 0
                  }}
                  animate={{
                    y: Math.random() * 300,
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.2
                  }}
                />
              ))}
            </div>
          )}

          <div className="relative z-10 text-center">
            <div className="flex justify-center mb-6">
              <div className={`p-4 rounded-full transition-all duration-300 ${
                fileUpload.isDragOver
                  ? fileUpload.isValidDrop
                    ? 'bg-lime-200 text-lime-700 scale-110'
                    : 'bg-red-200 text-red-700 scale-110'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {fileUpload.isDragOver ? (
                  fileUpload.isValidDrop ? (
                    <CheckCircle className="h-12 w-12" aria-hidden="true" />
                  ) : (
                    <X className="h-12 w-12" aria-hidden="true" />
                  )
                ) : (
                  <Upload className="h-12 w-12" aria-hidden="true" />
                )}
              </div>
            </div>

            <h3 className={`text-xl font-semibold mb-2 transition-colors duration-300 ${
              fileUpload.isDragOver
                ? fileUpload.isValidDrop
                  ? 'text-lime-800'
                  : 'text-red-800'
                : 'text-gray-800'
            }`}>
              {fileUpload.isDragOver ? (
                fileUpload.isValidDrop ? (
                  'Drop your Excel file here!'
                ) : (
                  fileUpload.draggedFileType ? (
                    `Unsupported file type: ${fileUpload.draggedFileType}`
                  ) : (
                    'Invalid file type'
                  )
                )
              ) : (
                'Drop your Excel file here'
              )}
            </h3>

            <p id="upload-instructions" className={`text-sm mb-6 transition-colors duration-300 ${
              fileUpload.isDragOver
                ? fileUpload.isValidDrop
                  ? 'text-lime-700'
                  : 'text-red-700'
                : 'text-gray-600'
            }`}>
              {fileUpload.isDragOver ? (
                fileUpload.isValidDrop ? (
                  'Release to upload your file'
                ) : (
                  'We only support .xlsx files'
                )
              ) : (
                'or click to browse your files'
              )}
            </p>

            {!fileUpload.isDragOver && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-lime-300 hover:bg-lime-400 text-gray-900 font-bold py-3 px-8 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-lime-500 focus:ring-offset-2"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  openMobileUpload();
                }}
                aria-label="Browse for Excel files to upload"
              >
                <FolderOpen className="inline h-5 w-5 mr-2" aria-hidden="true" />
                Browse Files
              </motion.button>
            )}
          </div>
        </div>
      )}

      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx"
        onChange={handleFileSelect}
        className="hidden"
        aria-label="File input for Excel files"
      />

      {/* Mobile Upload Modal */}
      {showMobileUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4 text-center">Upload Excel File</h3>
            <p className="text-gray-600 text-sm mb-4 text-center">
              Select an Excel file (.xlsx) from your device to analyze and repair.
            </p>
            <div className="flex flex-col gap-3">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowMobileUploadModal(false);
                  setTimeout(() => {
                    fileInputRef.current?.click();
                  }, 100);
                }}
                className="bg-lime-300 hover:bg-lime-400 text-gray-900 font-bold py-3 px-6 rounded-lg transition duration-300 flex items-center justify-center gap-2"
              >
                <FolderOpen className="h-5 w-5" aria-hidden="true" />
                Choose File
              </button>
              <button
                onClick={() => setShowMobileUploadModal(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-3 px-6 rounded-lg transition duration-300"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {fileUpload.currentError && (
        <div className="mb-6">
          <EnhancedErrorDisplay
            error={fileUpload.currentError}
          />
        </div>
      )}

      {/* File Processing Display */}
      {fileUpload.isProcessing && !fileUpload.currentError && (
        <div className="mb-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                Processing Your File
              </h3>
              <button
                onClick={() => {
                  // Allow canceling the processing
                  reset();
                }}
                className="text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Cancel file processing"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {fileUpload.showDetailedProgress && (
              <div className="mb-4">
                <DetailedProgressIndicator
                  isActive={fileUpload.showDetailedProgress}
                  currentStep={fileUpload.processingStatus}
                  progress={fileUpload.processingProgress}
                  fileName={fileUpload.uploadedFile?.name || ''}
                  fileSize={fileUpload.uploadedFile?.size || 0}
                  operation={fileUpload.currentOperation}
                />
              </div>
            )}

            <ProgressiveLoader
              isLoading={fileUpload.isProcessing}
              currentStep={fileUpload.processingStatus}
              steps={[]}
              progress={fileUpload.processingProgress}
            />

            {fileUpload.memoryUsage && (
              <div className="mt-4 text-sm text-gray-600">
                <p>Memory usage: {fileUpload.memoryUsage.percentage}% ({fileUpload.memoryUsage.available} available)</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Uploaded File Display */}
      {fileUpload.uploadedFile && !fileUpload.isProcessing && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Uploaded File</h3>
            <button
              onClick={removeFile}
              className="text-gray-500 hover:text-gray-700 transition-colors"
              aria-label="Remove uploaded file"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-green-500" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {fileUpload.uploadedFile.name}
              </p>
              <p className="text-sm text-gray-500">
                {formatFileSize(fileUpload.uploadedFile.size)}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

FileUpload.displayName = 'FileUpload';
