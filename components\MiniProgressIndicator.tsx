import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Download, CheckCircle, Loader2 } from 'lucide-react';

interface MiniProgressIndicatorProps {
  isActive: boolean;
  progress: number;
  label: string;
  onComplete?: () => void;
  className?: string;
}

export const MiniProgressIndicator: React.FC<MiniProgressIndicatorProps> = ({
  isActive,
  progress,
  label,
  onComplete,
  className = ''
}) => {
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    if (progress >= 100 && isActive) {
      setIsCompleted(true);
      setTimeout(() => {
        onComplete?.();
      }, 500);
    }
  }, [progress, isActive, onComplete]);

  if (!isActive && !isCompleted) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`bg-white border border-gray-200 rounded-lg p-3 shadow-sm ${className}`}
    >
      <div className="flex items-center gap-3">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          isCompleted ? 'bg-green-100' : 'bg-lime-100'
        }`}>
          {isCompleted ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <Loader2 className="h-4 w-4 text-lime-600 animate-spin" />
          )}
        </div>
        
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900">{label}</div>
          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
            <motion.div
              className={`h-1.5 rounded-full ${
                isCompleted ? 'bg-green-500' : 'bg-lime-500'
              }`}
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
            />
          </div>
        </div>
        
        <div className={`text-sm font-bold ${
          isCompleted ? 'text-green-600' : 'text-lime-600'
        }`}>
          {Math.round(progress)}%
        </div>
      </div>
    </motion.div>
  );
};

export default MiniProgressIndicator;