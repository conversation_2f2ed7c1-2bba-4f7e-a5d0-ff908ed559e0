import XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';

class ExcelFileAnalyzer {
  static analyzeFile(filePath) {
    console.log(`\n📊 Analyzing: ${path.basename(filePath)}`);
    console.log('=' .repeat(50));
    
    try {
      // Check file size
      const stats = fs.statSync(filePath);
      console.log(`📁 File Size: ${(stats.size / 1024).toFixed(2)} KB`);
      
      // Read the file
      const workbook = XLSX.readFile(filePath, { 
        cellDates: true,
        cellNF: false,
        cellStyles: true
      });
      
      console.log(`📋 Number of Sheets: ${workbook.SheetNames.length}`);
      
      let _totalCells = 0;
      let totalFormulas = 0;
      let totalErrors = 0;
      let totalData = 0;
      
      workbook.SheetNames.forEach((sheetName, index) => {
        const sheet = workbook.Sheets[sheetName];
        const range = XLSX.utils.decode_range(sheet['!ref'] || 'A1:A1');
        const sheetCells = (range.e.r - range.s.r + 1) * (range.e.c - range.s.c + 1);
        
        let sheetFormulas = 0;
        let sheetErrors = 0;
        let sheetData = 0;
        
        // Analyze each cell
        for (let R = range.s.r; R <= range.e.r; R++) {
          for (let C = range.s.c; C <= range.e.c; C++) {
            const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
            const cell = sheet[cellRef];
            
            if (cell) {
              sheetData++;
              
              // Check for formulas
              if (cell.f) {
                sheetFormulas++;
              }
              
              // Check for errors
              if (cell.v && typeof cell.v === 'string') {
                if (cell.v.includes('#REF!') || cell.v.includes('#NAME?') || 
                    cell.v.includes('#VALUE!') || cell.v.includes('#DIV/0!') ||
                    cell.v.includes('#NULL!') || cell.v.includes('#NUM!')) {
                  sheetErrors++;
                }
              }
            }
          }
        }
        
        console.log(`\n  📄 Sheet ${index + 1}: "${sheetName}"`);
        console.log(`     Range: ${sheet['!ref'] || 'Empty'}`);
        console.log(`     Cells with Data: ${sheetData}`);
        console.log(`     Formulas: ${sheetFormulas}`);
        console.log(`     Errors: ${sheetErrors}`);
        
        totalCells += sheetCells;
        totalFormulas += sheetFormulas;
        totalErrors += sheetErrors;
        totalData += sheetData;
      });
      
      console.log(`\n📈 Summary:`);
      console.log(`   Total Cells with Data: ${totalData}`);
      console.log(`   Total Formulas: ${totalFormulas}`);
      console.log(`   Total Errors: ${totalErrors}`);
      console.log(`   File Status: ${totalErrors > 0 ? '❌ Has Errors' : '✅ Clean'}`);
      
      return {
        fileName: path.basename(filePath),
        fileSize: stats.size,
        sheetsCount: workbook.SheetNames.length,
        totalData,
        totalFormulas,
        totalErrors,
        hasErrors: totalErrors > 0,
        workbook
      };
      
    } catch (error) {
      console.log(`❌ Error reading file: ${error.message}`);
      console.log(`   This likely means the file is severely corrupted`);
      
      return {
        fileName: path.basename(filePath),
        fileSize: fs.statSync(filePath).size,
        error: error.message,
        corrupted: true
      };
    }
  }
  
  static compareFiles(beforePath, afterPath) {
    console.log('\n🔍 COMPARISON ANALYSIS');
    console.log('=' .repeat(60));
    
    const before = this.analyzeFile(beforePath);
    const after = this.analyzeFile(afterPath);
    
    console.log('\n📊 COMPARISON RESULTS:');
    console.log('=' .repeat(30));
    
    if (before.corrupted && !after.corrupted) {
      console.log('✅ SUCCESS: Corrupted file was successfully repaired!');
    } else if (before.corrupted && after.corrupted) {
      console.log('❌ FAILED: File is still corrupted after repair');
    } else if (!before.corrupted && !after.corrupted) {
      console.log('📋 Both files are readable - checking improvements...');
      
      if (before.totalErrors > after.totalErrors) {
        console.log(`✅ ERRORS FIXED: ${before.totalErrors} → ${after.totalErrors} (${before.totalErrors - after.totalErrors} errors removed)`);
      } else if (before.totalErrors === after.totalErrors) {
        console.log(`📄 SAME ERRORS: ${before.totalErrors} errors in both files`);
      } else {
        console.log(`❓ UNEXPECTED: More errors after repair (${before.totalErrors} → ${after.totalErrors})`);
      }
      
      if (before.totalData !== after.totalData) {
        console.log(`📈 DATA CHANGE: ${before.totalData} → ${after.totalData} cells with data`);
      }
      
      if (before.totalFormulas !== after.totalFormulas) {
        console.log(`🧮 FORMULAS: ${before.totalFormulas} → ${after.totalFormulas} formulas`);
      }
    }
    
    console.log(`📁 FILE SIZE: ${(before.fileSize/1024).toFixed(2)}KB → ${(after.fileSize/1024).toFixed(2)}KB`);
  }
}

// Command line usage
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log(`
🔧 Excel File Repair Verification Tool

Usage:
  node verify-repair.mjs <file>                    # Analyze single file
  node verify-repair.mjs <before> <after>          # Compare before/after repair
  
Examples:
  node verify-repair.mjs test-corrupted.xlsx       # Analyze corrupted file
  node verify-repair.mjs test-corrupted.xlsx test-corrupted_repaired.xlsx  # Compare
  `);
} else if (args.length === 1) {
  // Analyze single file
  ExcelFileAnalyzer.analyzeFile(args[0]);
} else if (args.length === 2) {
  // Compare two files
  ExcelFileAnalyzer.compareFiles(args[0], args[1]);
} else {
  console.log('Error: Too many arguments. Use 1 file to analyze or 2 files to compare.');
} 