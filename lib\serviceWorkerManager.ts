/**
 * Service Worker Manager
 * Provides utilities for managing service worker functionality
 */

export interface CacheInfo {
  [cacheName: string]: number;
}

export interface ServiceWorkerStatus {
  isSupported: boolean;
  isRegistered: boolean;
  isControlling: boolean;
  registration?: ServiceWorkerRegistration | undefined;
}

export class ServiceWorkerManager {
  private static registration: ServiceWorkerRegistration | null = null;

  /**
   * Check if service worker is supported
   */
  static isSupported(): boolean {
    return 'serviceWorker' in navigator;
  }

  /**
   * Register the service worker
   */
  static async register(scriptURL: string = '/sw.js'): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported()) {
      console.warn('Service Worker not supported in this browser');
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.register(scriptURL);
      this.registration = registration;

      console.log('✅ Service Worker registered:', registration);

      // Set up event listeners
      this.setupEventListeners(registration);

      return registration;
    } catch (error) {
      console.error('❌ Service Worker registration failed:', error);
      return null;
    }
  }

  /**
   * Get current service worker status
   */
  static getStatus(): ServiceWorkerStatus {
    const isSupported = this.isSupported();
    const isRegistered = !!this.registration;
    const isControlling = !!navigator.serviceWorker?.controller;

    return {
      isSupported,
      isRegistered,
      isControlling,
      registration: this.registration || undefined
    };
  }

  /**
   * Update the service worker
   */
  static async update(): Promise<void> {
    if (!this.registration) {
      console.warn('No service worker registration found');
      return;
    }

    try {
      await this.registration.update();
      console.log('🔄 Service Worker update check completed');
    } catch (error) {
      console.error('❌ Service Worker update failed:', error);
    }
  }

  /**
   * Unregister the service worker
   */
  static async unregister(): Promise<boolean> {
    if (!this.registration) {
      console.warn('No service worker registration found');
      return false;
    }

    try {
      const result = await this.registration.unregister();
      if (result) {
        this.registration = null;
        console.log('✅ Service Worker unregistered successfully');
      }
      return result;
    } catch (error) {
      console.error('❌ Service Worker unregistration failed:', error);
      return false;
    }
  }

  /**
   * Get cache information
   */
  static async getCacheInfo(): Promise<CacheInfo> {
    if (!this.isSupported() || !navigator.serviceWorker.controller) {
      return {};
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();

      messageChannel.port1.onmessage = (event) => {
        resolve(event.data || {});
      };

      navigator.serviceWorker.controller?.postMessage(
        { type: 'GET_CACHE_INFO' },
        [messageChannel.port2]
      );

      // Timeout after 5 seconds
      setTimeout(() => resolve({}), 5000);
    });
  }

  /**
   * Clear all caches
   */
  static async clearCache(): Promise<boolean> {
    if (!this.isSupported() || !navigator.serviceWorker.controller) {
      return false;
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();

      messageChannel.port1.onmessage = (event) => {
        resolve(event.data?.success || false);
      };

      navigator.serviceWorker.controller?.postMessage(
        { type: 'CLEAR_CACHE' },
        [messageChannel.port2]
      );

      // Timeout after 10 seconds
      setTimeout(() => resolve(false), 10000);
    });
  }

  /**
   * Check if the app is running offline
   */
  static isOffline(): boolean {
    return !navigator.onLine;
  }

  /**
   * Add offline/online event listeners
   */
  static addNetworkListeners(
    onOnline?: () => void,
    onOffline?: () => void
  ): () => void {
    const handleOnline = () => {
      console.log('🌐 Back online');
      onOnline?.();
    };

    const handleOffline = () => {
      console.log('📱 Gone offline');
      onOffline?.();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Return cleanup function
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }

  /**
   * Show update available notification
   */
  static showUpdateNotification(): void {
    // This could be enhanced to show a proper UI notification
    if (confirm('A new version of SheetHealer is available. Would you like to update now?')) {
      window.location.reload();
    }
  }

  /**
   * Set up event listeners for service worker
   */
  private static setupEventListeners(registration: ServiceWorkerRegistration): void {
    // Listen for updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (!newWorker) return;

      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          console.log('🔄 New version available');
          this.showUpdateNotification();
        }
      });
    });

    // Listen for controlling changes
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('🔄 Service Worker controller changed');
      // Optionally reload the page
      // window.location.reload();
    });

    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { type, payload } = event.data;

      switch (type) {
        case 'CACHE_UPDATED':
          console.log('📦 Cache updated:', payload);
          break;
        case 'OFFLINE_READY':
          console.log('📱 App ready for offline use');
          break;
        default:
          console.log('📨 Message from service worker:', event.data);
      }
    });
  }

  /**
   * Preload critical resources
   */
  static async preloadCriticalResources(urls: string[]): Promise<void> {
    if (!this.isSupported()) return;

    try {
      const cache = await caches.open('sheethealer-preload');
      await cache.addAll(urls);
      console.log('📦 Critical resources preloaded');
    } catch (error) {
      console.error('❌ Failed to preload critical resources:', error);
    }
  }

  /**
   * Get estimated cache size
   */
  static async getEstimatedCacheSize(): Promise<number> {
    if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
      return 0;
    }

    try {
      const estimate = await navigator.storage.estimate();
      return estimate.usage || 0;
    } catch (error) {
      console.error('❌ Failed to get storage estimate:', error);
      return 0;
    }
  }
}

// Export convenience functions
export const registerServiceWorker = ServiceWorkerManager.register.bind(ServiceWorkerManager);
export const getServiceWorkerStatus = ServiceWorkerManager.getStatus.bind(ServiceWorkerManager);
export const updateServiceWorker = ServiceWorkerManager.update.bind(ServiceWorkerManager);
export const clearServiceWorkerCache = ServiceWorkerManager.clearCache.bind(ServiceWorkerManager);
export const addNetworkListeners = ServiceWorkerManager.addNetworkListeners.bind(ServiceWorkerManager);
export const isOffline = ServiceWorkerManager.isOffline.bind(ServiceWorkerManager);