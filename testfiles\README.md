# 🧪 Test Files & Verification Tools

This folder contains Excel test files and verification tools to test and validate the SheetHealer repair functionality.

## 📁 Test Files

### Sample Excel Files
- **`test-normal.xlsx`** - Clean Excel file with valid data and formulas
- **`test-working.xlsx`** - Another clean test file  
- **`test-corrupted.xlsx`** - File that appears corrupted but may be readable
- **`test-corrupted-excel.xlsx`** - Another test file with potential issues
- **`test30.xlsx`, `test50.xlsx`, `test70.xlsx`, `test100.xlsx`** - Large test files (1MB+)

### Generated Test Files
- **`test-with-real-errors.xlsx`** - File with actual Excel errors (#DIV/0!, #NAME?, etc.)
- **`test-with-real-errors_REPAIRED.xlsx`** - Repaired version showing successful fixes

## 🔧 Verification Tools

### 1. `verify-repair.mjs` - Main Analysis Tool
**Analyze a single file:**
```bash
node testfiles/verify-repair.mjs testfiles/your-file.xlsx
```

**Compare before/after repair:**
```bash
node testfiles/verify-repair.mjs testfiles/original.xlsx testfiles/repaired.xlsx
```

**What it shows:**
- File size and structure
- Number of sheets and cells
- Formula count
- Error detection
- Before/after comparison

### 2. `create-corrupted-test.mjs` - Error File Generator
```bash
node testfiles/create-corrupted-test.mjs
```

Creates `test-with-real-errors.xlsx` with various Excel errors:
- #DIV/0! (division by zero)
- #NAME? (invalid function names)
- #REF! (broken references)
- #VALUE! (wrong data types)
- #NULL! (invalid ranges)
- #NUM! (invalid numbers)
- Wrong function names (SUMM instead of SUM)
- Unbalanced parentheses
- Circular references

### 3. `test-repair-functionality.mjs` - Live Repair Test
```bash
node testfiles/test-repair-functionality.mjs
```

**What it does:**
- Reads `test-with-real-errors.xlsx`
- Applies repair algorithms
- Shows detailed repair log
- Saves repaired file
- Demonstrates actual repair functionality

## 📊 Example Verification Results

```
📊 Analyzing: test-with-real-errors.xlsx
==================================================
📁 File Size: 19.15 KB
📋 Number of Sheets: 2
📈 Summary:
   Total Cells with Data: 64
   Total Formulas: 16
   Total Errors: 6
   File Status: ❌ Has Errors

📊 Analyzing: test-with-real-errors_REPAIRED.xlsx
==================================================
📁 File Size: 18.85 KB
📋 Number of Sheets: 2
📈 Summary:
   Total Cells with Data: 64
   Total Formulas: 7
   Total Errors: 0
   File Status: ✅ Clean

✅ ERRORS FIXED: 6 → 0 (6 errors removed)
🧮 FORMULAS: 16 → 7 formulas
📁 FILE SIZE: 19.15KB → 18.85KB
```

## 🎯 How to Test Your Own Files

1. **Upload your file to the web app** and download the repaired version
2. **Place both files in this testfiles folder**
3. **Run comparison:**
   ```bash
   node testfiles/verify-repair.mjs testfiles/your-original.xlsx testfiles/your-repaired.xlsx
   ```
4. **Review the results** to see exactly what was fixed

## 🧹 Cleanup

To remove all test files and keep only the tools:
```bash
# Keep only the .mjs tools, remove Excel files
del testfiles\*.xlsx
```

## 🔧 Recent Improvements

### Fixed "undefined" Content Issue
**Problem:** Severely corrupted files that couldn't be repaired were returning Excel files with "undefined" content.

**Solution:** 
- Advanced repair now creates helpful recovery reports when no data can be extracted
- All undefined/null values are replaced with "Recovered data" placeholders
- Users get clear information about what happened and next steps

**Files demonstrating the fix:**
- `test-undefined-fix-result.xlsx` - Shows professional recovery report
- `test-data-cleaning.xlsx` - Shows how undefined values are handled

---

**Note:** All these tools use the same XLSX.js library as the main application, so they accurately reflect what the web app can detect and repair. 