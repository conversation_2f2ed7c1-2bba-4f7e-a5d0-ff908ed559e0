/**
 * Integration tests for file processing pipeline
 * Tests the complete flow from file upload to repair completion
 */

// Integration tests for file processing pipeline
// Run with: npm test or npx vitest run tests/integration/fileProcessing.test.ts

// Simple test framework for integration tests
const describe = (name: string, fn: () => void) => {
  console.log(`\n📋 ${name}`);
  fn();
};

const it = (name: string, fn: () => Promise<void> | void) => {
  return async () => {
    try {
      await fn();
      console.log(`  ✅ ${name}`);
    } catch (error) {
      console.log(`  ❌ ${name}`);
      console.error(`     Error: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  };
};

const expect = (actual: any) => ({
  toBe: (expected: any) => {
    if (actual !== expected) {
      throw new Error(`Expected ${actual} to be ${expected}`);
    }
  },
  toBeDefined: () => {
    if (actual === undefined) {
      throw new Error(`Expected value to be defined`);
    }
  },
  toBeGreaterThan: (expected: number) => {
    if (actual <= expected) {
      throw new Error(`Expected ${actual} to be greater than ${expected}`);
    }
  },
  toBeGreaterThanOrEqual: (expected: number) => {
    if (actual < expected) {
      throw new Error(`Expected ${actual} to be greater than or equal to ${expected}`);
    }
  },
  toBeLessThan: (expected: number) => {
    if (actual >= expected) {
      throw new Error(`Expected ${actual} to be less than ${expected}`);
    }
  },
  toContain: (expected: any) => {
    if (!actual.includes(expected)) {
      throw new Error(`Expected ${actual} to contain ${expected}`);
    }
  }
});

const beforeAll = (fn: () => Promise<void> | void) => fn;
const afterAll = (fn: () => Promise<void> | void) => fn;
const beforeEach = (fn: () => Promise<void> | void) => fn;
import { readFileSync } from 'fs';
import { join } from 'path';
import { ExcelRepairService } from '../../lib/exceljsRepair';
import { AdvancedExcelRepairService } from '../../lib/advancedExceljsRepair';
import * as ExcelJS from 'exceljs';

// Mock file for testing
const createMockFile = (name: string, content: ArrayBuffer): File => {
  return new File([content], name, {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  });
};

// Create a simple Excel file for testing
const createTestExcelFile = async (): Promise<ArrayBuffer> => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Test Sheet');

  // Add some test data
  worksheet.getCell('A1').value = 'Name';
  worksheet.getCell('B1').value = 'Value';
  worksheet.getCell('A2').value = 'Test';
  worksheet.getCell('B2').value = 42;
  worksheet.getCell('A3').value = 'Formula';
  worksheet.getCell('B3').value = { formula: 'B2*2' };

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};

// Create a corrupted Excel file for testing
const createCorruptedExcelFile = async (): Promise<ArrayBuffer> => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Corrupted Sheet');

  // Add data with errors
  worksheet.getCell('A1').value = 'Name';
  worksheet.getCell('B1').value = 'Value';
  worksheet.getCell('A2').value = 'Error Test';
  worksheet.getCell('B2').value = { error: '#DIV/0!' };
  worksheet.getCell('A3').value = 'Bad Formula';
  worksheet.getCell('B3').value = { formula: 'INVALID_FUNCTION()' };
  worksheet.getCell('A4').value = 'Ref Error';
  worksheet.getCell('B4').value = { error: '#REF!' };

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};

describe('File Processing Integration Tests', () => {
  let testExcelBuffer: ArrayBuffer;
  let corruptedExcelBuffer: ArrayBuffer;

  beforeAll(async () => {
    // Create test files
    testExcelBuffer = await createTestExcelFile();
    corruptedExcelBuffer = await createCorruptedExcelFile();
  });

  describe('ExcelRepairService Integration', () => {
    it('should analyze a healthy Excel file', async () => {
      const file = createMockFile('test-healthy.xlsx', testExcelBuffer);
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(testExcelBuffer);

      const result = await ExcelRepairService.analyzeWorkbook(workbook, {}, file);

      expect(result.success).toBe(true);
      expect(result.repairLog).toBeDefined();
      expect(result.originalIssues).toBeGreaterThanOrEqual(0);
      expect(result.repairedIssues).toBeGreaterThanOrEqual(0);
    });

    it('should repair a corrupted Excel file', async () => {
      const file = createMockFile('test-corrupted.xlsx', corruptedExcelBuffer);
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(corruptedExcelBuffer);

      const result = await ExcelRepairService.repairWorkbook(workbook, {}, file);

      expect(result).toBeDefined();
      expect(result.repairLog).toBeDefined();
      expect(Array.isArray(result.repairLog)).toBe(true);

      if (result.success) {
        expect(result.repairedWorkbook).toBeDefined();
        expect(result.originalIssues).toBeGreaterThan(0);
        expect(result.repairedIssues).toBeGreaterThanOrEqual(0);
      }
    });

    it('should handle files with initial parse errors', async () => {
      const file = createMockFile('test-parse-error.xlsx', testExcelBuffer);

      const result = await ExcelRepairService.analyzeWorkbook(
        null,
        { initialParseError: 'File could not be parsed' },
        file
      );

      expect(result.success).toBe(true);
      expect(result.repairLog.length).toBeGreaterThan(0);
      expect(result.repairLog[0].issue).toContain('Initial parsing failed');
      expect(result.originalIssues).toBeGreaterThan(0);
    });

    it('should detect and log various types of errors', async () => {
      const file = createMockFile('test-errors.xlsx', corruptedExcelBuffer);
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(corruptedExcelBuffer);

      const result = await ExcelRepairService.analyzeWorkbook(workbook, {}, file);

      expect(result.repairLog.length).toBeGreaterThan(0);

      // Check for different types of issues
      const issueTypes = result.repairLog.map(log => log.issue);
      const hasErrorCells = issueTypes.some(issue => issue.includes('error'));
      const hasFormulaIssues = issueTypes.some(issue => issue.includes('formula') || issue.includes('Formula'));

      expect(hasErrorCells || hasFormulaIssues).toBe(true);
    });

    it('should apply repair options correctly', async () => {
      const file = createMockFile('test-options.xlsx', corruptedExcelBuffer);
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(corruptedExcelBuffer);

      const options = {
        removeErrors: true,
        fixFormulas: true,
        cleanEmptyRows: false,
        repairCorruption: true
      };

      const result = await ExcelRepairService.repairWorkbook(workbook, options, file);

      expect(result).toBeDefined();
      expect(result.repairLog).toBeDefined();

      if (result.success) {
        expect(result.repairedWorkbook).toBeDefined();
      }
    });
  });

  describe('AdvancedExcelRepairService Integration', () => {
    it('should attempt advanced repair on severely corrupted files', async () => {
      // Create a severely corrupted buffer (invalid Excel data)
      const corruptedBuffer = new ArrayBuffer(1024);
      const view = new Uint8Array(corruptedBuffer);
      view.fill(0xFF); // Fill with invalid data

      const file = createMockFile('severely-corrupted.xlsx', corruptedBuffer);

      const result = await AdvancedExcelRepairService.repairCorruptedExcel(file);

      expect(result).toBeDefined();
      expect(result.success).toBeDefined();
      expect(result.repairLog).toBeDefined();
      expect(Array.isArray(result.repairLog)).toBe(true);

      // Advanced repair should at least attempt to process the file
      expect(result.repairLog.length).toBeGreaterThan(0);
    });

    it('should handle empty files gracefully', async () => {
      const emptyBuffer = new ArrayBuffer(0);
      const file = createMockFile('empty.xlsx', emptyBuffer);

      const result = await AdvancedExcelRepairService.repairCorruptedExcel(file);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.repairLog).toBeDefined();
      expect(result.originalError).toBeDefined();
    });

    it('should provide detailed repair logs', async () => {
      const file = createMockFile('test-logs.xlsx', testExcelBuffer);

      const result = await AdvancedExcelRepairService.repairCorruptedExcel(file);

      expect(result.repairLog).toBeDefined();
      expect(Array.isArray(result.repairLog)).toBe(true);

      if (result.repairLog.length > 0) {
        const firstLog = result.repairLog[0];
        expect(firstLog.level).toBeDefined();
        expect(firstLog.message).toBeDefined();
        expect(firstLog.timestamp).toBeDefined();
        expect(firstLog.timestamp instanceof Date).toBe(true);
      }
    });
  });

  describe('End-to-End File Processing', () => {
    it('should process a complete repair workflow', async () => {
      const file = createMockFile('workflow-test.xlsx', corruptedExcelBuffer);

      // Step 1: Try standard repair
      let workbook: ExcelJS.Workbook | null = null;
      let parseError: string | null = null;

      try {
        workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(corruptedExcelBuffer);
      } catch (error) {
        parseError = error instanceof Error ? error.message : 'Parse error';
      }

      // Step 2: Analyze the file
      const analysisResult = await ExcelRepairService.analyzeWorkbook(
        workbook,
        parseError ? { initialParseError: parseError } : {},
        file
      );

      expect(analysisResult).toBeDefined();

      // Step 3: Attempt repair if issues found
      if (analysisResult.originalIssues > 0) {
        const repairResult = await ExcelRepairService.repairWorkbook(
          workbook,
          parseError ? { initialParseError: parseError } : {},
          file
        );

        expect(repairResult).toBeDefined();

        // Step 4: If standard repair fails, try advanced repair
        if (!repairResult.success) {
          const advancedResult = await AdvancedExcelRepairService.repairCorruptedExcel(file);
          expect(advancedResult).toBeDefined();
        }
      }
    });

    it('should handle large files efficiently', async () => {
      // Create a larger test file
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Large Sheet');

      // Add more data to simulate a larger file
      for (let row = 1; row <= 100; row++) {
        for (let col = 1; col <= 10; col++) {
          const cell = worksheet.getCell(row, col);
          cell.value = `Cell_${row}_${col}`;
        }
      }

      const largeBuffer = await workbook.xlsx.writeBuffer();
      const file = createMockFile('large-test.xlsx', largeBuffer);

      const startTime = Date.now();

      const loadedWorkbook = new ExcelJS.Workbook();
      await loadedWorkbook.xlsx.load(largeBuffer);

      const result = await ExcelRepairService.analyzeWorkbook(loadedWorkbook, {}, file);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(processingTime).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should preserve data integrity during repair', async () => {
      const file = createMockFile('integrity-test.xlsx', testExcelBuffer);
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(testExcelBuffer);

      // Get original data
      const originalSheet = workbook.getWorksheet('Test Sheet');
      const originalA1 = originalSheet?.getCell('A1').value;
      const originalB2 = originalSheet?.getCell('B2').value;

      const result = await ExcelRepairService.repairWorkbook(workbook, {}, file);

      if (result.success && result.repairedWorkbook) {
        const repairedSheet = result.repairedWorkbook.getWorksheet('Test Sheet');
        const repairedA1 = repairedSheet?.getCell('A1').value;
        const repairedB2 = repairedSheet?.getCell('B2').value;

        // Data should be preserved
        expect(repairedA1).toBe(originalA1);
        expect(repairedB2).toBe(originalB2);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid file types gracefully', async () => {
      const textBuffer = new TextEncoder().encode('This is not an Excel file');
      const file = createMockFile('not-excel.xlsx', textBuffer.buffer);

      const result = await AdvancedExcelRepairService.repairCorruptedExcel(file);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.repairLog).toBeDefined();
    });

    it('should handle memory constraints', async () => {
      // Create a file that might stress memory
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Memory Test');

      // Add a reasonable amount of data
      for (let row = 1; row <= 50; row++) {
        for (let col = 1; col <= 50; col++) {
          worksheet.getCell(row, col).value = `Data_${row}_${col}_${'x'.repeat(100)}`;
        }
      }

      const memoryTestBuffer = await workbook.xlsx.writeBuffer();
      const file = createMockFile('memory-test.xlsx', memoryTestBuffer);

      const loadedWorkbook = new ExcelJS.Workbook();
      await loadedWorkbook.xlsx.load(memoryTestBuffer);

      const result = await ExcelRepairService.analyzeWorkbook(loadedWorkbook, {}, file);

      expect(result).toBeDefined();
      // Should complete without throwing memory errors
    });

    it('should provide meaningful error messages', async () => {
      const invalidBuffer = new ArrayBuffer(10);
      const file = createMockFile('invalid.xlsx', invalidBuffer);

      const result = await AdvancedExcelRepairService.repairCorruptedExcel(file);

      expect(result.success).toBe(false);
      expect(result.originalError).toBeDefined();
      if (result.originalError) {
        expect(typeof result.originalError).toBe('string');
        expect(result.originalError.length).toBeGreaterThan(0);
      }
    });
  });

  describe('Performance and Reliability', () => {
    it('should complete analysis within reasonable time limits', async () => {
      const file = createMockFile('performance-test.xlsx', testExcelBuffer);
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(testExcelBuffer);

      const startTime = Date.now();
      const result = await ExcelRepairService.analyzeWorkbook(workbook, {}, file);
      const endTime = Date.now();

      expect(result).toBeDefined();
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should be consistent across multiple runs', async () => {
      const file = createMockFile('consistency-test.xlsx', corruptedExcelBuffer);
      const workbook1 = new ExcelJS.Workbook();
      const workbook2 = new ExcelJS.Workbook();

      await workbook1.xlsx.load(corruptedExcelBuffer);
      await workbook2.xlsx.load(corruptedExcelBuffer);

      const result1 = await ExcelRepairService.analyzeWorkbook(workbook1, {}, file);
      const result2 = await ExcelRepairService.analyzeWorkbook(workbook2, {}, file);

      expect(result1.success).toBe(result2.success);
      expect(result1.originalIssues).toBe(result2.originalIssues);
      expect(result1.repairLog.length).toBe(result2.repairLog.length);
    });

    it('should handle concurrent processing', async () => {
      const file1 = createMockFile('concurrent-1.xlsx', testExcelBuffer);
      const file2 = createMockFile('concurrent-2.xlsx', corruptedExcelBuffer);

      const workbook1 = new ExcelJS.Workbook();
      const workbook2 = new ExcelJS.Workbook();

      await workbook1.xlsx.load(testExcelBuffer);
      await workbook2.xlsx.load(corruptedExcelBuffer);

      const [result1, result2] = await Promise.all([
        ExcelRepairService.analyzeWorkbook(workbook1, {}, file1),
        ExcelRepairService.analyzeWorkbook(workbook2, {}, file2)
      ]);

      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      expect(result1.success).toBeDefined();
      expect(result2.success).toBeDefined();
    });
  });
});