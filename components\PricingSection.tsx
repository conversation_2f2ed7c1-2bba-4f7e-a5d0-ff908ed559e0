import React from 'react';
import { Link } from 'react-router-dom';
import { Check, ArrowRight } from 'lucide-react';

const PricingSection: React.FC = () => {
  const plans = [
    {
      name: 'One-Time Fix',
      description: 'Perfect for a single file',
      price: '$9',
      period: '/file',
      features: [
        'Single .xlsx file repair',
        'Standard processing speed',
        'Basic repair report',
        'Email support',
        '24-hour file storage'
      ],
      cta: 'Fix a Single File',
      popular: false
    },
    {
      name: 'Unlimited Monthly',
      description: 'Best for regular users',
      price: '$19',
      period: '/month',
      features: [
        'Unlimited file repairs',
        'Priority processing',
        'Detailed repair reports',
        'Priority email support',
        'File history (30 days)'
      ],
      cta: 'Go Unlimited',
      popular: true
    }
  ];

  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Simple, Transparent Pricing
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose the plan that&apos;s right for you. No hidden fees.
          </p>
        </div>
        
        {/* Pricing Cards */}
        <div className="flex flex-col md:flex-row justify-center items-stretch gap-8">
          {plans.map((plan, index) => (
            <div 
              key={index}
              className={`relative rounded-3xl p-8 w-full max-w-md flex flex-col ${
                plan.popular 
                  ? 'bg-gray-900 text-white border-2 border-lime-400' 
                  : 'bg-white border-2 border-gray-900'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-lime-400 text-gray-900 px-4 py-2 rounded-full text-sm font-medium">
                    Most Popular
                  </div>
                </div>
              )}
              
              <div className="text-center mb-8">
                <h3 className={`text-2xl font-bold mb-2 ${plan.popular ? 'text-white' : 'text-gray-900'}`}>
                  {plan.name}
                </h3>
                <p className={`text-sm mb-6 ${plan.popular ? 'text-gray-300' : 'text-gray-600'}`}>
                  {plan.description}
                </p>
                
                <div className="mb-6">
                  <span className={`text-4xl font-bold ${plan.popular ? 'text-white' : 'text-gray-900'}`}>
                    {plan.price}
                  </span>
                  <span className={`text-lg ${plan.popular ? 'text-gray-300' : 'text-gray-600'}`}>
                    {plan.period}
                  </span>
                </div>
              </div>

              <div className="space-y-4 mb-8 flex-grow">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-start gap-3">
                    <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mt-0.5 ${
                      plan.popular ? 'bg-lime-400' : 'bg-lime-400'
                    }`}>
                      <Check className="h-3 w-3 text-gray-900" />
                    </div>
                    <span className={`text-sm ${plan.popular ? 'text-gray-300' : 'text-gray-600'}`}>
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
        
              <Link
                to="/repair"
                className={`w-full inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors border ${
                  plan.popular
                    ? 'bg-lime-300 text-gray-900 border-lime-300 hover:bg-white hover:text-gray-900 hover:border-gray-900'
                    : 'bg-gray-900 text-white border-gray-900 hover:bg-lime-300 hover:text-gray-900 hover:border-lime-300'
                }`}
              >
                {plan.cta}
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
          ))}
        </div>
        
        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-gray-600">
            All plans include secure file processing and instant downloads.
          </p>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
