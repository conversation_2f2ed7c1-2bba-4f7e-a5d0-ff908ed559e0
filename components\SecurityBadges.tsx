import React from 'react';
import { motion } from 'framer-motion';
import Icon from './Icon';

interface TrustIndicator {
  icon: string;
  title: string;
  description: string;
}

const trustIndicators: TrustIndicator[] = [
  {
    icon: "fas fa-shield-alt",
    title: "Secure Processing",
    description: "Your files are processed securely and deleted automatically"
  },
  {
    icon: "fas fa-clock",
    title: "Fast Repairs",
    description: "Most files are fixed within seconds of upload"
  },
  {
    icon: "fas fa-file-excel",
    title: "Excel Files",
    description: "Works with .xlsx files (the most common format)"
  },
  {
    icon: "fas fa-browser",
    title: "No Downloads",
    description: "100% web-based, works in any modern browser"
  }
];

const SecurityBadges: React.FC = () => {
  return (
    <section className="py-12 bg-gray-50">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          
          <motion.div 
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-3">
              Simple, secure, effective
            </h2>
            <p className="text-gray-600">
              Everything you need to repair corrupted Excel files
            </p>
          </motion.div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {trustIndicators.map((indicator, index) => (
              <motion.div
                key={index}
                className="text-center p-6"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <div className="w-12 h-12 bg-white rounded-lg shadow-sm border border-gray-200 flex items-center justify-center mx-auto mb-4">
                  <Icon iconClass={`${indicator.icon} text-slate-600`} />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {indicator.title}
                </h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {indicator.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SecurityBadges;