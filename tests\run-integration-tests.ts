#!/usr/bin/env node

/**
 * Integration Test Runner
 * Runs all integration tests for the SheetHealer application
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

// Test configuration
const TEST_CONFIG = {
  timeout: 30000, // 30 seconds
  retries: 2,
  parallel: false
};

// Test suites to run
const TEST_SUITES = [
  {
    name: 'File Processing Integration Tests',
    file: 'tests/integration/fileProcessing.test.ts',
    description: 'Tests the complete file processing pipeline'
  },
  {
    name: 'API Integration Tests', 
    file: 'tests/integration/api.test.ts',
    description: 'Tests API endpoints with real HTTP requests'
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg: string) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg: string) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  error: (msg: string) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  warning: (msg: string) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  header: (msg: string) => console.log(`${colors.cyan}${colors.bright}${msg}${colors.reset}`),
  subheader: (msg: string) => console.log(`${colors.magenta}${msg}${colors.reset}`)
};

class TestRunner {
  private results: Array<{
    suite: string;
    passed: boolean;
    duration: number;
    error?: string;
  }> = [];

  async runSuite(suite: typeof TEST_SUITES[0]): Promise<boolean> {
    log.subheader(`\n📋 Running: ${suite.name}`);
    log.info(suite.description);

    if (!existsSync(suite.file)) {
      log.error(`Test file not found: ${suite.file}`);
      return false;
    }

    const startTime = Date.now();

    try {
      // Try to run with ts-node first, fallback to node
      let command = '';
      
      try {
        execSync('npx ts-node --version', { stdio: 'pipe' });
        command = `npx ts-node "${suite.file}"`;
      } catch {
        // Fallback to compiling and running
        log.warning('ts-node not available, compiling TypeScript...');
        const jsFile = suite.file.replace('.ts', '.js');
        execSync(`npx tsc "${suite.file}" --outDir temp --target es2020 --module commonjs --esModuleInterop --skipLibCheck`, { stdio: 'pipe' });
        command = `node "temp/${jsFile.replace('tests/', '')}"`;
      }

      execSync(command, { 
        stdio: 'inherit',
        timeout: TEST_CONFIG.timeout,
        cwd: process.cwd()
      });

      const duration = Date.now() - startTime;
      this.results.push({
        suite: suite.name,
        passed: true,
        duration
      });

      log.success(`${suite.name} completed in ${duration}ms`);
      return true;

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.results.push({
        suite: suite.name,
        passed: false,
        duration,
        error: errorMessage
      });

      log.error(`${suite.name} failed: ${errorMessage}`);
      return false;
    }
  }

  async runAllTests(): Promise<boolean> {
    log.header('🧪 SheetHealer Integration Test Suite');
    log.info(`Running ${TEST_SUITES.length} test suites...\n`);

    let allPassed = true;

    for (const suite of TEST_SUITES) {
      const passed = await this.runSuite(suite);
      if (!passed) {
        allPassed = false;
        
        if (TEST_CONFIG.retries > 0) {
          log.warning(`Retrying ${suite.name}...`);
          const retryPassed = await this.runSuite(suite);
          if (retryPassed) {
            allPassed = true;
          }
        }
      }

      // Add delay between test suites
      if (TEST_SUITES.indexOf(suite) < TEST_SUITES.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    this.printSummary();
    return allPassed;
  }

  private printSummary(): void {
    log.header('\n📊 Test Results Summary');
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`\nTotal Suites: ${this.results.length}`);
    console.log(`${colors.green}Passed: ${passed}${colors.reset}`);
    console.log(`${colors.red}Failed: ${failed}${colors.reset}`);
    console.log(`Total Duration: ${totalDuration}ms\n`);

    if (failed > 0) {
      log.subheader('Failed Test Suites:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  ❌ ${r.suite}`);
          if (r.error) {
            console.log(`     ${r.error}`);
          }
        });
    }

    // Performance analysis
    const slowTests = this.results.filter(r => r.duration > 5000);
    if (slowTests.length > 0) {
      log.subheader('\nSlow Test Suites (>5s):');
      slowTests.forEach(r => {
        console.log(`  🐌 ${r.suite}: ${r.duration}ms`);
      });
    }

    if (passed === this.results.length) {
      log.success('\n🎉 All integration tests passed!');
    } else {
      log.error('\n💥 Some integration tests failed!');
    }
  }
}

// Environment setup
const setupEnvironment = (): void => {
  log.info('Setting up test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.INTEGRATION_TEST = 'true';
  
  // Ensure required directories exist
  const requiredDirs = ['tests', 'tests/integration'];
  requiredDirs.forEach(dir => {
    if (!existsSync(dir)) {
      log.warning(`Creating directory: ${dir}`);
      execSync(`mkdir -p "${dir}"`, { stdio: 'pipe' });
    }
  });
};

// Cleanup function
const cleanup = (): void => {
  log.info('Cleaning up test environment...');
  
  // Remove temporary files
  try {
    execSync('rm -rf temp', { stdio: 'pipe' });
  } catch {
    // Ignore cleanup errors
  }
};

// Main execution
const main = async (): Promise<void> => {
  try {
    setupEnvironment();
    
    const runner = new TestRunner();
    const success = await runner.runAllTests();
    
    cleanup();
    
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    log.error(`Test runner failed: ${error instanceof Error ? error.message : String(error)}`);
    cleanup();
    process.exit(1);
  }
};

// Handle process signals
process.on('SIGINT', () => {
  log.warning('\nTest run interrupted by user');
  cleanup();
  process.exit(1);
});

process.on('SIGTERM', () => {
  log.warning('\nTest run terminated');
  cleanup();
  process.exit(1);
});

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { TestRunner, TEST_SUITES, TEST_CONFIG };