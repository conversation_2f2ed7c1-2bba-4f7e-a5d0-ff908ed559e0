/**
 * Analytics API Endpoint
 * Collects and processes analytics events
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import { validate<PERSON>rigin } from '../lib/csrf';
import { rateLimitMiddleware, RATE_LIMITS } from '../lib/rateLimiter';

interface AnalyticsEvent {
  id: string;
  type: string;
  timestamp: number;
  sessionId: string;
  userId?: string;
  properties: Record<string, any>;
  context: {
    url: string;
    userAgent: string;
    referrer: string;
    viewport: { width: number; height: number };
  };
}

interface AnalyticsBatch {
  events: AnalyticsEvent[];
  metadata?: {
    batchId: string;
    timestamp: number;
    source: string;
  };
}

export default async function handler(
  req: VercelRequest,
  res: VercelResponse
) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  // SECURITY: Rate limiting for analytics
  const rateLimitCheck = rateLimitMiddleware('analytics', RATE_LIMITS.EMAIL_FORM);
  if (!rateLimitCheck(req, res)) {
    return; // Rate limit exceeded
  }

  // SECURITY: Origin validation
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'https://sheethealer.com',
    'https://www.sheethealer.com'
  ];

  if (!validateOrigin(req, allowedOrigins)) {
    return res.status(403).json({
      success: false,
      error: 'Invalid origin'
    });
  }

  try {
    const batch: AnalyticsBatch = req.body;

    // Validate batch structure
    if (!batch.events || !Array.isArray(batch.events)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid batch format'
      });
    }

    // Process events
    const processedEvents = await processAnalyticsEvents(batch.events);

    // Store events (in production, this would go to a database or analytics service)
    await storeAnalyticsEvents(processedEvents);

    // Generate insights
    const insights = generateInsights(processedEvents);

    console.log(`📊 Processed ${processedEvents.length} analytics events`);

    return res.status(200).json({
      success: true,
      processed: processedEvents.length,
      insights
    });

  } catch (error) {
    console.error('Analytics processing error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to process analytics events'
    });
  }
}

async function processAnalyticsEvents(events: AnalyticsEvent[]): Promise<AnalyticsEvent[]> {
  return events.map(event => {
    // Validate and sanitize event data
    const processedEvent: any = {
      id: event.id || `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: sanitizeString(event.type),
      timestamp: event.timestamp || Date.now(),
      sessionId: sanitizeString(event.sessionId),
      ...(event.userId && { userId: sanitizeString(event.userId) }),
      properties: sanitizeProperties(event.properties || {}),
      context: {
        url: sanitizeString(event.context?.url || ''),
        userAgent: sanitizeString(event.context?.userAgent || ''),
        referrer: sanitizeString(event.context?.referrer || ''),
        viewport: {
          width: Math.max(0, Math.min(10000, event.context?.viewport?.width || 0)),
          height: Math.max(0, Math.min(10000, event.context?.viewport?.height || 0))
        }
      }
    };

    return processedEvent;
  });
}

function sanitizeString(input: string): string {
  if (typeof input !== 'string') return '';
  return input
    .trim()
    .slice(0, 1000) // Limit length
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, ''); // Remove control characters
}

function sanitizeProperties(properties: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};

  for (const [key, value] of Object.entries(properties)) {
    const sanitizedKey = sanitizeString(key);
    if (sanitizedKey.length === 0) continue;

    if (typeof value === 'string') {
      sanitized[sanitizedKey] = sanitizeString(value);
    } else if (typeof value === 'number' && isFinite(value)) {
      sanitized[sanitizedKey] = value;
    } else if (typeof value === 'boolean') {
      sanitized[sanitizedKey] = value;
    } else if (value === null || value === undefined) {
      sanitized[sanitizedKey] = null;
    } else {
      // Convert complex objects to strings
      sanitized[sanitizedKey] = sanitizeString(JSON.stringify(value));
    }
  }

  return sanitized;
}

async function storeAnalyticsEvents(events: AnalyticsEvent[]): Promise<void> {
  // In production, this would store events in a database or send to analytics service
  // For now, we'll just log them

  const eventSummary = events.reduce((acc, event) => {
    acc[event.type] = (acc[event.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  console.log('📊 Analytics Event Summary:', eventSummary);

  // Store in memory for development (would be database in production)
  if (process.env.NODE_ENV === 'development') {
    global.analyticsEvents = global.analyticsEvents || [];
    global.analyticsEvents.push(...events);

    // Keep only last 1000 events in memory
    if (global.analyticsEvents.length > 1000) {
      global.analyticsEvents = global.analyticsEvents.slice(-1000);
    }
  }
}

function generateInsights(events: AnalyticsEvent[]): Record<string, any> {
  const insights: Record<string, any> = {
    totalEvents: events.length,
    eventTypes: {},
    timeRange: {
      start: Math.min(...events.map(e => e.timestamp)),
      end: Math.max(...events.map(e => e.timestamp))
    },
    uniqueSessions: new Set(events.map(e => e.sessionId)).size,
    uniqueUsers: new Set(events.filter(e => e.userId).map(e => e.userId)).size
  };

  // Count event types
  events.forEach(event => {
    insights.eventTypes[event.type] = (insights.eventTypes[event.type] || 0) + 1;
  });

  // Performance insights
  const performanceEvents = events.filter(e => e.type === 'performance_metric');
  if (performanceEvents.length > 0) {
    insights.performance = {
      totalMetrics: performanceEvents.length,
      avgPageLoadTime: calculateAverage(performanceEvents, 'page_load_time'),
      avgApiResponseTime: calculateAverage(performanceEvents, 'api_response_time')
    };
  }

  // Error insights
  const errorEvents = events.filter(e => e.type === 'error_occurred');
  if (errorEvents.length > 0) {
    insights.errors = {
      totalErrors: errorEvents.length,
      errorRate: (errorEvents.length / events.length * 100).toFixed(2) + '%',
      topErrors: getTopErrors(errorEvents)
    };
  }

  // File processing insights
  const fileEvents = events.filter(e =>
    e.type.includes('file_') ||
    e.type === 'file_upload_start' ||
    e.type === 'file_repair_complete'
  );

  if (fileEvents.length > 0) {
    insights.fileProcessing = {
      totalFileOperations: fileEvents.length,
      uploadEvents: fileEvents.filter(e => e.type === 'file_upload_start').length,
      repairEvents: fileEvents.filter(e => e.type === 'file_repair_complete').length,
      successRate: calculateFileSuccessRate(fileEvents)
    };
  }

  return insights;
}

function calculateAverage(events: AnalyticsEvent[], metricName: string): number {
  const values = events
    .filter(e => e.properties.metricName === metricName)
    .map(e => e.properties.value)
    .filter(v => typeof v === 'number' && isFinite(v));

  if (values.length === 0) return 0;
  return Math.round(values.reduce((sum, val) => sum + val, 0) / values.length);
}

function getTopErrors(errorEvents: AnalyticsEvent[]): Array<{ message: string; count: number }> {
  const errorCounts: Record<string, number> = {};

  errorEvents.forEach(event => {
    const message = event.properties.message || 'Unknown error';
    errorCounts[message] = (errorCounts[message] || 0) + 1;
  });

  return Object.entries(errorCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([message, count]) => ({ message, count }));
}

function calculateFileSuccessRate(fileEvents: AnalyticsEvent[]): string {
  const repairEvents = fileEvents.filter(e => e.type === 'file_repair_complete');
  if (repairEvents.length === 0) return '0%';

  const successfulRepairs = repairEvents.filter(e => e.properties.success === true).length;
  return ((successfulRepairs / repairEvents.length) * 100).toFixed(1) + '%';
}

// Extend global type for development storage
declare global {
  var analyticsEvents: AnalyticsEvent[] | undefined;
}