/**
 * Monitoring Dashboard Component
 * Displays real-time analytics and performance metrics
 */

import React, { useState, useEffect } from 'react';
import { analytics, getAnalytics } from '../lib/analytics';

interface MetricCard {
  title: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  color?: 'green' | 'red' | 'blue' | 'yellow';
}

interface PerformanceData {
  pageLoadTime: number;
  apiResponseTime: number;
  memoryUsage: number;
  errorRate: number;
  userSessions: number;
  fileProcessingSuccess: number;
}

export const MonitoringDashboard: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [performanceData, setPerformanceData] = useState<PerformanceData>({
    pageLoadTime: 0,
    apiResponseTime: 0,
    memoryUsage: 0,
    errorRate: 0,
    userSessions: 0,
    fileProcessingSuccess: 0
  });

  const [recentEvents, setRecentEvents] = useState<Array<{
    type: string;
    timestamp: number;
    message: string;
  }>>([]);

  useEffect(() => {
    // Only show in development or when explicitly enabled
    const shouldShow = process.env.NODE_ENV === 'development' || 
                      localStorage.getItem('show-monitoring') === 'true';
    setIsVisible(shouldShow);

    if (shouldShow) {
      // Simulate real-time data updates
      const interval = setInterval(() => {
        updatePerformanceData();
      }, 5000);

      return () => clearInterval(interval);
    }
  }, []);

  const updatePerformanceData = () => {
    // Simulate performance data (in real app, this would come from analytics)
    setPerformanceData({
      pageLoadTime: Math.random() * 2000 + 500,
      apiResponseTime: Math.random() * 1000 + 200,
      memoryUsage: Math.random() * 100,
      errorRate: Math.random() * 5,
      userSessions: Math.floor(Math.random() * 100) + 50,
      fileProcessingSuccess: Math.random() * 100
    });

    // Add a recent event
    const eventTypes = ['file_upload', 'file_repair', 'error', 'page_view'];
    const randomType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
    
    setRecentEvents(prev => [
      {
        type: randomType,
        timestamp: Date.now(),
        message: `${randomType.replace('_', ' ')} event occurred`
      },
      ...prev.slice(0, 9) // Keep only last 10 events
    ]);
  };

  const getMetricCards = (): MetricCard[] => [
    {
      title: 'Page Load Time',
      value: Math.round(performanceData.pageLoadTime),
      unit: 'ms',
      trend: performanceData.pageLoadTime < 1000 ? 'up' : 'down',
      color: performanceData.pageLoadTime < 1000 ? 'green' : 'red'
    },
    {
      title: 'API Response',
      value: Math.round(performanceData.apiResponseTime),
      unit: 'ms',
      trend: performanceData.apiResponseTime < 500 ? 'up' : 'down',
      color: performanceData.apiResponseTime < 500 ? 'green' : 'yellow'
    },
    {
      title: 'Memory Usage',
      value: Math.round(performanceData.memoryUsage),
      unit: '%',
      trend: performanceData.memoryUsage < 70 ? 'up' : 'down',
      color: performanceData.memoryUsage < 70 ? 'green' : 'red'
    },
    {
      title: 'Error Rate',
      value: performanceData.errorRate.toFixed(1),
      unit: '%',
      trend: performanceData.errorRate < 2 ? 'up' : 'down',
      color: performanceData.errorRate < 2 ? 'green' : 'red'
    },
    {
      title: 'Active Sessions',
      value: performanceData.userSessions,
      trend: 'stable',
      color: 'blue'
    },
    {
      title: 'Success Rate',
      value: Math.round(performanceData.fileProcessingSuccess),
      unit: '%',
      trend: performanceData.fileProcessingSuccess > 90 ? 'up' : 'down',
      color: performanceData.fileProcessingSuccess > 90 ? 'green' : 'yellow'
    }
  ];

  const toggleDashboard = () => {
    setIsVisible(!isVisible);
    localStorage.setItem('show-monitoring', (!isVisible).toString());
  };

  if (!isVisible) {
    return (
      <button
        onClick={toggleDashboard}
        className="fixed bottom-4 right-4 bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-50"
        title="Show Monitoring Dashboard"
      >
        📊
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-96 max-h-96 overflow-y-auto z-50">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">📊 Monitoring</h3>
        <button
          onClick={toggleDashboard}
          className="text-gray-500 hover:text-gray-700 text-xl"
          title="Hide Dashboard"
        >
          ×
        </button>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        {getMetricCards().map((metric, index) => (
          <div
            key={index}
            className={`p-2 rounded border-l-4 ${
              metric.color === 'green' ? 'border-green-500 bg-green-50' :
              metric.color === 'red' ? 'border-red-500 bg-red-50' :
              metric.color === 'yellow' ? 'border-yellow-500 bg-yellow-50' :
              'border-blue-500 bg-blue-50'
            }`}
          >
            <div className="text-xs text-gray-600 mb-1">{metric.title}</div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-semibold">
                {metric.value}{metric.unit}
              </span>
              {metric.trend && (
                <span className={`text-xs ${
                  metric.trend === 'up' ? 'text-green-600' :
                  metric.trend === 'down' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {metric.trend === 'up' ? '↗' : metric.trend === 'down' ? '↘' : '→'}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Recent Events */}
      <div>
        <h4 className="text-sm font-semibold text-gray-700 mb-2">Recent Events</h4>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {recentEvents.map((event, index) => (
            <div key={index} className="text-xs p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center">
                <span className="font-medium">{event.type}</span>
                <span className="text-gray-500">
                  {new Date(event.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div className="text-gray-600 mt-1">{event.message}</div>
            </div>
          ))}
          {recentEvents.length === 0 && (
            <div className="text-xs text-gray-500 text-center py-2">
              No recent events
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex space-x-2">
          <button
            onClick={() => analytics.track('user_interaction', { action: 'clear_events', element: 'monitoring_dashboard' })}
            className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
          >
            Clear Events
          </button>
          <button
            onClick={() => {
              analytics.trackPerformance({
                name: 'manual_performance_check',
                value: Date.now(),
                unit: 'ms',
                timestamp: Date.now()
              });
            }}
            className="text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded"
          >
            Track Event
          </button>
        </div>
      </div>
    </div>
  );
};

export default MonitoringDashboard;