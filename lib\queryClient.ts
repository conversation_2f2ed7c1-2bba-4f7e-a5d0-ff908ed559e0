import { QueryClient } from '@tanstack/react-query';

// Create a client with optimized defaults for our file processing use case
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Disable automatic refetching for file operations
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      
      // Set longer stale time for file analysis results (5 minutes)
      staleTime: 5 * 60 * 1000,
      
      // Cache file analysis results for 10 minutes
      gcTime: 10 * 60 * 1000,
      
      // Retry failed requests with exponential backoff
      retry: (failureCount, error: any) => {
        // Don't retry client errors (4xx)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        
        // Retry server errors up to 2 times
        return failureCount < 2;
      },
      
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // Retry mutations once for network errors
      retry: (failureCount, error: any) => {
        // Don't retry client errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        
        // Retry once for server errors
        return failureCount < 1;
      },
      
      retryDelay: 2000,
    },
  },
});

// Query keys factory for consistent key management
export const queryKeys = {
  // File analysis queries
  fileAnalysis: (fileHash: string) => ['file-analysis', fileHash] as const,
  
  // File repair queries  
  fileRepair: (fileHash: string) => ['file-repair', fileHash] as const,
  
  // CSRF token
  csrfToken: () => ['csrf-token'] as const,
  
  // Form submissions
  formSubmission: (formType: string) => ['form-submission', formType] as const,
} as const;

// Helper to generate file hash for caching
export const generateFileHash = async (file: File): Promise<string> => {
  const buffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};