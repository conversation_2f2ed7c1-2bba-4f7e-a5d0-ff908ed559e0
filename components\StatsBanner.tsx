import React from 'react';
import { STATS_DATA } from '../constants';
import { StatItem } from '../types';

const StatsBanner: React.FC = () => {
  return (
    <div className="bg-white/20 backdrop-blur-xl shadow-2xl border-y border-white/30">
      <div className="container mx-auto px-4 sm:px-6 py-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          {STATS_DATA.map((stat: StatItem) => (
            <div key={stat.label} className="p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">{stat.value}</div>
              <div className="text-gray-700 text-sm">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default StatsBanner;
