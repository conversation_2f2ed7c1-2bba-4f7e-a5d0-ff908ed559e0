import React, { FC, MouseEvent } from 'react';
import Icon from './Icon';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const Modal: FC<ModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  const handleBackdropClick = (event: MouseEvent<HTMLDivElement>) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/70 z-50 flex items-start sm:items-center justify-center p-0 sm:p-6 overflow-y-auto"
      onClick={handleBackdropClick}
    >
      <div 
        className="bg-gray-900 rounded-t-2xl sm:rounded-2xl shadow-2xl w-full max-w-lg relative border border-gray-700 p-6 sm:p-8 transform transition-all duration-300 scale-100 opacity-100 mt-auto sm:mt-0 min-h-[85vh] sm:min-h-0 sm:max-h-[90vh] overflow-y-auto"
        // Prevent click from propagating to backdrop
        onClick={e => e.stopPropagation()}
      >
        {/* Close Button */}
        <button 
          onClick={onClose} 
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors p-2 rounded-full hover:bg-gray-800"
        >
          <Icon iconClass="fas fa-times text-xl" />
        </button>

        {/* Modal Body */}
        <div>
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
