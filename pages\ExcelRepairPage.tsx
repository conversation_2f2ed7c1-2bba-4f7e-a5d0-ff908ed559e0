import React, { useState, useCallback, useRef, useEffect } from 'react';
import { ProcessedFileResult, FileUploadHandles } from '../components/FileUpload';
import ErrorBoundary from '../components/ErrorBoundary';
import { ErrorBoundaryProvider } from '../components/ErrorBoundaries/ErrorBoundaryProvider';
import UpgradeModal from '../components/UpgradeModal';
import RepairPageHeader from '../components/repair-page/RepairPageHeader';
import RepairPageContent from '../components/repair-page/RepairPageContent';
import { RepairPageFileUpload } from '../components/repair-page/RepairPageFileUpload';
import { useKeyboardNavigation, AnnouncementManager } from '../lib/keyboardNavigation';
import { KeyboardShortcutsButton } from '../components/KeyboardShortcutsHelp';

export const ExcelRepairPage: React.FC = () => {
  const [processedFile, setProcessedFile] = useState<ProcessedFileResult | null>(null);
  const [uploadedRawFile, setUploadedRawFile] = useState<File | null>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [isRepairing, setIsRepairing] = useState(false);
  const fileUploadRef = useRef<FileUploadHandles>(null); // Ref for FileUpload component

  // Keyboard navigation
  const { registerShortcut, removeShortcut, announce, focusFirst } = useKeyboardNavigation();

  const handleFileProcessed = useCallback((result: ProcessedFileResult) => {
    console.log('📊 File processed:', result);
    setProcessedFile(result);
    setUploadedRawFile(result.originalFile);

    // Announce result to screen readers
    if (result.status === 'success') {
      announce(`File ${result.originalFile.name} processed successfully`);
    } else {
      announce(`Error processing file ${result.originalFile.name}: ${result.message}`);
    }
  }, [announce]);

  const handleReset = useCallback(() => {
    console.log('🔄 Resetting file upload state...');
    setProcessedFile(null);
    setUploadedRawFile(null);
    setIsRepairing(false);

    // Announce reset to screen readers
    announce('File upload reset. Ready for new file.');

    // Also reset the file upload component if it has a reset method
    if (fileUploadRef.current) {
      console.log('🧹 Clearing FileUpload component state');
      fileUploadRef.current.reset();
    }

    // Focus the upload area
    setTimeout(() => {
      const uploadArea = document.querySelector('[role="button"][aria-label*="upload"]') as HTMLElement;
      if (uploadArea) {
        uploadArea.focus();
      }
    }, 100);
  }, [announce]);

  const handleRepairTrigger = useCallback(async (file: File) => {
    console.log('🔧 Repair triggered for file:', file.name);
    console.log('📝 FileUpload ref status:', fileUploadRef.current ? 'Available' : 'NULL');

    // Announce repair start
    announce(`Starting repair for file ${file.name}`);

    if (fileUploadRef.current) {
      try {
        setIsRepairing(true);
        console.log('🚀 Starting repair process...');
        const repairedResult = await fileUploadRef.current.repairFile(file);
        console.log('✅ Repair completed:', repairedResult);
        setProcessedFile(repairedResult);

        // Announce repair completion
        if (repairedResult.status === 'success') {
          announce(`File ${file.name} repaired successfully`);
        } else {
          announce(`Repair failed for file ${file.name}`);
        }
      } catch (error) {
        console.error('❌ Repair failed:', error);
        const errorMessage = `Repair failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        announce(errorMessage);
        alert(errorMessage);
      } finally {
        setIsRepairing(false);
      }
    } else {
      console.error('❌ fileUploadRef.current is null. Cannot initiate repair.');
      const errorMessage = 'Unable to start repair. Please try refreshing the page.';
      announce(errorMessage);
      alert(errorMessage);
    }
  }, [announce]);

  // Check if file has been actually repaired (not just analyzed)
  const isFileFixed = processedFile && processedFile.status === 'success' && processedFile.workbook && processedFile.repairReport && (
    // Only consider it fixed if actual repair actions were taken (not analysis)
    processedFile.repairReport.sections.some(section =>
      section.issues.some(issue =>
        (issue.actionTaken.includes('Removed') ||
         issue.actionTaken.includes('Fixed') ||
         issue.actionTaken.includes('Applied') ||
         issue.actionTaken.includes('Repaired') ||
         issue.actionTaken.includes('Advanced repair')) &&
        !issue.actionTaken.includes('We can') // Exclude analysis-only messages
      )
    ) ||
    // Also check if the message explicitly indicates repair success
    (processedFile.message && (
      processedFile.message.includes('successfully repaired') ||
      processedFile.message.includes('File successfully repaired') ||
      processedFile.message.includes('successfully fixed') ||
      processedFile.message.includes('Recovered') && processedFile.message.includes('cells')
    ))
  );

  // Check if file needs repair (has actual problems that can be fixed)
  const needsRepair = processedFile && (
    // Check if there are errors in the sheets
    (processedFile.sheets && processedFile.sheets.some(sheet => sheet.errors.length > 0)) ||
    // Or check repair report for actionable issues that aren't just analysis messages
    (processedFile.repairReport &&
     processedFile.repairReport.sections.some(section =>
       section.issues.some(issue =>
         // Only count actual repair-worthy issues, not analysis summaries
         (issue.actionTaken.includes('We can') ||
          issue.actionTaken.includes('We found') ||
          issue.actionTaken.includes('We estimated')) &&
         !issue.actionTaken.includes('We checked') &&
         !issue.actionTaken.includes('We scanned') &&
         !issue.description.includes('Analysis complete') &&
         (issue.severity === 'Critical' ||
          issue.severity === 'Major' ||
          issue.severity === 'Minor' ||
          issue.type.includes('Error') ||
          issue.description.includes('Error') ||
          issue.description.includes('#DIV/0!') ||
          issue.description.includes('#NAME?') ||
          issue.description.includes('#REF!') ||
          issue.description.includes('#VALUE!') ||
          issue.description.includes('#NUM!') ||
          issue.description.includes('#NULL!'))
       )
     ))
  ) && !isFileFixed;

  // Check if file is healthy (no problems found)
  const isHealthyFile = processedFile && processedFile.status === 'success' && (
    // No errors found in sheets
    (!processedFile.sheets || processedFile.sheets.every(sheet => sheet.errors.length === 0)) &&
    // And repair report shows no real issues
    (!processedFile.repairReport ||
     processedFile.repairReport.sections.every(section =>
       section.issues.every(issue =>
         issue.actionTaken.includes('We checked') ||
         issue.description.includes('No problems detected') ||
         issue.description.includes('perfectly healthy')
       )
     ))
  ) && !needsRepair && !isFileFixed;

  // Setup keyboard shortcuts
  useEffect(() => {
    // Skip links removed per user request

    // Register keyboard shortcuts
    registerShortcut({
      key: 'u',
      altKey: true,
      action: () => {
        const uploadArea = document.querySelector('[role="button"][aria-label*="upload"]') as HTMLElement;
        if (uploadArea) {
          uploadArea.click();
        }
      },
      description: 'Open file upload dialog',
      category: 'file'
    });

    registerShortcut({
      key: 'r',
      altKey: true,
      action: () => {
        if (processedFile && needsRepair) {
          handleRepairTrigger(processedFile.originalFile);
        }
      },
      description: 'Repair current file',
      category: 'file'
    });

    registerShortcut({
      key: 'n',
      altKey: true,
      action: handleReset,
      description: 'Start with new file',
      category: 'file'
    });

    registerShortcut({
      key: 'd',
      altKey: true,
      action: () => {
        const downloadButton = document.querySelector('button[aria-label*="Download"]') as HTMLButtonElement;
        if (downloadButton && !downloadButton.disabled) {
          downloadButton.click();
        }
      },
      description: 'Download repaired file',
      category: 'file'
    });

    registerShortcut({
      key: '?',
      action: () => {
        const helpButton = document.querySelector('button[aria-label*="keyboard shortcuts"]') as HTMLElement;
        if (helpButton) {
          helpButton.click();
        }
      },
      description: 'Show keyboard shortcuts help',
      category: 'general'
    });

    registerShortcut({
      key: 'h',
      altKey: true,
      action: () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
        focusFirst();
      },
      description: 'Go to top of page',
      category: 'navigation'
    });

    // Cleanup shortcuts on unmount
    return () => {
      removeShortcut('u', { altKey: true });
      removeShortcut('r', { altKey: true });
      removeShortcut('n', { altKey: true });
      removeShortcut('d', { altKey: true });
      removeShortcut('?');
      removeShortcut('h', { altKey: true });
    };
  }, [registerShortcut, removeShortcut, handleReset, handleRepairTrigger, processedFile, needsRepair, focusFirst]);

  return (
    <ErrorBoundary>
      <div className="bg-white min-h-screen flex flex-col">
        <RepairPageHeader />

        <main id="main-content" className="flex-1 flex items-start py-8 sm:py-16 lg:py-20">
          <div className="max-w-7xl mx-auto px-4 md:px-8 lg:px-12 w-full">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 lg:gap-20 items-start">
              <RepairPageContent processedFile={!!processedFile} needsRepair={!!needsRepair} isFileFixed={!!isFileFixed} isHealthyFile={!!isHealthyFile} />
              <div id="file-upload-area">
                <ErrorBoundaryProvider type="file-upload">
                  <RepairPageFileUpload
                    processedFile={processedFile}
                    onFileProcessed={handleFileProcessed}
                    onRepairFile={handleRepairTrigger}
                    onAnalyzeAnother={handleReset}
                    isRepairing={isRepairing}
                    ref={fileUploadRef}
                  />
                </ErrorBoundaryProvider>
              </div>
            </div>
          </div>
        </main>

        <UpgradeModal isOpen={showUpgradeModal} onClose={() => setShowUpgradeModal(false)} />

        {/* Keyboard Shortcuts Help */}
        <KeyboardShortcutsButton />
      </div>
    </ErrorBoundary>
  );
};
