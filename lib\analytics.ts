/**
 * Analytics and Monitoring System for SheetHealer
 * Provides comprehensive tracking of user interactions, performance metrics, and errors
 */

import { log } from './logger';

// Analytics configuration
interface AnalyticsConfig {
  enabled: boolean;
  debug: boolean;
  trackingId?: string | undefined;
  apiEndpoint?: string;
  batchSize: number;
  flushInterval: number;
  enablePerformanceTracking: boolean;
  enableErrorTracking: boolean;
  enableUserTracking: boolean;
}

// Event types
export type EventType =
  | 'page_view'
  | 'file_upload_start'
  | 'file_upload_complete'
  | 'file_analysis_start'
  | 'file_analysis_complete'
  | 'file_repair_start'
  | 'file_repair_complete'
  | 'file_download'
  | 'form_submit'
  | 'error_occurred'
  | 'performance_metric'
  | 'user_interaction'
  | 'feature_usage';

// Event data structure
interface AnalyticsEvent {
  id: string;
  type: EventType;
  timestamp: number;
  sessionId: string;
  userId?: string | undefined;
  properties: Record<string, any>;
  context: {
    url: string;
    userAgent: string;
    referrer: string;
    viewport: { width: number; height: number };
    connection?: string;
  };
}

// Performance metrics
interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: number;
  context?: Record<string, any>;
}

// Error tracking
interface ErrorEvent {
  message: string;
  stack?: string;
  filename?: string;
  lineno?: number;
  colno?: number;
  timestamp: number;
  context?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class AnalyticsManager {
  private config: AnalyticsConfig;
  private eventQueue: AnalyticsEvent[] = [];
  private sessionId: string;
  private userId?: string;
  private flushTimer?: NodeJS.Timeout;
  private performanceObserver?: PerformanceObserver;

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = {
      enabled: process.env.NODE_ENV === 'production',
      debug: process.env.NODE_ENV === 'development',
      batchSize: 10,
      flushInterval: 30000, // 30 seconds
      enablePerformanceTracking: true,
      enableErrorTracking: true,
      enableUserTracking: true,
      ...config
    };

    this.sessionId = this.generateSessionId();
    this.initialize();
  }

  private initialize(): void {
    if (!this.config.enabled) {
      if (this.config.debug) {
        console.log('📊 Analytics disabled');
      }
      return;
    }

    // Set up automatic flushing
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);

    // Set up performance tracking
    if (this.config.enablePerformanceTracking && typeof window !== 'undefined') {
      this.setupPerformanceTracking();
    }

    // Set up error tracking
    if (this.config.enableErrorTracking && typeof window !== 'undefined') {
      this.setupErrorTracking();
    }

    // Track initial page view
    this.trackPageView();

    if (this.config.debug) {
      console.log('📊 Analytics initialized', {
        sessionId: this.sessionId,
        config: this.config
      });
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getContext(): AnalyticsEvent['context'] {
    if (typeof window === 'undefined') {
      return {
        url: '',
        userAgent: '',
        referrer: '',
        viewport: { width: 0, height: 0 }
      };
    }

    return {
      url: window.location.href,
      userAgent: navigator.userAgent,
      referrer: document.referrer,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      connection: (navigator as any).connection?.effectiveType
    };
  }

  // Public API
  setUserId(userId: string): void {
    this.userId = userId;
    if (this.config.debug) {
      console.log('📊 User ID set:', userId);
    }
  }

  track(type: EventType, properties: Record<string, any> = {}): void {
    if (!this.config.enabled) return;

    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      type,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      properties,
      context: this.getContext()
    };

    this.eventQueue.push(event);

    // Log to our internal logging system
    log.info('analytics', `Event tracked: ${type}`, properties);

    if (this.config.debug) {
      console.log('📊 Event tracked:', event);
    }

    // Auto-flush if queue is full
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flush();
    }
  }

  trackPageView(page?: string): void {
    const url = page || (typeof window !== 'undefined' ? window.location.pathname : '');
    this.track('page_view', { page: url });
  }

  trackFileUpload(fileName: string, fileSize: number, fileType: string): void {
    this.track('file_upload_start', {
      fileName,
      fileSize,
      fileType,
      fileSizeMB: Math.round(fileSize / (1024 * 1024) * 100) / 100
    });
  }

  trackFileAnalysis(fileName: string, duration: number, issuesFound: number): void {
    this.track('file_analysis_complete', {
      fileName,
      duration,
      issuesFound,
      durationSeconds: Math.round(duration / 1000 * 100) / 100
    });
  }

  trackFileRepair(fileName: string, duration: number, issuesRepaired: number, success: boolean): void {
    this.track('file_repair_complete', {
      fileName,
      duration,
      issuesRepaired,
      success,
      durationSeconds: Math.round(duration / 1000 * 100) / 100
    });
  }

  trackError(error: Error | string, context?: Record<string, any>, severity: ErrorEvent['severity'] = 'medium'): void {
    const errorMessage = error instanceof Error ? error.message : error;
    const stack = error instanceof Error ? error.stack : undefined;

    this.track('error_occurred', {
      message: errorMessage,
      stack,
      severity,
      context
    });
  }

  trackPerformance(metric: PerformanceMetric): void {
    this.track('performance_metric', {
      metricName: metric.name,
      value: metric.value,
      unit: metric.unit,
      context: metric.context
    });
  }

  trackUserInteraction(action: string, element: string, context?: Record<string, any>): void {
    this.track('user_interaction', {
      action,
      element,
      ...context
    });
  }

  trackFeatureUsage(feature: string, context?: Record<string, any>): void {
    this.track('feature_usage', {
      feature,
      ...context
    });
  }

  // Performance tracking setup
  private setupPerformanceTracking(): void {
    // Track Core Web Vitals
    this.trackWebVitals();

    // Track custom performance metrics
    this.trackCustomMetrics();

    // Set up Performance Observer
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.handlePerformanceEntry(entry);
        }
      });

      this.performanceObserver.observe({ entryTypes: ['navigation', 'resource', 'measure'] });
    }
  }

  private trackWebVitals(): void {
    // First Contentful Paint (FCP)
    this.observePerformanceMetric('first-contentful-paint', (value) => {
      this.trackPerformance({
        name: 'first_contentful_paint',
        value,
        unit: 'ms',
        timestamp: Date.now()
      });
    });

    // Largest Contentful Paint (LCP)
    this.observePerformanceMetric('largest-contentful-paint', (value) => {
      this.trackPerformance({
        name: 'largest_contentful_paint',
        value,
        unit: 'ms',
        timestamp: Date.now()
      });
    });

    // Cumulative Layout Shift (CLS)
    this.observeLayoutShift();
  }

  private observePerformanceMetric(name: string, callback: (value: number) => void): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === name) {
            callback(entry.startTime);
          }
        }
      });
      observer.observe({ entryTypes: ['paint'] });
    }
  }

  private observeLayoutShift(): void {
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.trackPerformance({
          name: 'cumulative_layout_shift',
          value: clsValue,
          unit: 'count',
          timestamp: Date.now()
        });
      });
      observer.observe({ entryTypes: ['layout-shift'] });
    }
  }

  private trackCustomMetrics(): void {
    // Track memory usage
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.trackPerformance({
          name: 'memory_usage',
          value: memory.usedJSHeapSize,
          unit: 'bytes',
          timestamp: Date.now(),
          context: {
            totalJSHeapSize: memory.totalJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit
          }
        });
      }, 60000); // Every minute
    }

    // Track connection quality
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.trackPerformance({
        name: 'connection_speed',
        value: connection.downlink || 0,
        unit: 'count',
        timestamp: Date.now(),
        context: {
          effectiveType: connection.effectiveType,
          rtt: connection.rtt
        }
      });
    }
  }

  private handlePerformanceEntry(entry: PerformanceEntry): void {
    if (entry.entryType === 'navigation') {
      const navEntry = entry as PerformanceNavigationTiming;
      this.trackPerformance({
        name: 'page_load_time',
        value: navEntry.loadEventEnd - navEntry.fetchStart,
        unit: 'ms',
        timestamp: Date.now(),
        context: {
          domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.fetchStart,
          firstByte: navEntry.responseStart - navEntry.fetchStart
        }
      });
    }

    if (entry.entryType === 'resource') {
      const resourceEntry = entry as PerformanceResourceTiming;
      if (resourceEntry.name.includes('api/')) {
        this.trackPerformance({
          name: 'api_response_time',
          value: resourceEntry.responseEnd - resourceEntry.requestStart,
          unit: 'ms',
          timestamp: Date.now(),
          context: {
            url: resourceEntry.name,
            size: resourceEntry.transferSize
          }
        });
      }
    }
  }

  // Error tracking setup
  private setupErrorTracking(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.trackError(event.error || event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'javascript_error'
      }, 'high');
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(event.reason, {
        type: 'unhandled_promise_rejection'
      }, 'high');
    });

    // Resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.trackError(`Resource failed to load: ${(event.target as any)?.src || (event.target as any)?.href}`, {
          type: 'resource_error',
          element: (event.target as any)?.tagName
        }, 'medium');
      }
    }, true);
  }

  // Data flushing
  async flush(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    if (this.config.debug) {
      console.log('📊 Flushing analytics events:', events.length);
    }

    try {
      await this.sendEvents(events);
    } catch (error) {
      // Re-queue events if sending fails
      this.eventQueue.unshift(...events);
      console.error('Failed to send analytics events:', error);
    }
  }

  private async sendEvents(events: AnalyticsEvent[]): Promise<void> {
    if (this.config.apiEndpoint) {
      // Send to external analytics service
      await fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ events })
      });
    }

    // Send to internal logging endpoint
    try {
      await fetch('/api/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: 'info',
          context: 'analytics',
          message: 'Analytics events batch',
          data: { events, count: events.length }
        })
      });
    } catch (error) {
      // Silently fail for internal logging
      if (this.config.debug) {
        console.warn('Failed to send to internal logging:', error);
      }
    }
  }

  // Cleanup
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }

    // Flush remaining events
    this.flush();
  }
}

// Global analytics instance
let analyticsInstance: AnalyticsManager | null = null;

export const initializeAnalytics = (config?: Partial<AnalyticsConfig>): AnalyticsManager => {
  if (analyticsInstance) {
    analyticsInstance.destroy();
  }

  analyticsInstance = new AnalyticsManager(config);
  return analyticsInstance;
};

export const getAnalytics = (): AnalyticsManager | null => {
  return analyticsInstance;
};

// Convenience functions
export const analytics = {
  track: (type: EventType, properties?: Record<string, any>) => {
    analyticsInstance?.track(type, properties);
  },

  trackPageView: (page?: string) => {
    analyticsInstance?.trackPageView(page);
  },

  trackFileUpload: (fileName: string, fileSize: number, fileType: string) => {
    analyticsInstance?.trackFileUpload(fileName, fileSize, fileType);
  },

  trackFileAnalysis: (fileName: string, duration: number, issuesFound: number) => {
    analyticsInstance?.trackFileAnalysis(fileName, duration, issuesFound);
  },

  trackFileRepair: (fileName: string, duration: number, issuesRepaired: number, success: boolean) => {
    analyticsInstance?.trackFileRepair(fileName, duration, issuesRepaired, success);
  },

  trackError: (error: Error | string, context?: Record<string, any>, severity?: ErrorEvent['severity']) => {
    analyticsInstance?.trackError(error, context, severity);
  },

  trackPerformance: (metric: PerformanceMetric) => {
    analyticsInstance?.trackPerformance(metric);
  },

  trackUserInteraction: (action: string, element: string, context?: Record<string, any>) => {
    analyticsInstance?.trackUserInteraction(action, element, context);
  },

  trackFeatureUsage: (feature: string, context?: Record<string, any>) => {
    analyticsInstance?.trackFeatureUsage(feature, context);
  },

  setUserId: (userId: string) => {
    analyticsInstance?.setUserId(userId);
  }
};

export default analytics;