import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Sparkles,
  Download,
  RefreshCw
} from 'lucide-react';

interface SuccessFailureAnimationsProps {
  status: 'success' | 'error' | 'warning' | 'processing' | null;
  title: string;
  message: string;
  onAction?: (() => void) | undefined;
  actionLabel?: string;
  onSecondaryAction?: (() => void) | undefined;
  secondaryActionLabel?: string;
  autoHide?: boolean;
  duration?: number;
  className?: string | undefined;
}

export const SuccessFailureAnimations: React.FC<SuccessFailureAnimationsProps> = ({
  status,
  title,
  message,
  onAction,
  actionLabel,
  onSecondaryAction,
  secondaryActionLabel,
  autoHide = false,
  duration = 5000,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (status) {
      setIsVisible(true);

      if (status === 'success') {
        setShowConfetti(true);
        setTimeout(() => setShowConfetti(false), 3000);
      }

      if (autoHide) {
        const timer = setTimeout(() => {
          setIsVisible(false);
        }, duration);
        return () => clearTimeout(timer);
      }
    } else {
      setIsVisible(false);
    }
  }, [status, autoHide, duration]);

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          accentColor: 'bg-green-500',
          particles: '🎉',
          animation: {
            scale: [0.8, 1.1, 1],
            rotate: [0, 10, -10, 0],
          }
        };
      case 'error':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          accentColor: 'bg-red-500',
          particles: '💥',
          animation: {
            x: [-5, 5, -5, 5, 0],
            scale: [1, 1.05, 1],
          }
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          accentColor: 'bg-orange-500',
          particles: '⚠️',
          animation: {
            y: [-2, 2, -2, 2, 0],
            scale: [1, 1.02, 1],
          }
        };
      default:
        return {
          icon: Zap,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          accentColor: 'bg-blue-500',
          particles: '⚡',
          animation: {
            scale: [1, 1.05, 1],
          }
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  if (!status) return null;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -50, scale: 0.9 }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 25,
            duration: 0.6
          }}
          className={`relative overflow-hidden rounded-2xl border-2 ${config.borderColor} ${config.bgColor} p-6 shadow-lg ${className}`}
        >
          {/* Confetti Effect for Success */}
          {showConfetti && status === 'success' && (
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute text-2xl"
                  initial={{
                    x: '50%',
                    y: '50%',
                    opacity: 0,
                    scale: 0,
                  }}
                  animate={{
                    x: `${Math.random() * 100}%`,
                    y: `${Math.random() * 100}%`,
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0],
                    rotate: [0, 360],
                  }}
                  transition={{
                    duration: 2,
                    delay: i * 0.1,
                    ease: 'easeOut',
                  }}
                >
                  {['🎉', '✨', '🎊', '⭐', '💫'][Math.floor(Math.random() * 5)]}
                </motion.div>
              ))}
            </div>
          )}

          {/* Animated Background Gradient */}
          <motion.div
            className={`absolute inset-0 opacity-10 ${config.accentColor}`}
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.1, 0.2, 0.1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />

          {/* Main Content */}
          <div className="relative z-10">
            {/* Icon with Animation */}
            <div className="flex items-center justify-center mb-4">
              <motion.div
                className={`w-16 h-16 rounded-full ${config.bgColor} border-2 ${config.borderColor} flex items-center justify-center`}
                animate={config.animation}
                transition={{
                  duration: 0.8,
                  ease: 'easeInOut',
                }}
              >
                <IconComponent className={`h-8 w-8 ${config.color}`} />
              </motion.div>
            </div>

            {/* Title with Typewriter Effect */}
            <motion.h3
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className={`text-xl font-bold text-center mb-2 ${config.color}`}
            >
              {title}
            </motion.h3>

            {/* Message with Slide-in Effect */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="text-gray-700 text-center mb-6"
            >
              {message}
            </motion.p>

            {/* Action Buttons */}
            {(onAction || onSecondaryAction) && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.5 }}
                className="flex flex-col sm:flex-row gap-3 justify-center"
              >
                {onAction && actionLabel && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={onAction}
                    className={`px-6 py-3 ${config.accentColor} text-white rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 shadow-md hover:shadow-lg`}
                  >
                    {status === 'success' && <Download className="h-4 w-4" />}
                    {status === 'error' && <RefreshCw className="h-4 w-4" />}
                    {actionLabel}
                  </motion.button>
                )}

                {onSecondaryAction && secondaryActionLabel && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={onSecondaryAction}
                    className="px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2"
                  >
                    {secondaryActionLabel}
                  </motion.button>
                )}
              </motion.div>
            )}

            {/* Progress Indicator for Auto-hide */}
            {autoHide && (
              <motion.div
                className="mt-4 w-full bg-gray-200 rounded-full h-1"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
              >
                <motion.div
                  className={`h-1 rounded-full ${config.accentColor}`}
                  initial={{ width: '100%' }}
                  animate={{ width: '0%' }}
                  transition={{ duration: duration / 1000, ease: 'linear' }}
                />
              </motion.div>
            )}
          </div>

          {/* Floating Sparkles for Success */}
          {status === 'success' && (
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    rotate: [0, 180, 360],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 3,
                    delay: i * 0.3,
                    repeat: Infinity,
                    repeatDelay: 2,
                  }}
                >
                  <Sparkles className="h-4 w-4 text-yellow-400" />
                </motion.div>
              ))}
            </div>
          )}

          {/* Pulse Effect for Errors */}
          {status === 'error' && (
            <motion.div
              className="absolute inset-0 border-2 border-red-400 rounded-2xl"
              animate={{
                scale: [1, 1.02, 1],
                opacity: [0.5, 0.8, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Preset configurations for common scenarios
export const SuccessAnimation: React.FC<{
  title?: string;
  message?: string;
  onDownload?: (() => void) | undefined;
  onContinue?: (() => void) | undefined;
  className?: string | undefined;
}> = ({
  title = "Success!",
  message = "Your file has been processed successfully.",
  onDownload,
  onContinue,
  className
}) => (
  <SuccessFailureAnimations
    status="success"
    title={title}
    message={message}
    onAction={onDownload || undefined}
    actionLabel="Download File"
    onSecondaryAction={onContinue || undefined}
    secondaryActionLabel="Process Another"
    className={className}
  />
);

export const ErrorAnimation: React.FC<{
  title?: string;
  message?: string;
  onRetry?: () => void;
  onSupport?: () => void;
  className?: string | undefined;
}> = ({
  title = "Something went wrong",
  message = "We encountered an error processing your file.",
  onRetry,
  onSupport,
  className
}) => (
  <SuccessFailureAnimations
    status="error"
    title={title}
    message={message}
    onAction={onRetry || undefined}
    actionLabel="Try Again"
    onSecondaryAction={onSupport || undefined}
    secondaryActionLabel="Contact Support"
    className={className}
  />
);

export const WarningAnimation: React.FC<{
  title?: string;
  message?: string;
  onProceed?: () => void;
  onCancel?: () => void;
  className?: string | undefined;
}> = ({
  title = "Warning",
  message = "Please review the following before proceeding.",
  onProceed,
  onCancel,
  className
}) => (
  <SuccessFailureAnimations
    status="warning"
    title={title}
    message={message}
    onAction={onProceed || undefined}
    actionLabel="Proceed"
    onSecondaryAction={onCancel || undefined}
    secondaryActionLabel="Cancel"
    className={className}
  />
);

export default SuccessFailureAnimations;