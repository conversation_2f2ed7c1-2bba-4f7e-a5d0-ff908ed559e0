
import React from 'react';

interface RepairPageContentProps {
  processedFile: boolean;
  needsRepair: boolean;
  isFileFixed?: boolean;
  isHealthyFile?: boolean;
  hasPartialRepair?: boolean;
}

const RepairPageContent: React.FC<RepairPageContentProps> = ({ processedFile, needsRepair, isFileFixed = false, isHealthyFile = false }) => (
  <div className="space-y-10 text-left w-full">
    <div className="space-y-8">
      <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight text-left">
        {!processedFile ? (
          <>
            Upload & 
            <br />
            <span className="bg-lime-300 px-1 py-0.5 rounded-lg">Analyze</span>
            <br />
            Your Excel File
          </>
        ) : isFileFixed ? (
          <>
            Your File is 
            <br />
            <span className="bg-lime-300 px-1 py-0.5 rounded-lg">Fixed!</span>
            <br />
            Ready to Download
          </>
        ) : needsRepair ? (
          <>
            We Found 
            <br />
            <span className="bg-lime-300 px-1 py-0.5 rounded-lg">Problems!</span>
            <br />
            Let's Fix Them
          </>
        ) : (
          <>
            File is 
            <br />
            <span className="bg-lime-300 px-1 py-0.5 rounded-lg">Healthy!</span>
            <br />
            Ready to Download
          </>
        )}
      </h1>
      
      <p className="text-xl text-gray-600 text-left">
        {!processedFile 
          ? "Drop your .xlsx file here for an instant check. We'll scan for any problems and tell you exactly what we can fix." 
          : isFileFixed
          ? "Excellent! We've successfully repaired your file. All problems have been fixed and it's ready to download."
          : needsRepair
          ? "Don't worry! We found some problems in your file, but we can fix them. Click the 'Fix My File' button to get started."
          : "Great news! Your file is perfectly healthy. No problems found - you're all set!"
        }
      </p>
    </div>

    {/* Action Buttons - Only show when no file is processed */}
    {!processedFile && (
      <div className="flex flex-col sm:flex-row gap-4 text-left">
        <div className="text-sm text-gray-500">
          Drag and drop your file on the right →
        </div>
      </div>
    )}

    {/* Stats */}
    {!processedFile && (
      <div className="flex flex-wrap justify-start gap-x-10 gap-y-4 pt-6 text-left">
        <div className="text-left">
          <div className="text-3xl sm:text-4xl font-bold text-gray-900">99.9%</div>
          <div className="text-base text-gray-600">Success Rate</div>
        </div>
        <div className="text-left">
          <div className="text-3xl sm:text-4xl font-bold text-gray-900">5s</div>
          <div className="text-base text-gray-600">Analysis Time</div>
        </div>
        <div className="text-left">
          <div className="text-3xl sm:text-4xl font-bold text-gray-900">100%</div>
          <div className="text-base text-gray-600">Secure</div>
        </div>
      </div>
    )}
  </div>
);

export default RepairPageContent;
