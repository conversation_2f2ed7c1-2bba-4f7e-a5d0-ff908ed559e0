/**

 * API Client with timeout support and error handling

 * Provides a centralized way to make API calls with consistent timeout behavior

 */



import { log } from './logger';

export interface ApiRequestOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
}

export class ApiTimeoutError extends Error {
  constructor(timeout: number) {
    super(`Request timed out after ${timeout}ms`);
    this.name = 'ApiTimeoutError';
  }
}

export class ApiRetryError extends Error {
  constructor(attempts: number, lastError: string) {
    super(`Request failed after ${attempts} attempts. Last error: ${lastError}`);
    this.name = 'ApiRetryError';
  }
}

export class ApiClient {
  private static readonly DEFAULT_TIMEOUT = 60000; // 60 seconds
  private static readonly DEFAULT_RETRIES = 2;
  private static readonly DEFAULT_RETRY_DELAY = 1000; // 1 second

  /**
   * Make an API request with timeout and retry support
   */
  static async request<T = any>(
    url: string,
    options: RequestInit & ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      timeout = this.DEFAULT_TIMEOUT,
      retries = this.DEFAULT_RETRIES,
      retryDelay = this.DEFAULT_RETRY_DELAY,
      ...fetchOptions
    } = options;

    const startTime = Date.now();
    const method = fetchOptions.method || 'GET';
    let lastError: Error | null = null;

    log.api.request(method, url);

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await this.makeRequestWithTimeout(url, fetchOptions, timeout);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const duration = Date.now() - startTime;
        log.api.request(method, url, duration);
        log.performance('api-request-duration', duration);

        return {
          success: true,
          data,
          status: response.status
        };

      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain errors
        if (error instanceof ApiTimeoutError && attempt < retries) {
          console.warn(`Request timeout on attempt ${attempt + 1}/${retries + 1}, retrying in ${retryDelay}ms...`);
          await this.delay(retryDelay);
          continue;
        }

        // Don't retry on client errors (4xx)
        if (lastError instanceof Error && lastError.message.includes('HTTP 4')) {
          break;
        }

        // Retry on network errors and server errors (5xx)
        if (attempt < retries) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.warn(`Request failed on attempt ${attempt + 1}/${retries + 1}, retrying in ${retryDelay}ms...`, errorMessage);
          await this.delay(retryDelay);
          continue;
        }
      }
    }

    // All attempts failed
    const errorMessage = lastError?.message || 'Unknown error occurred';
    const duration = Date.now() - startTime;

    log.api.error(method, url, lastError || new Error(errorMessage));
    log.performance('api-request-failed-duration', duration);

    if (lastError instanceof ApiTimeoutError) {
      return {
        success: false,
        error: `Request timed out after ${timeout}ms. Please check your connection and try again.`
      };
    }

    return {
      success: false,
      error: retries > 0 ?
        `Request failed after ${retries + 1} attempts. ${errorMessage}` :
        errorMessage
    };
  }

  /**
   * Make a request with timeout using AbortController
   */
  private static async makeRequestWithTimeout(
    url: string,
    options: RequestInit,
    timeout: number
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response;

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiTimeoutError(timeout);
      }

      throw error;
    }
  }

  /**
   * Delay utility for retries
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Specialized method for file upload/repair with longer timeout
   */
  static async uploadFile<T = any>(
    url: string,
    fileData: string,
    fileName: string,
    fileSize: number,
    operation: 'analyze' | 'repair' = 'repair',
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    // Calculate timeout based on file size (minimum 30s, add 10s per MB)
    const fileSizeMB = fileSize / (1024 * 1024);
    const calculatedTimeout = Math.max(30000, 30000 + (fileSizeMB * 10000)); // 30s + 10s per MB

    const uploadTimeout = options.timeout || calculatedTimeout;

    console.log(`📤 ${operation === 'repair' ? 'Repairing' : 'Processing'} file ${fileName} (${fileSizeMB.toFixed(2)}MB) with ${uploadTimeout/1000}s timeout`);

    return this.request<T>(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileData,
        fileName,
        fileSize,
        operation,
      }),
      timeout: uploadTimeout,
      retries: 1, // Reduce retries for file uploads
      ...options
    });
  }

  /**
   * Specialized method for file analysis with medium timeout
   */
  static async analyzeFile<T = any>(
    url: string,
    fileData: string,
    fileName: string,
    fileSize: number,
    operation: 'analyze' | 'repair' = 'analyze',
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    // Calculate timeout based on file size (minimum 20s, add 5s per MB)
    const fileSizeMB = fileSize / (1024 * 1024);
    const calculatedTimeout = Math.max(20000, 20000 + (fileSizeMB * 5000)); // 20s + 5s per MB

    const analysisTimeout = options.timeout || calculatedTimeout;

    console.log(`🔍 ${operation === 'analyze' ? 'Analyzing' : 'Processing'} file ${fileName} (${fileSizeMB.toFixed(2)}MB) with ${analysisTimeout/1000}s timeout`);

    return this.request<T>(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileData,
        fileName,
        fileSize,
        operation,
      }),
      timeout: analysisTimeout,
      retries: 2, // Allow more retries for analysis
      ...options
    });
  }

  /**
   * Specialized method for form submissions with short timeout
   */
  static async submitForm<T = any>(
    url: string,
    formData: Record<string, any>,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
      timeout: options.timeout || 10000, // 10 second timeout for forms
      retries: options.retries || 1,
      ...options
    });
  }

  /**
   * Get CSRF token with short timeout
   */
  static async getCsrfToken(): Promise<ApiResponse<{ token: string }>> {
    return this.request<{ token: string }>('/api/csrf-token', {
      method: 'GET',
      timeout: 5000, // 5 second timeout for CSRF token
      retries: 1
    });
  }
}

// Export convenience functions
export const apiRequest = ApiClient.request.bind(ApiClient);
export const uploadFile = ApiClient.uploadFile.bind(ApiClient);
export const analyzeFile = ApiClient.analyzeFile.bind(ApiClient);
export const submitForm = ApiClient.submitForm.bind(ApiClient);
export const getCsrfToken = ApiClient.getCsrfToken.bind(ApiClient);
