import fs from 'fs';

// Test the API flow end-to-end
async function testAPIFlow() {
  console.log('🧪 Testing API flow with corrupted file...');
  
  try {
    // Read the test file
    const fileName = 'test-corrupted-excel.xlsx';
    if (!fs.existsSync(fileName)) {
      console.error('❌ Test file not found:', fileName);
      return;
    }
    
    const fileData = fs.readFileSync(fileName);
    const base64Data = fileData.toString('base64');
    
    console.log(`📁 File loaded: ${fileName} (${fileData.length} bytes)`);
    
    // Test analysis-only mode (what happens when you first upload)
    console.log('\n🔍 Testing analysis-only mode...');
    
    const analysisResponse = await fetch('http://localhost:3000/api/repair-excel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileData: base64Data,
        fileName: fileName,
        fileSize: fileData.length,
        analysisOnly: true
      }),
    });
    
    if (!analysisResponse.ok) {
      console.error('❌ Analysis request failed:', analysisResponse.status, analysisResponse.statusText);
      const errorText = await analysisResponse.text();
      console.error('Error response:', errorText);
      return;
    }
    
    const analysisResult = await analysisResponse.json();
    console.log('✅ Analysis completed successfully!');
    console.log('📊 Analysis result:', {
      success: analysisResult.success,
      summary: analysisResult.repairSummary?.summary,
      issueCount: analysisResult.repairSummary?.sections?.[0]?.issues?.length || 0
    });
    
    // Test full repair mode
    console.log('\n🔧 Testing full repair mode...');
    
    const repairResponse = await fetch('http://localhost:3000/api/repair-excel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileData: base64Data,
        fileName: fileName,
        fileSize: fileData.length,
        analysisOnly: false
      }),
    });
    
    if (!repairResponse.ok) {
      console.error('❌ Repair request failed:', repairResponse.status, repairResponse.statusText);
      const errorText = await repairResponse.text();
      console.error('Error response:', errorText);
      return;
    }
    
    const repairResult = await repairResponse.json();
    console.log('✅ Repair completed successfully!');
    console.log('📊 Repair result:', {
      success: repairResult.success,
      summary: repairResult.repairSummary?.summary,
      hasRepairedFile: !!repairResult.repairedFileData
    });
    
    if (repairResult.repairedFileData) {
      // Save the repaired file
      const repairedFileName = fileName.replace('.xlsx', '_repaired_test.xlsx');
      const repairedBuffer = Buffer.from(repairResult.repairedFileData, 'base64');
      fs.writeFileSync(repairedFileName, repairedBuffer);
      console.log(`💾 Repaired file saved as: ${repairedFileName}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test if API server is available
const testServer = async () => {
  try {
    const response = await fetch('http://localhost:3000/api/repair-excel', {
      method: 'OPTIONS'
    });
    return response.ok || response.status === 405; // OPTIONS might not be implemented
  } catch (error) {
    return false;
  }
};

console.log('🚀 Checking if API server is running...');
const serverRunning = await testServer();

if (serverRunning) {
  console.log('✅ API server is running, starting test...');
  await testAPIFlow();
} else {
  console.log('⚠️ API server is not running. Please start your development server:');
  console.log('   npm run dev');
  console.log('   or');
  console.log('   yarn dev');
}
