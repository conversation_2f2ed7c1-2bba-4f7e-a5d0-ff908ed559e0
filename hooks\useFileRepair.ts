import { useState, useCallback, useRef, useEffect } from 'react';
import ExcelJS from 'exceljs';
import { MemoryManager } from '../lib/memoryManager';
import { uploadFile, ApiTimeoutError } from '../lib/apiClient';
import { useProgressiveLoader, LoadingSequences } from '../lib/loadingStates';
import { EnhancedErrorHandler, createProcessingError } from '../lib/errorMessages';
import { ProcessedFileResult } from '../components/FileUpload';
import { log } from '../lib/logger';

interface UseFileRepairOptions {
  onRepairComplete?: (result: ProcessedFileResult) => void;
  onError?: (error: any) => void;
}

interface FileRepairState {
  isRepairing: boolean;
  repairProgress: number;
  repairStatus: string;
  memoryUsage: { percentage: number; available: string } | null;
  currentError: any;
}

export const useFileRepair = (options: UseFileRepairOptions = {}) => {
  const { onRepairComplete, onError } = options;

  const [state, setState] = useState<FileRepairState>({
    isRepairing: false,
    repairProgress: 0,
    repairStatus: '',
    memoryUsage: null,
    currentError: null
  });

  const repairSessionRef = useRef<{ processId: string; cleanup: () => void; monitor: (callback: (stats: any) => void) => NodeJS.Timeout } | null>(null);
  const progressiveLoader = useProgressiveLoader();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (repairSessionRef.current) {
        repairSessionRef.current.cleanup();
        repairSessionRef.current = null;
      }
    };
  }, []);

  const updateState = useCallback((updates: Partial<FileRepairState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const analyzeWorkbook = useCallback((workbook: ExcelJS.Workbook): ProcessedFileResult['sheets'] => {
    // Get memory-aware analysis limits
    const analysisLimits = MemoryManager.getCellAnalysisLimits();

    return workbook.worksheets.slice(0, analysisLimits.maxSheets).map(worksheet => {
      const sheetName = worksheet.name;
      try {
        // Handle missing or corrupted worksheets
        if (!worksheet) {
          return {
            name: sheetName,
            cellCount: 0,
            hasFormulas: false,
            errors: []
          };
        }

        // Safe range calculation for ExcelJS
        let cellCount = 0;
        try {
          // ExcelJS uses different structure - count actual cells
          let maxRow = 0;
          let maxCol = 0;

          worksheet.eachRow((row, rowNumber) => {
            maxRow = Math.max(maxRow, rowNumber);
            row.eachCell((cell, colNumber) => {
              maxCol = Math.max(maxCol, colNumber);
            });
          });

          cellCount = maxRow * maxCol;
        } catch (rangeError) {
          console.warn(`Range calculation failed for sheet ${sheetName}:`, rangeError);
          // Fallback: count by iterating
          cellCount = 0;
          try {
            worksheet.eachRow((row) => {
              row.eachCell(() => {
                cellCount++;
              });
            });
          } catch {
            cellCount = 0;
          }
        }

        let hasFormulas = false;
        const errors: string[] = [];

        // Check for formulas and errors using ExcelJS structure
        let cellsChecked = 0;
        const maxCellsToCheck = Math.min(analysisLimits.maxCells, 5000);

        worksheet.eachRow((row, rowNumber) => {
          if (cellsChecked >= maxCellsToCheck) return;

          row.eachCell((cell, colNumber) => {
            if (cellsChecked >= maxCellsToCheck) return;
            cellsChecked++;

            try {
              // Check for formulas
              if (cell.type === ExcelJS.ValueType.Formula) {
                hasFormulas = true;

                // Check for formula errors
                if (typeof cell.value === 'object' && cell.value && 'error' in cell.value) {
                  const cellAddress = `${String.fromCharCode(64 + colNumber)}${rowNumber}`;
                  errors.push(`Sheet ${sheetName}, Cell ${cellAddress}: Formula error - ${cell.formula}`);
                  console.log(`Detected formula error in ${sheetName}!${cellAddress}: ${cell.formula}`);
                }
              }

              // Check for error values
              if (cell.type === ExcelJS.ValueType.Error) {
                const cellAddress = `${String.fromCharCode(64 + colNumber)}${rowNumber}`;
                const errorValue = cell.value || '#ERROR!';
                errors.push(`Sheet ${sheetName}, Cell ${cellAddress}: ${errorValue}`);
                console.log(`Detected Excel error in ${sheetName}!${cellAddress}: ${errorValue}`);
              }

            } catch (_error) {
              console.warn(`Error accessing cell at row ${rowNumber}, col ${colNumber} in sheet ${sheetName}:`, _error);
            }
          });
        });

        console.log(`Sheet ${sheetName} analysis: hasFormulas=${hasFormulas}, errors=${errors.length}`);
        return {
          name: sheetName,
          cellCount,
          hasFormulas,
          errors
        };
      } catch (sheetError) {
        console.warn(`Analysis failed for sheet ${sheetName}:`, sheetError);
        return {
          name: sheetName,
          cellCount: 0,
          hasFormulas: false,
          errors: []
        };
      }
    });
  }, []);

  const repairFile = useCallback(async (file: File): Promise<ProcessedFileResult> => {
    log.fileRepair.start(file.name, 'repair');
    const startTime = Date.now();

    updateState({
      isRepairing: true,
      repairProgress: 0,
      currentError: null
    });

    // Start progressive loading for repair
    progressiveLoader.start(LoadingSequences.fileRepair);

    progressiveLoader.updateStepProgress(50, 'Setting up repair environment...');
    updateState({ repairStatus: '🔧 Starting the repair process...' });
    await new Promise(resolve => setTimeout(resolve, 800));
    progressiveLoader.updateStepProgress(100);
    progressiveLoader.nextStep();

    // Create processing session for repair
    let repairSession;
    try {
      repairSession = MemoryManager.createProcessingSession(file.size);
      repairSessionRef.current = repairSession;

      // Start memory monitoring for repair
      const memoryMonitor = repairSession.monitor((stats) => {
        updateState({
          memoryUsage: {
            percentage: Math.round(stats.percentage),
            available: MemoryManager.formatBytes(stats.available)
          }
        });
      });

      // Step 2: Backup
      progressiveLoader.updateStepProgress(60, 'Safely backing up your original file...');
      updateState({ repairStatus: '📤 Sending your file for repair...', repairProgress: 20 });
      await new Promise(resolve => setTimeout(resolve, 400));
      progressiveLoader.updateStepProgress(100);
      progressiveLoader.nextStep();

      const arrayBuffer = await file.arrayBuffer();
      const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

      // Step 3: Repair
      progressiveLoader.updateStepProgress(30, 'Fixing detected issues...');
      updateState({ repairStatus: '⚙️ Working hard to fix your file...', repairProgress: 40 });

      progressiveLoader.updateStepProgress(70, 'Applying advanced repair techniques...');

      // Use API client with timeout support for file repair
      const apiResponse = await uploadFile('/api/excel-processor', base64Data, file.name, file.size, 'repair');

      progressiveLoader.updateStepProgress(100);
      progressiveLoader.nextStep();

      // Step 4: Validate
      progressiveLoader.updateStepProgress(50, 'Ensuring repair was successful...');
      updateState({ repairProgress: 70, repairStatus: '📥 Getting your fixed file ready...' });

      if (!apiResponse.success) {
        throw new Error(apiResponse.error || 'Something went wrong while fixing your file.');
      }

      progressiveLoader.updateStepProgress(100);
      progressiveLoader.nextStep();

      // Step 5: Finalize
      progressiveLoader.updateStepProgress(70, 'Preparing your repaired file...');
      updateState({ repairProgress: 90, repairStatus: '🎉 Putting the finishing touches...' });

      const repairReport = apiResponse.data?.repairSummary;
      const repairedWorkbookData = apiResponse.data?.repairedFileData;

      let finalWorkbook: ExcelJS.Workbook | null = null;
      if (repairedWorkbookData) {
        const binaryString = atob(repairedWorkbookData);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        finalWorkbook = new ExcelJS.Workbook();
        await finalWorkbook.xlsx.load(bytes.buffer);
      }

      const sheets = finalWorkbook ? analyzeWorkbook(finalWorkbook) : [];
      const totalCells = sheets.reduce((sum, sheet) => sum + sheet.cellCount, 0);
      const sheetsWithData = sheets.filter(sheet => sheet.cellCount > 0).length;

      let status: ProcessedFileResult['status'];
      let message: string;

      if (apiResponse.success) {
        status = 'success';
        message = `✅ File successfully repaired! Recovered ${totalCells.toLocaleString()} cells across ${sheetsWithData} sheet${sheetsWithData !== 1 ? 's' : ''}`;
      } else {
        status = 'error';
        message = repairReport?.summary || apiResponse.error || 'File repair failed.';
      }

      progressiveLoader.updateStepProgress(100, 'Repair complete!');
      updateState({ repairProgress: 100, repairStatus: '🎉 Your file is fixed and ready!' });

      // Complete progressive loading
      progressiveLoader.complete();
      await new Promise(resolve => setTimeout(resolve, 200));

      const result: ProcessedFileResult = {
        originalFile: file,
        workbook: finalWorkbook,
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: new Date(file.lastModified)
        },
        sheets,
        status,
        message,
        repairReport: repairReport,
      };

      const duration = Date.now() - startTime;
      log.fileRepair.complete(file.name, 'repair', result);
      log.performance('file-repair-duration', duration);
      console.log('✅ File repair complete:', result);
      onRepairComplete?.(result);
      return result;

    } catch (error: unknown) {
      log.fileRepair.error(file.name, 'repair', error instanceof Error ? error : new Error(String(error)));
      console.error('❌ File repair failed:', error);

      // Create enhanced error for repair failure
      const enhancedError = createProcessingError(
        error instanceof Error ? error.message : 'Failed to repair file',
        file.name,
        progressiveLoader.currentStep || 'repair'
      );

      // Log error for debugging
      EnhancedErrorHandler.logError(error instanceof Error ? error : new Error(String(error)), {
        fileName: file.name,
        fileSize: file.size,
        operation: 'repair',
        step: progressiveLoader.currentStep || 'repair'
      });

      updateState({ currentError: enhancedError, repairStatus: 'Repair failed', repairProgress: 100 });
      onError?.(enhancedError);

      const errorResult: ProcessedFileResult = {
        originalFile: file,
        workbook: null,
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: new Date(file.lastModified)
        },
        sheets: [],
        status: 'error',
        message: (error as Error).message || 'Failed to repair file'
      };

      return errorResult;
    } finally {
      // Clean up repair session
      if (repairSession) {
        repairSession.cleanup();
        repairSessionRef.current = null;
      }

      // Ensure progressive loading is completed or cancelled
      if (progressiveLoader.isLoading) {
        progressiveLoader.cancel();
      }

      updateState({ isRepairing: false, memoryUsage: null });
    }
  }, [analyzeWorkbook, progressiveLoader, updateState, onRepairComplete, onError]);

  const reset = useCallback(() => {
    console.log('🧹 Resetting file repair state');

    // Clean up any active repair session
    if (repairSessionRef.current) {
      repairSessionRef.current.cleanup();
      repairSessionRef.current = null;
    }

    setState({
      isRepairing: false,
      repairProgress: 0,
      repairStatus: '',
      memoryUsage: null,
      currentError: null
    });

    // Cancel progressive loading
    progressiveLoader.cancel();
  }, [progressiveLoader]);

  return {
    // State
    ...state,

    // Actions
    repairFile,
    reset,

    // Utilities
    analyzeWorkbook
  };
};