/**
 * Simple validation tests for all schemas
 * Run this file with: node lib/validation.test.js
 */

import { z } from 'zod';

// Define the schemas directly here for testing
const FileUploadSchema = z.object({
  fileData: z.string()
    .min(1, 'File data is required')
    .max(50 * 1024 * 1024, 'File data too large (50MB limit)'),
  fileName: z.string()
    .min(1, 'File name is required')
    .max(255, 'File name too long')
    .regex(/^[^<>:"/\\|?*\x00-\x1f]+$/, 'Invalid file name characters')
    .refine(name => name.toLowerCase().endsWith('.xlsx'), 'Only .xlsx files are supported'),
  fileSize: z.number()
    .positive('File size must be positive')
    .max(50 * 1024 * 1024, 'File size exceeds 50MB limit'),
  operation: z.enum(['analyze', 'repair']).optional().default('analyze'),
  analysisOnly: z.boolean().optional()
});

const EmailFormSchema = z.object({
  email: z.string()
    .email('Invalid email address')
    .max(254, 'Email too long')
    .toLowerCase()
    .trim(),
  name: z.string()
    .min(1, 'Name is required')
    .max(100, 'Name too long')
    .trim()
    .regex(/^[a-zA-Z\u00C0-\u024F\u1E00-\u1EFF\s\-'\.]+$/, 'Name contains invalid characters'),
  pricingModel: z.string()
    .max(50, 'Pricing model too long')
    .optional(),
  frequency: z.string()
    .max(50, 'Frequency too long')
    .optional(),
  urgency: z.string()
    .max(50, 'Urgency too long')
    .optional(),
  payment: z.string()
    .max(50, 'Payment preference too long')
    .optional(),
  painPoints: z.string()
    .max(1000, 'Pain points description too long')
    .optional()
});

// Test runner
class ValidationTester {
  constructor() {
    this.passedTests = 0;
    this.failedTests = 0;
    this.testResults = [];
  }

  test(name, testFn) {
    try {
      const result = testFn();
      const passed = result !== false;

      if (passed) {
        this.passedTests++;
        console.log(`✅ ${name}`);
      } else {
        this.failedTests++;
        console.log(`❌ ${name}`);
      }

      this.testResults.push({ name, passed });
    } catch (error) {
      this.failedTests++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log(`❌ ${name} - Error: ${errorMessage}`);
      this.testResults.push({ name, passed: false, error: errorMessage });
    }
  }

  expect(actual) {
    return {
      toBe: (expected) => actual === expected,
      toContain: (expected) => {
        if (typeof actual === 'string') {
          return actual.includes(expected);
        }
        if (Array.isArray(actual)) {
          return actual.includes(expected);
        }
        return false;
      },
      toBeTruthy: () => !!actual,
      toBeFalsy: () => !actual,
      toBeGreaterThan: (expected) => actual > expected,
      toBeLessThan: (expected) => actual < expected
    };
  }

  summary() {
    console.log('\n' + '='.repeat(50));
    console.log(`Test Summary: ${this.passedTests} passed, ${this.failedTests} failed`);
    console.log('='.repeat(50));

    if (this.failedTests > 0) {
      console.log('\nFailed tests:');
      this.testResults
        .filter(result => !result.passed)
        .forEach(result => {
          console.log(`- ${result.name}${result.error ? ` (${result.error})` : ''}`);
        });
    }
  }
}

const tester = new ValidationTester();

// File Upload Schema Tests
tester.test('FileUploadSchema - valid data', () => {
  const validData = {
    fileData: 'UEsDBBQAAAAIAA==',
    fileName: 'test.xlsx',
    fileSize: 1024,
    operation: 'analyze'
  };

  const result = FileUploadSchema.safeParse(validData);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('FileUploadSchema - invalid file extension', () => {
  const invalidData = {
    fileData: 'UEsDBBQAAAAIAA==',
    fileName: 'test.pdf',
    fileSize: 1024
  };

  const result = FileUploadSchema.safeParse(invalidData);
  return tester.expect(result.success).toBeFalsy();
});

tester.test('FileUploadSchema - file too large', () => {
  const invalidData = {
    fileData: 'UEsDBBQAAAAIAA==',
    fileName: 'test.xlsx',
    fileSize: 60 * 1024 * 1024 // 60MB
  };

  const result = FileUploadSchema.safeParse(invalidData);
  return tester.expect(result.success).toBeFalsy();
});

// Email Form Schema Tests
tester.test('EmailFormSchema - valid data', () => {
  const validData = {
    email: '<EMAIL>',
    name: 'John Doe',
    pricingModel: 'premium',
    frequency: 'weekly',
    urgency: 'high',
    payment: 'credit-card',
    painPoints: 'Excel files keep getting corrupted'
  };

  const result = EmailFormSchema.safeParse(validData);
  return tester.expect(result.success).toBeTruthy();
});

tester.test('EmailFormSchema - invalid email', () => {
  const invalidData = {
    email: 'invalid-email',
    name: 'John Doe'
  };

  const result = EmailFormSchema.safeParse(invalidData);
  return tester.expect(result.success).toBeFalsy();
});

tester.test('EmailFormSchema - email normalization', () => {
  const data = {
    email: '<EMAIL>',
    name: 'John Doe'
  };
  
  const result = EmailFormSchema.safeParse(data);
  if (result.success) {
    console.log('Actual email:', JSON.stringify(result.data.email));
    console.log('Expected email:', JSON.stringify('<EMAIL>'));
    return tester.expect(result.data.email).toBe('<EMAIL>');
  } else {
    console.log('Email validation failed:', result.error);
  }
  return false;
});

tester.test('EmailFormSchema - malicious input rejected', () => {
  const maliciousData = {
    email: '<EMAIL>',
    name: '"><script>alert("xss")</script>',
    painPoints: 'DROP TABLE users; --'
  };

  const result = EmailFormSchema.safeParse(maliciousData);
  return tester.expect(result.success).toBeFalsy(); // Should fail due to invalid name characters
});

tester.test('EmailFormSchema - unicode characters accepted', () => {
  const unicodeData = {
    email: '<EMAIL>',
    name: 'José María O\'Connor-Smith'
  };

  const result = EmailFormSchema.safeParse(unicodeData);
  if (!result.success) {
    console.log('Unicode validation failed:', result.error);
  }
  return tester.expect(result.success).toBeTruthy();
});

// Edge Cases and Security Tests
tester.test('EmailFormSchema - extremely long strings rejected', () => {
  const longString = 'a'.repeat(10000);
  const result = EmailFormSchema.safeParse({
    email: '<EMAIL>',
    name: longString
  });

  return tester.expect(result.success).toBeFalsy();
});

tester.test('FileUploadSchema - null/undefined values rejected', () => {
  const result1 = FileUploadSchema.safeParse(null);
  const result2 = FileUploadSchema.safeParse(undefined);
  const result3 = FileUploadSchema.safeParse({});

  return tester.expect(result1.success).toBeFalsy() &&
         tester.expect(result2.success).toBeFalsy() &&
         tester.expect(result3.success).toBeFalsy();
});

// Performance Tests
tester.test('FileUploadSchema - large valid data performance', () => {
  const largeValidData = {
    fileData: 'U'.repeat(1000) + '==', // Large but valid base64
    fileName: 'large-file.xlsx',
    fileSize: 1024 * 1024 // 1MB
  };

  const start = performance.now();
  const result = FileUploadSchema.safeParse(largeValidData);
  const end = performance.now();

  return tester.expect(result.success).toBeTruthy() &&
         tester.expect(end - start).toBeLessThan(100); // Should complete in under 100ms
});

// Run all tests and show summary
console.log('🧪 Running Validation Schema Tests...\n');
tester.summary();