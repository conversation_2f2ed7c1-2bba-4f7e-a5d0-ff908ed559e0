export interface NavItem {
  label: string;
  href: string;
}

export type INavbarMenu = {
  id: number;
  title: string;
  url: string;
  dropdown?: boolean;
  items?: INavbarMenu[];
};

export interface StatItem {
  value: string;
  label: string;
}

export interface HowItWorksStep {
  iconClass: string;
  title: string;
  description: string;
}

export interface FeatureItem {
  iconClass: string;
  title: string;
  description: string;
}

export interface FAQ {
  question: string;
  answer: string;
}

export interface PricingPlan {
  name: string;
  price: string;
  priceSuffix: string;
  yearlyPrice?: string;
  yearlyPriceSuffix?: string;
  features: string[];
  buttonText: string;
  isPopular?: boolean;
  buttonClass?: string;
  borderColor?: string;
  textColor?: string;
}

export interface FooterLink {
  label: string;
  href: string;
}

export interface FooterLinkGroup {
  title: string;
  links: FooterLink[];
}

export interface RepairIssue {
  type: string; // e.g., 'Structural Corruption', 'Formula Error', 'Data Inconsistency'
  location?: string; // e.g., 'Sheet1!A1', 'Workbook Structure'
  description: string;
  actionTaken: string;
  severity: 'Critical' | 'Major' | 'Minor';
}

export interface RepairReportSection {
  title: string;
  issues: RepairIssue[];
}

export interface RepairReport {
  summary: string;
  sections: RepairReportSection[];
  performanceMetrics: {
    analysisTime: string;
    repairTime?: string;
  };
  downloadLink?: string;
}