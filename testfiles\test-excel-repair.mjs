// Test script for Excel repair functionality
import * as XLSX from 'xlsx';


console.log('🧪 Testing Excel Repair Functionality\n');

// Test 1: Create test Excel files
console.log('📝 Test 1: Creating test Excel files...');

// Create a normal workbook with some errors
const wb = XLSX.utils.book_new();

// Add some test data
const wsData = [
  ['Name', 'Age', 'Email', 'Score'],
  ['<PERSON>', 30, '<EMAIL>', 95],
  ['<PERSON>', 25, '<EMAIL>', 87],
  ['<PERSON>', 35, '<EMAIL>', '#REF!'], // Error cell
  ['<PERSON>', 28, '<EMAIL>', 92]
];

// Add formulas
wsData.push(['Total', '', '', '=SUM(D2:D5)']);
wsData.push(['Average', '', '', '=AVERAGE(D2:D5)']);
wsData.push(['Count', '', '', '=COUNT(D2:D5)']);

const ws = XLSX.utils.aoa_to_sheet(wsData);

// Add some formulas with errors
ws['D4'] = { t: 'e', v: '#REF!', w: '#REF!' }; // Error cell
ws['D6'] = { f: 'SUM(D2:D5)', t: 'n' }; // Formula
ws['D7'] = { f: 'AVERAGE(D2:D5)', t: 'n' }; // Formula
ws['D8'] = { f: 'COUNT(D2:D5)', t: 'n' }; // Formula

// Add a formula with circular reference
ws['E2'] = { f: 'E2+1', t: 'e', v: '#REF!', w: '#REF!' };

// Add a broken formula
ws['E3'] = { f: 'SUMM(D2:D5)', t: 'e', v: '#NAME?', w: '#NAME?' };

XLSX.utils.book_append_sheet(wb, ws, 'TestData');

// Create a second sheet with more complex data
const ws2Data = [
  ['Product', 'Q1', 'Q2', 'Q3', 'Q4', 'Total'],
  ['Product A', 100, 150, 200, 250, '=SUM(B2:E2)'],
  ['Product B', 80, '#DIV/0!', 160, 200, '=SUM(B3:E3)'],
  ['Product C', 120, 180, '#REF!', 300, '=SUM(B4:E4)'],
  ['Total', '=SUM(B2:B4)', '=SUM(C2:C4)', '=SUM(D2:D4)', '=SUM(E2:E4)', '=SUM(F2:F4)']
];

const ws2 = XLSX.utils.aoa_to_sheet(ws2Data);

// Add error cells
ws2['C3'] = { t: 'e', v: '#DIV/0!', w: '#DIV/0!' };
ws2['D4'] = { t: 'e', v: '#REF!', w: '#REF!' };

XLSX.utils.book_append_sheet(wb, ws2, 'Sales');

// Write test files
XLSX.writeFile(wb, 'test-normal.xlsx');
console.log('✅ Created test-normal.xlsx');

// Create a more corrupted version
const corruptedWb = XLSX.utils.book_new();
const corruptedWs = XLSX.utils.aoa_to_sheet([
  ['Test', 'Data'],
  ['#REF!', '#DIV/0!'],
  ['#NAME?', '#VALUE!'],
  ['#NULL!', '#NUM!']
]);

// Add all error types
corruptedWs['A2'] = { t: 'e', v: '#REF!', w: '#REF!' };
corruptedWs['B2'] = { t: 'e', v: '#DIV/0!', w: '#DIV/0!' };
corruptedWs['A3'] = { t: 'e', v: '#NAME?', w: '#NAME?' };
corruptedWs['B3'] = { t: 'e', v: '#VALUE!', w: '#VALUE!' };
corruptedWs['A4'] = { t: 'e', v: '#NULL!', w: '#NULL!' };
corruptedWs['B4'] = { t: 'e', v: '#NUM!', w: '#NUM!' };

XLSX.utils.book_append_sheet(corruptedWb, corruptedWs, 'Errors');
XLSX.writeFile(corruptedWb, 'test-corrupted.xlsx');
console.log('✅ Created test-corrupted.xlsx');

console.log('\n🎉 Excel test files created successfully!');
console.log('\nYou can now upload these files to test the repair functionality:');
console.log('  - test-normal.xlsx (has some errors and formulas)');
console.log('  - test-corrupted.xlsx (heavily corrupted with all error types)'); 