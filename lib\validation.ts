import { z } from 'zod';

// Type exports for better TypeScript integration
export type FileUploadData = z.infer<typeof FileUploadSchema>;
export type EmailFormData = z.infer<typeof EmailFormSchema>;
export type RepairIssue = z.infer<typeof RepairIssueSchema>;
export type RepairReportSection = z.infer<typeof RepairReportSectionSchema>;
export type RepairReport = z.infer<typeof RepairReportSchema>;
export type FileProcessingSuccess = z.infer<typeof FileProcessingSuccessSchema>;
export type FileProcessingError = z.infer<typeof FileProcessingErrorSchema>;
export type ApiSuccess = z.infer<typeof ApiSuccessSchema>;
export type ApiError = z.infer<typeof ApiErrorSchema>;
export type FileProcessingState = z.infer<typeof FileProcessingStateSchema>;
export type FileUploadState = z.infer<typeof FileUploadStateSchema>;
export type ValidationError = z.infer<typeof ValidationErrorSchema>;
export type ApiErrorDetail = z.infer<typeof ApiErrorDetailSchema>;
export type AppConfig = z.infer<typeof AppConfigSchema>;
export type ProgressIndicatorProps = z.infer<typeof ProgressIndicatorPropsSchema>;
export type ErrorDisplayProps = z.infer<typeof ErrorDisplayPropsSchema>;

// File upload validation schema
export const FileUploadSchema = z.object({
  fileData: z.string()
    .min(1, 'File data is required')
    .max(50 * 1024 * 1024, 'File data too large (50MB limit)'), // Base64 string size limit
  fileName: z.string()
    .min(1, 'File name is required')
    .max(255, 'File name too long')
    .regex(/^[^<>:"/\\|?*\x00-\x1f]+$/, 'Invalid file name characters')
    .refine(name => name.toLowerCase().endsWith('.xlsx'), 'Only .xlsx files are supported'),
  fileSize: z.number()
    .positive('File size must be positive')
    .max(50 * 1024 * 1024, 'File size exceeds 50MB limit'),
  operation: z.enum(['analyze', 'repair']).optional().default('analyze'),
  // Keep analysisOnly for backward compatibility
  analysisOnly: z.boolean().optional()
});

// Email form validation schema
export const EmailFormSchema = z.object({
  email: z.string()
    .email('Invalid email address')
    .max(254, 'Email too long')
    .toLowerCase()
    .trim(),
  name: z.string()
    .min(1, 'Name is required')
    .max(100, 'Name too long')
    .trim()
    .regex(/^[a-zA-Z\u00C0-\u024F\u1E00-\u1EFF\s\-'\.]+$/, 'Name contains invalid characters'),
  pricingModel: z.string()
    .max(50, 'Pricing model too long')
    .optional(),
  frequency: z.string()
    .max(50, 'Frequency too long')
    .optional(),
  urgency: z.string()
    .max(50, 'Urgency too long')
    .optional(),
  payment: z.string()
    .max(50, 'Payment preference too long')
    .optional(),
  painPoints: z.string()
    .max(1000, 'Pain points description too long')
    .optional()
});

// Rate limiting schema
export const RateLimitSchema = z.object({
  ip: z.string().ip(),
  endpoint: z.string().min(1),
  timestamp: z.number().positive()
});

// Repair Issue validation schema
export const RepairIssueSchema = z.object({
  type: z.string().min(1, 'Issue type is required'),
  location: z.string().optional(),
  description: z.string().min(1, 'Description is required'),
  actionTaken: z.string().min(1, 'Action taken is required'),
  severity: z.enum(['Critical', 'Major', 'Minor'])
});

// Repair Report Section validation schema
export const RepairReportSectionSchema = z.object({
  title: z.string().min(1, 'Section title is required'),
  issues: z.array(RepairIssueSchema)
});

// Repair Report validation schema
export const RepairReportSchema = z.object({
  summary: z.string().min(1, 'Summary is required'),
  sections: z.array(RepairReportSectionSchema),
  performanceMetrics: z.object({
    analysisTime: z.string(),
    repairTime: z.string().optional()
  }),
  downloadLink: z.string().url().optional()
});

// File Processing Response schemas
export const FileProcessingSuccessSchema = z.object({
  success: z.literal(true),
  repairedFileData: z.string().optional(),
  originalFileName: z.string().optional(),
  repairedFileName: z.string().optional(),
  repairSummary: RepairReportSchema.optional(),
  message: z.string().optional()
});

export const FileProcessingErrorSchema = z.object({
  success: z.literal(false),
  error: z.string().min(1, 'Error message is required'),
  details: z.string().optional(),
  repairSummary: RepairReportSchema.optional()
});

// Generic API response schemas
export const ApiSuccessSchema = z.object({
  success: z.literal(true),
  data: z.any().optional(),
  message: z.string().optional()
});

export const ApiErrorSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  details: z.string().optional()
});

// Email submission response schemas
export const EmailSubmissionSuccessSchema = z.object({
  message: z.string().min(1, 'Success message is required')
});

export const EmailSubmissionErrorSchema = z.object({
  error: z.string().min(1, 'Error message is required'),
  details: z.string().optional()
});

// CSRF Token response schema
export const CsrfTokenResponseSchema = z.object({
  token: z.string().min(1, 'CSRF token is required')
});

// Validation helper function
export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return { success: false, error: errorMessage };
    }
    return { success: false, error: 'Invalid input data' };
  }
}

// Sanitization helpers
export function sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, ''); // Remove control characters
}

export function sanitizeEmail(email: string): string {
  return email
    .toLowerCase()
    .trim()
    .replace(/[^\w@.-]/g, ''); // Keep only valid email characters
}

// File Processing State schemas
export const FileProcessingStateSchema = z.object({
  status: z.enum(['idle', 'uploading', 'processing', 'completed', 'error']),
  progress: z.number().min(0).max(100),
  currentStep: z.string().optional(),
  estimatedTimeRemaining: z.number().optional(),
  memoryUsage: z.number().optional(),
  error: z.string().optional()
});

// File Upload State schema
export const FileUploadStateSchema = z.object({
  isDragActive: z.boolean(),
  isUploading: z.boolean(),
  uploadProgress: z.number().min(0).max(100),
  selectedFile: z.object({
    name: z.string(),
    size: z.number(),
    type: z.string()
  }).optional(),
  error: z.string().optional()
});

// Error schemas
export const ValidationErrorSchema = z.object({
  field: z.string(),
  message: z.string(),
  code: z.string().optional()
});

export const ApiErrorDetailSchema = z.object({
  type: z.enum(['validation', 'authentication', 'authorization', 'rate_limit', 'server_error', 'file_error']),
  message: z.string(),
  field: z.string().optional(),
  code: z.string().optional(),
  timestamp: z.string().datetime().optional()
});

// Configuration schemas
export const AppConfigSchema = z.object({
  maxFileSize: z.number().positive(),
  allowedFileTypes: z.array(z.string()),
  apiTimeout: z.number().positive(),
  rateLimits: z.object({
    fileProcessing: z.number().positive(),
    emailForm: z.number().positive()
  }),
  features: z.object({
    offlineSupport: z.boolean(),
    analytics: z.boolean(),
    errorReporting: z.boolean()
  })
});

// UI Component Props schemas
export const ProgressIndicatorPropsSchema = z.object({
  progress: z.number().min(0).max(100),
  status: z.string(),
  currentStep: z.string().optional(),
  estimatedTime: z.number().optional(),
  showDetails: z.boolean().optional()
});

export const ErrorDisplayPropsSchema = z.object({
  error: z.string(),
  type: z.enum(['validation', 'network', 'server', 'file']).optional(),
  onRetry: z.function().optional(),
  onDismiss: z.function().optional()
});

// File validation helpers
export function validateFileExtension(fileName: string): boolean {
  return fileName.toLowerCase().endsWith('.xlsx');
}

export function validateFileSize(size: number): boolean {
  return size > 0 && size <= 50 * 1024 * 1024; // 50MB limit
}

export function validateBase64(data: string): boolean {
  try {
    // Check if it's valid base64
    const decoded = Buffer.from(data, 'base64');
    const reencoded = decoded.toString('base64');
    return reencoded === data;
  } catch {
    return false;
  }
}

// Runtime validation helpers and type guards
export function isValidRepairReport(data: unknown): data is z.infer<typeof RepairReportSchema> {
  return RepairReportSchema.safeParse(data).success;
}

export function isValidFileProcessingResponse(data: unknown): data is z.infer<typeof FileProcessingSuccessSchema> | z.infer<typeof FileProcessingErrorSchema> {
  return FileProcessingSuccessSchema.safeParse(data).success || FileProcessingErrorSchema.safeParse(data).success;
}

export function isValidApiError(data: unknown): data is z.infer<typeof ApiErrorSchema> {
  return ApiErrorSchema.safeParse(data).success;
}

export function isValidEmailFormData(data: unknown): data is z.infer<typeof EmailFormSchema> {
  return EmailFormSchema.safeParse(data).success;
}

export function isValidFileUploadData(data: unknown): data is z.infer<typeof FileUploadSchema> {
  return FileUploadSchema.safeParse(data).success;
}

// Validation result type
export type ValidationResult<T> =
  | { success: true; data: T; error?: never }
  | { success: false; data?: never; error: string };

// Enhanced validation helper with better error formatting
export function validateInputWithDetails<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): ValidationResult<T> {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const formattedErrors = error.errors.map(err => {
        const path = err.path.length > 0 ? err.path.join('.') : 'root';
        return `${path}: ${err.message}`;
      });
      return { success: false, error: formattedErrors.join('; ') };
    }
    return { success: false, error: 'Invalid input data' };
  }
}

// Schema validation for environment variables
export const EnvironmentSchema = z.object({
  RESEND_API_KEY: z.string().min(1, 'Resend API key is required'),
  TO_EMAIL_ADDRESS: z.string().email('Invalid TO_EMAIL_ADDRESS'),
  FROM_EMAIL_ADDRESS: z.string().email('Invalid FROM_EMAIL_ADDRESS'),
  NODE_ENV: z.enum(['development', 'production', 'test']).optional()
});

// Validate environment variables
export function validateEnvironment(): ValidationResult<z.infer<typeof EnvironmentSchema>> {
  return validateInputWithDetails(EnvironmentSchema, {
    RESEND_API_KEY: process.env.RESEND_API_KEY,
    TO_EMAIL_ADDRESS: process.env.TO_EMAIL_ADDRESS,
    FROM_EMAIL_ADDRESS: process.env.FROM_EMAIL_ADDRESS,
    NODE_ENV: process.env.NODE_ENV
  });
}