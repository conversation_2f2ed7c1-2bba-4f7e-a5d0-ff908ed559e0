import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white">
      
      {/* CTA Section */}
      <div className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-lime-400 rounded-3xl p-8 lg:p-16 relative overflow-hidden">
            <div className="relative z-10 text-center max-w-4xl mx-auto">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Ready to fix your corrupted Excel file?
              </h2>
              <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                Don&apos;t let a corrupted spreadsheet slow you down. Upload your .xlsx file now and get it 
                repaired in seconds with our advanced algorithms.
              </p>
              
              <Link
                to="/repair"
                className="inline-flex items-center gap-2 bg-gray-900 text-white border border-gray-900 rounded-lg font-medium transition-colors hover:bg-lime-300 hover:text-gray-900 hover:border-gray-900 px-8 py-4 text-lg"
              >
                Repair Your File Now
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>
            
            {/* Background decoration */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-64 h-64 border border-gray-900 rounded-full -translate-x-32 -translate-y-32"></div>
              <div className="absolute bottom-0 right-0 w-96 h-96 border border-gray-900 rounded-full translate-x-48 translate-y-48"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
            
            {/* Left Column - Logo and Description */}
            <div className="space-y-8 lg:col-span-2 text-center">
              <Link to="/" className="inline-block">
                <img 
                  src="/logonew (1).webp" 
                  alt="SheetHealer" 
                  className="h-12 w-auto max-w-[250px] object-contain mx-auto filter invert"
                />
              </Link>
              
              <p className="text-gray-400 text-lg max-w-2xl mx-auto">
                SheetHealer is the #1 .xlsx file repair service. We use advanced algorithms to restore 
                corrupted files, fix broken formulas, and recover your valuable data quickly and securely.
              </p>
            </div>
          </div>
        </div>
        </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            <p className="text-gray-400 text-sm">
              © 2025 SheetHealer. All Rights Reserved
          </p>
            
            <div className="flex items-center gap-6 text-sm">
              <Link to="/privacy-policy" className="text-gray-400 hover:text-lime-400 transition-colors">
              Privacy Policy
            </Link>
              <Link to="/terms-of-service" className="text-gray-400 hover:text-lime-400 transition-colors">
              Terms of Service
            </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
