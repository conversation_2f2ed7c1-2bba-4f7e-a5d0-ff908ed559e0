import React from 'react';
import PrivacyPolicy from '../components/PrivacyPolicy';
import PageHeader from '../components/PageHeader';
import Footer from '../components/Footer';

const PrivacyPolicyPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <PageHeader />

      {/* Main content */}
      <main className="container mx-auto px-6 py-8 flex-grow">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-8">
          <PrivacyPolicy />
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default PrivacyPolicyPage;