import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Upload, Cloud, FileDown } from 'lucide-react';

const HowItWorksSection: React.FC = () => {
  const steps = [
    {
      number: '01',
      icon: Upload,
      title: 'Upload your file',
      description: 'Drag and drop your broken .xlsx workbook. It\'s encrypted while in transit and storage for maximum security.'
    },
    {
      number: '02',
      icon: Cloud,
      title: 'We fix it in the cloud',
      description: 'Our engine scans for corruption, patches the damage, and rechecks the file all in a few seconds.'
    },
    {
      number: '03',
      icon: FileDown,
      title: 'Download and get back to work',
      description: 'Grab the repaired file instantly. Get a clear summary of what was fixed in your .xlsx file.'
    }
  ];

  return (
    <section className="py-16 lg:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            How It Works
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Three simple steps to get your .xlsx files working again. Most files under 10MB are 
            fixed in under 5 seconds with our automated repair engine.
          </p>
        </div>

        {/* Process Steps */}
        <div className="relative">
          {/* Connection Line */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-lime-400 transform -translate-y-1/2"></div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
            {steps.map((step, index) => {
              const IconComponent = step.icon;
              
              return (
                <div key={index} className="text-center relative">
                  {/* Step Number and Icon */}
                  <div className="relative mb-8">
                    <div className="w-24 h-24 bg-lime-400 border-4 border-white rounded-full flex items-center justify-center mx-auto shadow-lg relative z-10">
                      <span className="text-xl font-bold text-gray-900">{step.number}</span>
                    </div>
                    
                    {/* Icon Container */}
                    <div className="absolute -bottom-2 -right-2 w-12 h-12 bg-gray-900 rounded-full flex items-center justify-center shadow-lg">
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  
                  {/* Step Content */}
                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-gray-900">
                      {step.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed max-w-sm mx-auto">
                      {step.description}
                    </p>
                  </div>
                </div>
              );
            })}
              </div>
            </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Link
            to="/repair"
                          className="inline-flex items-center gap-2 bg-gray-900 hover:bg-lime-300 text-white hover:text-gray-900 px-8 py-4 rounded-lg font-medium transition-colors text-lg border border-gray-900"
          >
            Fix Your First File Free
            <ArrowRight className="h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
