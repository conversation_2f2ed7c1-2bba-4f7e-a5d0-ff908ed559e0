#!/usr/bin/env node

/**
 * Create a truly corrupted Excel file for testing
 */

import fs from 'fs';
import path from 'path';

function createCorruptedFile() {
  // Start with a valid Excel file
  const validFile = './testfiles/test-normal.xlsx';
  
  if (!fs.existsSync(validFile)) {
    console.log('❌ Valid test file not found');
    return;
  }
  
  // Read the valid file
  const buffer = fs.readFileSync(validFile);
  console.log(`📁 Original file size: ${buffer.length} bytes`);
  
  // Corrupt the file by modifying random bytes
  const corruptedBuffer = Buffer.from(buffer);
  
  // Method 1: Corrupt ZIP header (Excel files are ZIP archives)
  if (corruptedBuffer.length > 10) {
    corruptedBuffer[0] = 0xFF; // Should be 0x50 for ZIP
    corruptedBuffer[1] = 0xFF; // Should be 0x4B for ZIP
    console.log('🔧 Corrupted ZIP header');
  }
  
  // Method 2: Corrupt some random bytes in the middle
  for (let i = 0; i < 5; i++) {
    const randomIndex = Math.floor(Math.random() * (corruptedBuffer.length - 100)) + 50;
    corruptedBuffer[randomIndex] = Math.floor(Math.random() * 256);
  }
  console.log('🔧 Corrupted random bytes');
  
  // Method 3: Truncate the file slightly
  const truncatedBuffer = corruptedBuffer.slice(0, corruptedBuffer.length - 100);
  console.log(`🔧 Truncated file to ${truncatedBuffer.length} bytes`);
  
  // Save the corrupted file
  const outputPath = './testfiles/test-truly-corrupted.xlsx';
  fs.writeFileSync(outputPath, truncatedBuffer);
  console.log(`💾 Saved corrupted file: ${outputPath}`);
  
  return outputPath;
}

function createPartiallyCorruptedFile() {
  // Create a file with just some XML corruption inside
  const validFile = './testfiles/test-normal.xlsx';
  
  if (!fs.existsSync(validFile)) {
    console.log('❌ Valid test file not found');
    return;
  }
  
  const buffer = fs.readFileSync(validFile);
  const corruptedBuffer = Buffer.from(buffer);
  
  // Find and corrupt XML content (look for common XML patterns)
  const xmlPattern = Buffer.from('</worksheet>');
  const index = corruptedBuffer.indexOf(xmlPattern);
  
  if (index !== -1) {
    // Replace with invalid XML
    const invalidXml = Buffer.from('</workshee>'); // Missing 't'
    invalidXml.copy(corruptedBuffer, index);
    console.log('🔧 Corrupted XML structure');
  }
  
  const outputPath = './testfiles/test-xml-corrupted.xlsx';
  fs.writeFileSync(outputPath, corruptedBuffer);
  console.log(`💾 Saved XML-corrupted file: ${outputPath}`);
  
  return outputPath;
}

console.log('🚀 Creating corrupted test files...');
console.log('=====================================');

const trulyCorrupted = createCorruptedFile();
const xmlCorrupted = createPartiallyCorruptedFile();

console.log('\n✅ Corrupted files created!');
console.log(`📄 Truly corrupted: ${trulyCorrupted}`);
console.log(`📄 XML corrupted: ${xmlCorrupted}`);
