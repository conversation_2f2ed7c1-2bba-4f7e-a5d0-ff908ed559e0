import { useState, useCallback, useRef, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import ExcelJS from 'exceljs';
import { MemoryManager } from '../lib/memoryManager';
import { useProgressiveLoader, LoadingSequences } from '../lib/loadingStates';
import { EnhancedErrorHandler, createFileError, createNetworkError, createProcessingError } from '../lib/errorMessages';
import { ProcessedFileResult } from '../components/FileUpload';
import { queryKeys } from '../lib/queryClient';
import { useFileAnalysis } from './useFileQueries';

const SUPPORTED_FORMATS = ['.xlsx'];

interface UseFileUploadOptions {
  onFileProcessed?: (result: ProcessedFileResult) => void;
  onError?: (error: any) => void;
}

interface FileUploadState {
  isDragOver: boolean;
  dragDepth: number;
  draggedFileType: string | null;
  isValidDrop: boolean;
  uploadedFile: File | null;
  currentError: any;
  showDetailedProgress: boolean;
  currentOperation: 'analyze' | 'repair';
}

export const useFileUploadWithQuery = (options: UseFileUploadOptions = {}) => {
  const { onFileProcessed, onError } = options;
  const queryClient = useQueryClient();

  const [state, setState] = useState<FileUploadState>({
    isDragOver: false,
    dragDepth: 0,
    draggedFileType: null,
    isValidDrop: true,
    uploadedFile: null,
    currentError: null,
    showDetailedProgress: false,
    currentOperation: 'analyze'
  });

  const processingSessionRef = useRef<{ processId: string; cleanup: () => void; monitor: (callback: (stats: any) => void) => NodeJS.Timeout } | null>(null);
  const progressiveLoader = useProgressiveLoader();

  // Use React Query for file analysis
  const {
    data: analysisResult,
    isLoading: isAnalyzing,
    error: analysisError,
    refetch: refetchAnalysis
  } = useFileAnalysis(state.uploadedFile, false); // Don't auto-fetch

  // File analysis mutation with React Query
  const analyzeFileMutation = useMutation({
    mutationFn: async (file: File) => {
      console.log('📄 Analyzing file with React Query:', file.name, file.size, file.type);

      // Start progressive loading
      progressiveLoader.start(LoadingSequences.fileUpload);

      // Create processing session with memory management
      const processingSession = MemoryManager.createProcessingSession(file.size);
      processingSessionRef.current = processingSession;

      // Start memory monitoring
      const memoryMonitor = processingSession.monitor((stats) => {
        // Update memory usage in state if needed
      });

      try {
        // Step 1: Upload
        progressiveLoader.updateStepProgress(50, 'Securely uploading your file...');
        await new Promise(resolve => setTimeout(resolve, 800));
        progressiveLoader.updateStepProgress(100);
        progressiveLoader.nextStep();

        // Step 2: Validate
        progressiveLoader.updateStepProgress(30, 'Checking file format and integrity...');
        await new Promise(resolve => setTimeout(resolve, 600));
        progressiveLoader.updateStepProgress(100);
        progressiveLoader.nextStep();

        // Step 3: Analyze using React Query
        progressiveLoader.updateStepProgress(20, 'Scanning for issues and errors...');
        
        // Trigger the analysis query
        const queryKey = queryKeys.fileAnalysis(file.name + file.size + file.lastModified);
        const result = await queryClient.fetchQuery({
          queryKey,
          queryFn: async () => {
            const arrayBuffer = await file.arrayBuffer();
            const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

            const response = await fetch('/api/excel-processor', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                fileData: base64Data,
                fileName: file.name,
                fileSize: file.size,
                operation: 'analyze'
              })
            });

            if (!response.ok) {
              throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (!data.success) {
              throw new Error(data.error || 'Analysis failed');
            }

            return data;
          },
          staleTime: 5 * 60 * 1000, // 5 minutes
        });

        progressiveLoader.updateStepProgress(100, 'Analysis complete!');
        progressiveLoader.complete();

        // Transform to ProcessedFileResult format
        const processedResult: ProcessedFileResult = {
          originalFile: file,
          workbook: null, // Will be populated if needed
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: new Date(file.lastModified)
          },
          sheets: [], // Will be populated from analysis
          status: result.success ? 'success' : 'error',
          message: result.repairSummary?.summary || 'File analyzed successfully',
          repairReport: result.repairSummary
        };

        return processedResult;

      } finally {
        // Clean up processing session
        if (processingSessionRef.current) {
          processingSessionRef.current.cleanup();
          processingSessionRef.current = null;
        }

        // Ensure progressive loading is completed or cancelled
        if (progressiveLoader.isLoading) {
          progressiveLoader.cancel();
        }
      }
    },
    onSuccess: (result) => {
      console.log('✅ File analysis complete with React Query:', result);
      onFileProcessed?.(result);
    },
    onError: (error) => {
      console.error('❌ File analysis failed:', error);
      
      const enhancedError = createProcessingError(
        error instanceof Error ? error.message : 'Failed to analyze file',
        state.uploadedFile?.name || 'unknown',
        progressiveLoader.currentStep || undefined
      );

      setState(prev => ({ ...prev, currentError: enhancedError }));
      onError?.(enhancedError);
    }
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (processingSessionRef.current) {
        processingSessionRef.current.cleanup();
        processingSessionRef.current = null;
      }
    };
  }, []);

  const updateState = useCallback((updates: Partial<FileUploadState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const validateFile = useCallback((file: File): { isValid: boolean; error?: any } => {
    // Use memory manager to check if file can be processed
    const canProcess = MemoryManager.canProcessFile(file.size);
    if (!canProcess.allowed) {
      const error = createFileError(
        canProcess.reason || 'File cannot be processed due to memory constraints.',
        file.name,
        file.size
      );
      return { isValid: false, error };
    }

    // Check for minimum file size (empty files)
    if (file.size < 100) {
      const error = createFileError(
        'File appears to be empty or too small to be a valid Excel file.',
        file.name,
        file.size
      );
      return { isValid: false, error };
    }

    // Check file extension
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!SUPPORTED_FORMATS.includes(fileExtension)) {
      const error = createFileError(
        'Unsupported file format. We only support .xlsx files currently.',
        file.name,
        file.size
      );
      return { isValid: false, error };
    }

    return { isValid: true };
  }, []);

  const handleFileUpload = useCallback((file: File) => {
    const validation = validateFile(file);
    if (!validation.isValid) {
      console.error('❌ File validation failed:', validation.error);
      updateState({ currentError: validation.error, uploadedFile: file });
      onError?.(validation.error);
      return;
    }

    updateState({ 
      uploadedFile: file, 
      currentError: null,
      showDetailedProgress: true,
      currentOperation: 'analyze'
    });
    
    // Trigger analysis with React Query
    analyzeFileMutation.mutate(file);
  }, [validateFile, updateState, onError, analyzeFileMutation]);

  const reset = useCallback(() => {
    console.log('🧹 Resetting file upload state with React Query');

    // Clean up any active processing session
    if (processingSessionRef.current) {
      processingSessionRef.current.cleanup();
      processingSessionRef.current = null;
    }

    setState({
      isDragOver: false,
      dragDepth: 0,
      draggedFileType: null,
      isValidDrop: true,
      uploadedFile: null,
      currentError: null,
      showDetailedProgress: false,
      currentOperation: 'analyze'
    });

    // Cancel progressive loading
    progressiveLoader.cancel();

    // Reset mutation state
    analyzeFileMutation.reset();
  }, [progressiveLoader, analyzeFileMutation]);

  // Drag and drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    updateState({ dragDepth: state.dragDepth + 1 });

    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      const item = e.dataTransfer.items[0];
      if (item.kind === 'file') {
        const fileType = item.type;
        updateState({ draggedFileType: fileType });

        // Check if it's a valid Excel file
        const isValid = fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                       fileType === 'application/vnd.ms-excel';
        updateState({ isValidDrop: isValid, isDragOver: true });
      }
    } else {
      // Fallback when we can't detect file type during drag
      updateState({ isDragOver: true, isValidDrop: true });
    }
  }, [state.dragDepth, updateState]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const newDepth = state.dragDepth - 1;
    updateState({ dragDepth: newDepth });

    if (newDepth === 0) {
      updateState({
        isDragOver: false,
        draggedFileType: null,
        isValidDrop: true
      });
    }
  }, [state.dragDepth, updateState]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Update drag effect based on validity
    e.dataTransfer.dropEffect = state.isValidDrop ? 'copy' : 'none';
  }, [state.isValidDrop]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    updateState({
      isDragOver: false,
      dragDepth: 0,
      draggedFileType: null,
      isValidDrop: true
    });

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  }, [handleFileUpload, updateState]);

  return {
    // State
    ...state,
    isProcessing: analyzeFileMutation.isPending,
    processingProgress: analyzeFileMutation.isPending ? 50 : 0,
    processingStatus: analyzeFileMutation.isPending ? 'Analyzing file...' : '',
    
    // Actions
    handleFileUpload,
    reset,
    
    // Drag and drop handlers
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    
    // Utilities
    validateFile,
    
    // React Query state
    analysisResult,
    isAnalyzing,
    analysisError,
    mutation: analyzeFileMutation
  };
};