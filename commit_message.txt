feat: Improve Excel file corruption detection and UI reporting

This commit addresses issues related to inaccurate file health reporting in the UI.

- Enhanced `checkForSevereCorruption` in `lib/excelRepair.ts` for more robust corruption detection.
- Refactored `api/repair-excel.ts` to ensure consistent JSON responses, especially in error scenarios, resolving 'Unexpected end of JSON input' errors.
- Standardized `repairSummary` object to align with `RepairReport` interface in `types.ts`.
- Updated `components/FileUpload.tsx` to correctly consume and display backend repair results.
- Adjusted `RepairResult` interface to allow `repairedWorkbook` to be null on repair failure.