import XLSX from 'xlsx';


// Import our repair service (we'll need to create a simple version for Node.js)
class SimpleExcelRepair {
  static repairWorkbook(workbook) {
    console.log('🔧 Starting repair process...');
    let repairsMap = new Map();
    let totalRepairs = 0;

    // Iterate through all sheets
    workbook.SheetNames.forEach(sheetName => {
      const sheet = workbook.Sheets[sheetName];
      const sheetRepairs = [];
      
      // Get the range of the sheet
      const range = XLSX.utils.decode_range(sheet['!ref'] || 'A1:A1');
      
      // Check each cell for errors
      for (let R = range.s.r; R <= range.e.r; R++) {
        for (let C = range.s.c; C <= range.e.c; C++) {
          const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
          const cell = sheet[cellRef];
          
          if (cell && cell.f) {
            let originalFormula = cell.f;
            let repairedFormula = originalFormula;
            let wasRepaired = false;
            
            // Fix common function name errors
            if (originalFormula.includes('SUMM(')) {
              repairedFormula = repairedFormula.replace(/SUMM\(/g, 'SUM(');
              wasRepaired = true;
            }
            
            if (originalFormula.includes('AVERAG(')) {
              repairedFormula = repairedFormula.replace(/AVERAG\(/g, 'AVERAGE(');
              wasRepaired = true;
            }
            
            // Fix unbalanced parentheses (simple version)
            const openParens = (repairedFormula.match(/\(/g) || []).length;
            const closeParens = (repairedFormula.match(/\)/g) || []).length;
            if (openParens > closeParens) {
              repairedFormula += ')'.repeat(openParens - closeParens);
              wasRepaired = true;
            }
            
            // Remove error-causing formulas and replace with safe values
            if (originalFormula.includes('/0') || 
                originalFormula.includes('XYZ999') ||
                originalFormula.includes('INVALID') ||
                originalFormula.includes('A100:B200') ||
                originalFormula.includes('SQRT(-1)') ||
                originalFormula.includes('VALUE("text")') ||
                originalFormula.includes('A1 A2')) {
              
              // Replace with a safe value
              delete cell.f;
              cell.v = 0;
              cell.t = 'n';
              wasRepaired = true;
              repairedFormula = '(removed error formula)';
            }
            
            // Fix circular references (simple detection)
            if (originalFormula.includes(cellRef)) {
              delete cell.f;
              cell.v = 0;
              cell.t = 'n';
              wasRepaired = true;
              repairedFormula = '(removed circular reference)';
            }
            
            if (wasRepaired) {
              cell.f = repairedFormula === '(removed error formula)' || repairedFormula === '(removed circular reference)' ? undefined : repairedFormula;
              sheetRepairs.push({
                cell: cellRef,
                original: originalFormula,
                repaired: repairedFormula
              });
              totalRepairs++;
            }
          }
          
          // Remove error values
          if (cell && cell.v && typeof cell.v === 'string') {
            if (cell.v.includes('#REF!') || cell.v.includes('#NAME?') || 
                cell.v.includes('#VALUE!') || cell.v.includes('#DIV/0!') ||
                cell.v.includes('#NULL!') || cell.v.includes('#NUM!')) {
              cell.v = 0;
              cell.t = 'n';
              sheetRepairs.push({
                cell: cellRef,
                original: 'Error value',
                repaired: '0'
              });
              totalRepairs++;
            }
          }
        }
      }
      
      if (sheetRepairs.length > 0) {
        repairsMap.set(sheetName, sheetRepairs);
      }
    });

    return {
      success: totalRepairs > 0,
      repairsCount: totalRepairs,
      repairsMap,
      repairedWorkbook: workbook
    };
  }
}

async function testRepairFunctionality() {
  const fileName = 'test-with-real-errors.xlsx';
  
  console.log('🔍 EXCEL REPAIR FUNCTIONALITY TEST');
  console.log('=' .repeat(50));
  
  try {
    // Read the original file
    console.log(`📖 Reading ${fileName}...`);
    const workbook = XLSX.readFile(fileName);
    
    // Repair the file
    console.log('🔧 Applying repairs...');
    const repairResult = SimpleExcelRepair.repairWorkbook(workbook);
    
    if (repairResult.success) {
      console.log(`\n✅ Repair completed! ${repairResult.repairsCount} issues fixed.`);
      
      // Show detailed repairs
      console.log('\n📋 Detailed Repairs:');
      repairResult.repairsMap.forEach((repairs, sheetName) => {
        console.log(`\n  📄 Sheet: ${sheetName}`);
        repairs.forEach((repair, index) => {
          console.log(`     ${index + 1}. Cell ${repair.cell}:`);
          console.log(`        Before: ${repair.original}`);
          console.log(`        After:  ${repair.repaired}`);
        });
      });
      
      // Save repaired file
      const repairedFileName = fileName.replace('.xlsx', '_REPAIRED.xlsx');
      XLSX.writeFile(repairResult.repairedWorkbook, repairedFileName);
      console.log(`\n💾 Repaired file saved as: ${repairedFileName}`);
      
      // Compare using our verification tool
      console.log('\n🔍 Running before/after comparison...');
      
    } else {
      console.log('❌ No repairs were needed or possible.');
    }
    
  } catch (error) {
    console.error('❌ Error during repair test:', error.message);
  }
}

// Run the test
testRepairFunctionality(); 