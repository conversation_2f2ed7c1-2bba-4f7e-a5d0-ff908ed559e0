import { VercelRequest, VercelResponse } from '@vercel/node';

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

// In-memory store for rate limiting (in production, use Redis or similar)
const rateLimitStore = new Map<string, RateLimitEntry>();

// Rate limit configurations
export const RATE_LIMITS = {
  // File processing endpoints - more restrictive
  FILE_PROCESSING: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 requests per minute
    message: 'Too many file processing requests. Please wait before trying again.'
  },
  
  // Email form - moderate restriction
  EMAIL_FORM: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 3, // 3 requests per minute
    message: 'Too many form submissions. Please wait before trying again.'
  },
  
  // General API - less restrictive
  GENERAL: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20, // 20 requests per minute
    message: 'Too many requests. Please slow down.'
  }
};

/**
 * Get client IP address from request
 */
function getClientIP(req: VercelRequest): string {
  // Check various headers for the real IP
  const forwarded = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  const cfConnectingIP = req.headers['cf-connecting-ip']; // Cloudflare
  
  if (typeof forwarded === 'string') {
    return forwarded.split(',')[0].trim();
  }
  
  if (typeof realIP === 'string') {
    return realIP;
  }
  
  if (typeof cfConnectingIP === 'string') {
    return cfConnectingIP;
  }
  
  // Fallback to connection remote address
  return req.socket?.remoteAddress || 'unknown';
}

/**
 * Create a rate limit key
 */
function createRateLimitKey(ip: string, endpoint: string): string {
  return `${ip}:${endpoint}`;
}

/**
 * Clean up expired entries from the rate limit store
 */
function cleanupExpiredEntries(): void {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Check if request should be rate limited
 */
export function checkRateLimit(
  req: VercelRequest,
  endpoint: string,
  config: typeof RATE_LIMITS.FILE_PROCESSING
): { allowed: boolean; remaining: number; resetTime: number; error?: string } {
  const ip = getClientIP(req);
  const key = createRateLimitKey(ip, endpoint);
  const now = Date.now();
  
  // Clean up expired entries periodically
  if (Math.random() < 0.1) { // 10% chance to cleanup
    cleanupExpiredEntries();
  }
  
  const entry = rateLimitStore.get(key);
  
  if (!entry || now > entry.resetTime) {
    // First request or window expired - create new entry
    const newEntry: RateLimitEntry = {
      count: 1,
      resetTime: now + config.windowMs
    };
    rateLimitStore.set(key, newEntry);
    
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: newEntry.resetTime
    };
  }
  
  // Check if limit exceeded
  if (entry.count >= config.maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime,
      error: config.message
    };
  }
  
  // Increment counter
  entry.count++;
  rateLimitStore.set(key, entry);
  
  return {
    allowed: true,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime
  };
}

/**
 * Rate limiting middleware
 */
export function rateLimitMiddleware(
  endpoint: string,
  config: typeof RATE_LIMITS.FILE_PROCESSING
) {
  return (req: VercelRequest, res: VercelResponse, next?: () => void): boolean => {
    const result = checkRateLimit(req, endpoint, config);
    
    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', config.maxRequests);
    res.setHeader('X-RateLimit-Remaining', result.remaining);
    res.setHeader('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000));
    
    if (!result.allowed) {
      res.status(429).json({
        success: false,
        error: result.error || 'Rate limit exceeded',
        retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
      });
      return false;
    }
    
    if (next) next();
    return true;
  };
}

/**
 * Enhanced rate limiting for suspicious behavior
 */
export function detectSuspiciousActivity(req: VercelRequest): boolean {
  const ip = getClientIP(req);
  const userAgent = req.headers['user-agent'] || '';
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i
  ];
  
  // Check user agent
  if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
    console.warn(`Suspicious user agent detected: ${userAgent} from IP: ${ip}`);
    return true;
  }
  
  // Check for missing common headers
  if (!req.headers['accept'] || !req.headers['accept-language']) {
    console.warn(`Missing common headers from IP: ${ip}`);
    return true;
  }
  
  return false;
}

/**
 * Get rate limit status for monitoring
 */
export function getRateLimitStats(): {
  totalEntries: number;
  activeIPs: string[];
  topRequesters: Array<{ ip: string; requests: number }>;
} {
  const now = Date.now();
  const activeEntries = Array.from(rateLimitStore.entries())
    .filter(([_, entry]) => now <= entry.resetTime);
  
  const ipCounts = new Map<string, number>();
  
  for (const [key, entry] of activeEntries) {
    const ip = key.split(':')[0];
    ipCounts.set(ip, (ipCounts.get(ip) || 0) + entry.count);
  }
  
  const topRequesters = Array.from(ipCounts.entries())
    .map(([ip, requests]) => ({ ip, requests }))
    .sort((a, b) => b.requests - a.requests)
    .slice(0, 10);
  
  return {
    totalEntries: activeEntries.length,
    activeIPs: Array.from(ipCounts.keys()),
    topRequesters
  };
}