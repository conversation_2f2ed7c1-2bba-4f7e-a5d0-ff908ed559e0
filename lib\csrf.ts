import { VercelRequest, VercelResponse } from '@vercel/node';
import { createHash, randomBytes } from 'crypto';

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32;
const CSRF_TOKEN_EXPIRY = 60 * 60 * 1000; // 1 hour in milliseconds
const CSRF_SECRET = process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production';

interface CSRFTokenData {
  token: string;
  timestamp: number;
  ip: string;
}

// In-memory store for CSRF tokens (in production, use Redis or similar)
const csrfTokenStore = new Map<string, CSRFTokenData>();

/**
 * Generate a CSRF token
 */
export function generateCSRFToken(ip: string): string {
  const token = randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
  const timestamp = Date.now();
  
  // Store token with metadata
  csrfTokenStore.set(token, {
    token,
    timestamp,
    ip
  });
  
  // Clean up expired tokens periodically
  if (Math.random() < 0.1) { // 10% chance
    cleanupExpiredTokens();
  }
  
  return token;
}

/**
 * Validate a CSRF token
 */
export function validateCSRFToken(token: string, ip: string): boolean {
  if (!token) {
    return false;
  }
  
  const tokenData = csrfTokenStore.get(token);
  
  if (!tokenData) {
    return false;
  }
  
  // Check if token is expired
  if (Date.now() - tokenData.timestamp > CSRF_TOKEN_EXPIRY) {
    csrfTokenStore.delete(token);
    return false;
  }
  
  // Check if IP matches (optional, can be disabled for mobile users)
  if (tokenData.ip !== ip) {
    console.warn(`CSRF token IP mismatch: expected ${tokenData.ip}, got ${ip}`);
    // For now, we'll allow IP changes (mobile users, proxies, etc.)
    // return false;
  }
  
  // Token is valid, remove it (one-time use)
  csrfTokenStore.delete(token);
  return true;
}

/**
 * Clean up expired CSRF tokens
 */
function cleanupExpiredTokens(): void {
  const now = Date.now();
  for (const [token, data] of csrfTokenStore.entries()) {
    if (now - data.timestamp > CSRF_TOKEN_EXPIRY) {
      csrfTokenStore.delete(token);
    }
  }
}

/**
 * Get client IP address
 */
function getClientIP(req: VercelRequest): string {
  const forwarded = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  const cfConnectingIP = req.headers['cf-connecting-ip'];
  
  if (typeof forwarded === 'string') {
    return forwarded.split(',')[0].trim();
  }
  
  if (typeof realIP === 'string') {
    return realIP;
  }
  
  if (typeof cfConnectingIP === 'string') {
    return cfConnectingIP;
  }
  
  return req.socket?.remoteAddress || 'unknown';
}

/**
 * CSRF protection middleware for API endpoints
 */
export function csrfProtection(req: VercelRequest, res: VercelResponse): boolean {
  const ip = getClientIP(req);
  
  // Skip CSRF for OPTIONS requests
  if (req.method === 'OPTIONS') {
    return true;
  }
  
  // For GET requests, generate and return a CSRF token
  if (req.method === 'GET') {
    const token = generateCSRFToken(ip);
    res.setHeader('X-CSRF-Token', token);
    return true;
  }
  
  // For POST/PUT/DELETE requests, validate CSRF token
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method || '')) {
    const token = req.headers['x-csrf-token'] as string || req.body?.csrfToken;
    
    if (!validateCSRFToken(token, ip)) {
      res.status(403).json({
        success: false,
        error: 'CSRF token validation failed',
        code: 'CSRF_INVALID'
      });
      return false;
    }
  }
  
  return true;
}

/**
 * Generate CSRF token endpoint
 */
export function handleCSRFTokenRequest(req: VercelRequest, res: VercelResponse): void {
  if (req.method !== 'GET') {
    res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
    return;
  }
  
  const ip = getClientIP(req);
  const token = generateCSRFToken(ip);
  
  res.status(200).json({
    success: true,
    csrfToken: token,
    expiresIn: CSRF_TOKEN_EXPIRY
  });
}

/**
 * Enhanced CSRF protection with double-submit cookie pattern
 */
export function doubleSubmitCSRFProtection(req: VercelRequest, res: VercelResponse): boolean {
  const ip = getClientIP(req);
  
  // Skip for OPTIONS
  if (req.method === 'OPTIONS') {
    return true;
  }
  
  // For state-changing requests
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method || '')) {
    const headerToken = req.headers['x-csrf-token'] as string;
    const cookieToken = req.headers.cookie?.match(/csrfToken=([^;]+)/)?.[1];
    const bodyToken = req.body?.csrfToken;
    
    // Check if tokens exist and match
    const token = headerToken || bodyToken;
    
    if (!token || !cookieToken || token !== cookieToken) {
      res.status(403).json({
        success: false,
        error: 'CSRF protection: token mismatch',
        code: 'CSRF_TOKEN_MISMATCH'
      });
      return false;
    }
    
    // Additional validation
    if (!validateCSRFToken(token, ip)) {
      res.status(403).json({
        success: false,
        error: 'CSRF protection: invalid token',
        code: 'CSRF_INVALID_TOKEN'
      });
      return false;
    }
  }
  
  return true;
}

/**
 * Get CSRF statistics for monitoring
 */
export function getCSRFStats(): {
  activeTokens: number;
  expiredTokens: number;
  totalGenerated: number;
} {
  const now = Date.now();
  let activeTokens = 0;
  let expiredTokens = 0;
  
  for (const [_, data] of csrfTokenStore.entries()) {
    if (now - data.timestamp > CSRF_TOKEN_EXPIRY) {
      expiredTokens++;
    } else {
      activeTokens++;
    }
  }
  
  return {
    activeTokens,
    expiredTokens,
    totalGenerated: activeTokens + expiredTokens
  };
}

/**
 * Validate Origin header for additional CSRF protection
 */
export function validateOrigin(req: VercelRequest, allowedOrigins: string[]): boolean {
  const origin = req.headers.origin;
  const referer = req.headers.referer;
  
  // For same-origin requests, origin might be undefined
  if (!origin && !referer) {
    return true; // Allow same-origin requests
  }
  
  // Check origin
  if (origin && allowedOrigins.includes(origin)) {
    return true;
  }
  
  // Check referer as fallback
  if (referer) {
    const refererOrigin = new URL(referer).origin;
    if (allowedOrigins.includes(refererOrigin)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Complete CSRF protection with origin validation
 */
export function fullCSRFProtection(
  req: VercelRequest, 
  res: VercelResponse,
  allowedOrigins: string[] = ['http://localhost:5173', 'http://localhost:5174', 'https://sheethealer.com']
): boolean {
  // Validate origin first
  if (!validateOrigin(req, allowedOrigins)) {
    res.status(403).json({
      success: false,
      error: 'Invalid origin',
      code: 'INVALID_ORIGIN'
    });
    return false;
  }
  
  // Then validate CSRF token
  return csrfProtection(req, res);
}