#!/usr/bin/env node

/**
 * Test script to verify the fixes for file upload and repair functionality
 */

import fs from 'fs';
import path from 'path';

const API_BASE = 'http://localhost:5174';

async function testFileUpload(filePath, operation = 'analyze') {
  try {
    console.log(`\n🧪 Testing ${operation} operation with file: ${path.basename(filePath)}`);
    
    // Read the test file
    const fileBuffer = fs.readFileSync(filePath);
    const base64Data = fileBuffer.toString('base64');
    const fileName = path.basename(filePath);
    const fileSize = fileBuffer.length;
    
    console.log(`📁 File: ${fileName} (${(fileSize / 1024).toFixed(2)} KB)`);
    
    // Make API request
    const response = await fetch(`${API_BASE}/api/excel-processor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileData: base64Data,
        fileName,
        fileSize,
        operation
      })
    });
    
    const result = await response.json();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`✅ Success: ${result.success}`);
    
    if (result.success) {
      const repairSummary = result.data?.repairSummary || result.repairSummary;
      console.log(`📋 Summary: ${repairSummary?.summary || 'No summary'}`);

      if (repairSummary?.sections) {
        console.log(`🔍 Issues found: ${repairSummary.sections.length} section(s)`);
        repairSummary.sections.forEach((section, i) => {
          console.log(`  Section ${i + 1}: ${section.title} (${section.issues.length} issues)`);
          section.issues.forEach((issue, j) => {
            console.log(`    Issue ${j + 1}: ${issue.type} - ${issue.description}`);
            console.log(`    Action: ${issue.actionTaken}`);
          });
        });
      }

      const repairedFileData = result.data?.repairedFileData || result.repairedFileData;
      if (operation === 'repair' && repairedFileData) {
        console.log(`💾 Repaired file data received: ${repairedFileData.length} characters`);
        console.log(`📄 Original filename: ${result.originalFileName}`);
        console.log(`📄 Repaired filename: ${result.repairedFileName}`);
      }
    } else {
      console.log(`❌ Error: ${result.error}`);
      const repairSummary = result.data?.repairSummary || result.repairSummary;
      if (repairSummary) {
        console.log(`📋 Error Summary: ${repairSummary.summary}`);
      }
    }
    
    return result;
    
  } catch (error) {
    console.error(`💥 Test failed:`, error.message);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting SheetHealer API Tests');
  console.log('=====================================');
  
  const testFiles = [
    './testfiles/test-xml-corrupted.xlsx'
  ];
  
  for (const filePath of testFiles) {
    if (fs.existsSync(filePath)) {
      // Test analysis first
      const analysisResult = await testFileUpload(filePath, 'analyze');
      
      // If analysis shows issues that can be repaired, test repair
      const repairSummary = analysisResult?.data?.repairSummary || analysisResult?.repairSummary;
      if (analysisResult && analysisResult.success &&
          repairSummary?.sections?.some(section =>
            section.issues.some(issue =>
              issue.actionTaken.includes('We can') ||
              issue.actionTaken.includes('try to repair')
            )
          )) {
        console.log('\n🔧 File appears repairable, testing repair...');
        await testFileUpload(filePath, 'repair');
      }
    } else {
      console.log(`⚠️ Test file not found: ${filePath}`);
    }
  }
  
  console.log('\n✅ Tests completed!');
}

// Run the tests
runTests().catch(console.error);
