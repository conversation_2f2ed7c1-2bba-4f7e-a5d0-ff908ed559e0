import fs from 'fs';
import * as XLSX from 'xlsx';

// Simulate the scenario: 6 errors detected, only 5 fixed, 1 remains
function simulatePartialRepairScenario() {
  console.log('🧪 Testing Partial Repair Scenario');
  console.log('Scenario: 6 errors detected, 5 fixed, 1 remaining');
  
  // Simulate original file analysis
  const originalAnalysis = {
    sheets: [
      {
        name: 'Sheet1',
        cellCount: 50,
        hasFormulas: true,
        errors: [
          'Cell A1: #DIV/0!',
          'Cell B2: #NAME?',
          'Cell C3: #REF!',
          'Cell D4: #VALUE!',
          'Cell E5: #NUM!',
          'Cell F6: #NULL!'
        ]
      }
    ],
    repairReport: {
      sections: [{
        title: 'Repair Log',
        issues: [
          {
            type: 'Error cells',
            description: 'Found 6 error cells',
            actionTaken: 'We can remove these error cells',
            severity: 'medium'
          }
        ]
      }]
    }
  };
  
  // Simulate partially repaired file (5 out of 6 errors fixed)
  const partiallyRepairedAnalysis = {
    sheets: [
      {
        name: 'Sheet1',
        cellCount: 50,
        hasFormulas: true,
        errors: [
          'Cell F6: #NULL!' // Only 1 error remains
        ]
      }
    ],
    repairReport: {
      sections: [{
        title: 'Repair Log',
        issues: [
          {
            type: 'Error cells',
            description: 'Removed 5 error cells',
            actionTaken: 'Removed error cells',
            severity: 'medium'
          },
          {
            type: 'Remaining issues',
            description: 'Found 1 remaining error cell',
            actionTaken: 'We can remove this error cell',
            severity: 'medium'
          }
        ]
      }]
    }
  };
  
  console.log('\n📊 Original Analysis:');
  console.log(`  - Total errors: ${originalAnalysis.sheets[0].errors.length}`);
  
  console.log('\n🔧 After Partial Repair:');
  console.log(`  - Remaining errors: ${partiallyRepairedAnalysis.sheets[0].errors.length}`);
  console.log(`  - Issues fixed: 5`);
  
  // Test the logic from our components
  function testStatusLogic(analysis, isRepairedFile) {
    const sheets = analysis.sheets;
    const repairReport = analysis.repairReport;
    
    // Calculate stats
    const issuesFixed = isRepairedFile ? 5 : 0; // Simulated fixed count
    const remainingIssues = sheets.reduce((sum, sheet) => sum + sheet.errors.length, 0);
    
    // Determine if file needs repair
    const hasDetectedIssues = sheets.some(sheet => sheet.errors.length > 0) ||
      (repairReport && repairReport.sections.some(section => 
        section.issues.some(issue => 
          issue.actionTaken.includes('We can') && 
          !issue.actionTaken.includes('We checked')
        )
      ));
    
    const hasIssues = hasDetectedIssues && !isRepairedFile;
    
    // Status message logic
    let statusMessage = '';
    let leftSideTitle = '';
    
    if (isRepairedFile) {
      if (remainingIssues > 0) {
        // Partially repaired file - some issues remain
        statusMessage = `We fixed ${issuesFixed} problem${issuesFixed !== 1 ? 's' : ''}, but ${remainingIssues} issue${remainingIssues !== 1 ? 's' : ''} still remain${remainingIssues === 1 ? 's' : ''}. Your file may need additional attention.`;
        leftSideTitle = "Partial Fix! Some Issues Remain";
      } else {
        // Fully repaired file
        statusMessage = `Excellent! We successfully fixed ${issuesFixed} problem${issuesFixed !== 1 ? 's' : ''} in your file. It's now ready to use.`;
        leftSideTitle = "Your File is Fixed! Ready to Download";
      }
    } else if (hasIssues) {
      const issueCount = remainingIssues;
      statusMessage = `We found ${issueCount} problem${issueCount !== 1 ? 's' : ''} in your file. They're mostly minor issues and we have a 95% chance of fixing them all.`;
      leftSideTitle = "We Found Problems! Let's Fix Them";
    } else {
      statusMessage = 'Perfect! Your file is healthy and ready to use.';
      leftSideTitle = "File is Healthy! Ready to Download";
    }
    
    return {
      statusMessage,
      leftSideTitle,
      hasIssues,
      issuesFixed,
      remainingIssues,
      isConsistent: true // We'll determine this
    };
  }
  
  console.log('\n🎯 Testing Status Logic:');
  
  // Test original file (before repair)
  const originalStatus = testStatusLogic(originalAnalysis, false);
  console.log('\n📝 Original File Status:');
  console.log(`  - Left title: "${originalStatus.leftSideTitle}"`);
  console.log(`  - Status message: "${originalStatus.statusMessage}"`);
  console.log(`  - Has issues: ${originalStatus.hasIssues}`);
  console.log(`  - Show Fix button: ${originalStatus.hasIssues ? 'YES' : 'NO'}`);
  
  // Test partially repaired file
  const repairedStatus = testStatusLogic(partiallyRepairedAnalysis, true);
  console.log('\n📝 Partially Repaired File Status:');
  console.log(`  - Left title: "${repairedStatus.leftSideTitle}"`);
  console.log(`  - Status message: "${repairedStatus.statusMessage}"`);
  console.log(`  - Has issues: ${repairedStatus.hasIssues}`);
  console.log(`  - Issues fixed: ${repairedStatus.issuesFixed}`);
  console.log(`  - Remaining issues: ${repairedStatus.remainingIssues}`);
  console.log(`  - Show Fix button: ${repairedStatus.remainingIssues > 0 ? 'YES' : 'NO'}`);
  
  // Check consistency
  const isConsistent = 
    (originalStatus.hasIssues && originalStatus.leftSideTitle.includes('Problems')) &&
    (repairedStatus.remainingIssues > 0 ? 
      repairedStatus.leftSideTitle.includes('Partial') || repairedStatus.statusMessage.includes('still remain') :
      repairedStatus.leftSideTitle.includes('Fixed'));
  
  console.log('\n✅ Consistency Check:');
  console.log(`  - Original file correctly shows problems: ${originalStatus.hasIssues ? 'YES' : 'NO'}`);
  console.log(`  - Partially repaired file shows remaining issues: ${repairedStatus.remainingIssues > 0 ? 'YES' : 'NO'}`);
  console.log(`  - Messages are consistent: ${isConsistent ? 'YES' : 'NO'}`);
  
  return {
    scenario: 'Partial Repair (6→1 remaining)',
    original: originalStatus,
    repaired: repairedStatus,
    isConsistent
  };
}

// Test fully repaired scenario for comparison
function simulateFullRepairScenario() {
  console.log('\n🧪 Testing Full Repair Scenario');
  console.log('Scenario: 6 errors detected, all 6 fixed, 0 remaining');
  
  const fullyRepairedAnalysis = {
    sheets: [
      {
        name: 'Sheet1',
        cellCount: 50,
        hasFormulas: true,
        errors: [] // No errors remain
      }
    ],
    repairReport: {
      sections: [{
        title: 'Repair Log',
        issues: [
          {
            type: 'Error cells',
            description: 'Removed 6 error cells',
            actionTaken: 'Removed error cells',
            severity: 'medium'
          }
        ]
      }]
    }
  };
  
  function testStatusLogic(analysis, isRepairedFile) {
    const sheets = analysis.sheets;
    const issuesFixed = isRepairedFile ? 6 : 0;
    const remainingIssues = sheets.reduce((sum, sheet) => sum + sheet.errors.length, 0);
    
    let statusMessage = '';
    let leftSideTitle = '';
    
    if (isRepairedFile) {
      if (remainingIssues > 0) {
        statusMessage = `We fixed ${issuesFixed} problem${issuesFixed !== 1 ? 's' : ''}, but ${remainingIssues} issue${remainingIssues !== 1 ? 's' : ''} still remain${remainingIssues === 1 ? 's' : ''}. Your file may need additional attention.`;
        leftSideTitle = "Partial Fix! Some Issues Remain";
      } else {
        statusMessage = `Excellent! We successfully fixed ${issuesFixed} problem${issuesFixed !== 1 ? 's' : ''} in your file. It's now ready to use.`;
        leftSideTitle = "Your File is Fixed! Ready to Download";
      }
    }
    
    return { statusMessage, leftSideTitle, remainingIssues, issuesFixed };
  }
  
  const fullyRepairedStatus = testStatusLogic(fullyRepairedAnalysis, true);
  console.log('\n📝 Fully Repaired File Status:');
  console.log(`  - Left title: "${fullyRepairedStatus.leftSideTitle}"`);
  console.log(`  - Status message: "${fullyRepairedStatus.statusMessage}"`);
  console.log(`  - Issues fixed: ${fullyRepairedStatus.issuesFixed}`);
  console.log(`  - Remaining issues: ${fullyRepairedStatus.remainingIssues}`);
  console.log(`  - Show Fix button: ${fullyRepairedStatus.remainingIssues > 0 ? 'YES' : 'NO'}`);
  
  return {
    scenario: 'Full Repair (6→0 remaining)',
    repaired: fullyRepairedStatus,
    isConsistent: fullyRepairedStatus.remainingIssues === 0 && fullyRepairedStatus.leftSideTitle.includes('Fixed')
  };
}

console.log('🎯 Testing Partial Repair Fix Logic');
console.log('='.repeat(60));

const partialResult = simulatePartialRepairScenario();
const fullResult = simulateFullRepairScenario();

console.log('\n🎉 Test Summary:');
console.log('='.repeat(40));
console.log(`✅ ${partialResult.scenario}: ${partialResult.isConsistent ? 'PASS' : 'FAIL'}`);
console.log(`✅ ${fullResult.scenario}: ${fullResult.isConsistent ? 'PASS' : 'FAIL'}`);

const allPass = partialResult.isConsistent && fullResult.isConsistent;
console.log(`\n🎯 Overall Result: ${allPass ? '✅ ALL TESTS PASS!' : '❌ Some tests failed'}`);

if (allPass) {
  console.log('\n🎉 Great! The partial repair logic is working correctly:');
  console.log('• Partially repaired files show remaining issue count');
  console.log('• Fully repaired files show success message');
  console.log('• Messages are consistent with actual file state');
  console.log('• Users get clear guidance on next steps');
}
