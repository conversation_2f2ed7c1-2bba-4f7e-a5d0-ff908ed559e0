import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const FAQSection: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: 'What are the pricing options?',
      answer: 'We offer two simple plans: a One-Time Fix for $9 per file, or an Unlimited Monthly plan for $19/month that lets you repair as many files as you need.'
    },
    {
      question: 'What file types do you support?',
      answer: 'We currently specialize in repairing .xlsx files, which is the standard format for modern versions of Microsoft Excel.'
    },
    {
      question: 'How does the One-Time Fix work?',
      answer: 'Simply upload your file, and if we can fix it, you pay $9 to download the repaired version. If we can\'t fix it, you don\'t pay anything.'
    },
    {
      question: 'What benefits do I get with the Unlimited plan?',
      answer: 'For $19/month, you get unlimited file repairs, priority processing in our queue, detailed repair reports, and access to your file history for 30 days.'
    },
    {
      question: 'Is my data secure?',
      answer: 'Absolutely. Your files are encrypted both in transit and at rest. We automatically delete all files from our servers 24 hours after upload.'
    },
    {
      question: 'What happens if you can\'t repair my file?',
      answer: 'In the rare case that a repair is unsuccessful, you will not be charged for the One-Time Fix. Our system will provide a report on what went wrong.'
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-16 lg:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Got questions about SheetHealer? Find answers to the most common questions 
            about how our .xlsx repair service works, security, and pricing.
          </p>
        </div>

        {/* FAQ Grid */}
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-start">
          
          {/* Right Column - FAQ Accordion */}
        <div className="space-y-4 lg:col-span-2">
            {faqs.map((faq, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl border border-gray-200 overflow-hidden"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <span className="font-medium text-gray-900 pr-4">
                    {faq.question}
                  </span>
                  <div className="flex-shrink-0 w-6 h-6 bg-lime-400 rounded-full flex items-center justify-center">
                    {openIndex === index ? (
                      <ChevronUp className="h-4 w-4 text-gray-900" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-900" />
                    )}
                  </div>
                </button>
                
                {openIndex === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
          ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
