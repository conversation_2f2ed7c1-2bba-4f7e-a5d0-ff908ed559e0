import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys, generateFileHash } from '../lib/queryClient';
import { ApiClient } from '../lib/apiClient';
import { ProcessedFileResult } from '../components/FileUpload';

// File analysis query hook
export const useFileAnalysis = (file: File | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: file ? queryKeys.fileAnalysis(file.name + file.size + file.lastModified) : [],
    queryFn: async (): Promise<ProcessedFileResult> => {
      if (!file) throw new Error('No file provided');
      
      const arrayBuffer = await file.arrayBuffer();
      const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      
      const response = await ApiClient.analyzeFile(
        '/api/excel-processor',
        base64Data,
        file.name,
        file.size,
        'analyze'
      );
      
      if (!response.success) {
        throw new Error(response.error || 'Analysis failed');
      }
      
      // Transform API response to ProcessedFileResult format
      return {
        originalFile: file,
        workbook: null, // Will be populated by the hook consumer if needed
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: new Date(file.lastModified)
        },
        sheets: [], // Will be populated from response data
        status: response.data?.repairSummary ? 'success' : 'error',
        message: response.data?.repairSummary?.summary || 'Analysis completed',
        repairReport: response.data?.repairSummary
      };
    },
    enabled: enabled && !!file,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// File repair mutation hook
export const useFileRepair = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (file: File): Promise<ProcessedFileResult> => {
      const arrayBuffer = await file.arrayBuffer();
      const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      
      const response = await ApiClient.uploadFile(
        '/api/excel-processor',
        base64Data,
        file.name,
        file.size,
        'repair'
      );
      
      if (!response.success) {
        throw new Error(response.error || 'Repair failed');
      }
      
      // Transform API response to ProcessedFileResult format
      return {
        originalFile: file,
        workbook: null, // Will be populated by the hook consumer if needed
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: new Date(file.lastModified)
        },
        sheets: [], // Will be populated from response data
        status: response.data?.repairSummary ? 'success' : 'error',
        message: response.data?.repairSummary?.summary || 'Repair completed',
        repairReport: response.data?.repairSummary
      };
    },
    onSuccess: (data, file) => {
      // Invalidate and update the analysis cache for this file
      const analysisKey = queryKeys.fileAnalysis(file.name + file.size + file.lastModified);
      queryClient.setQueryData(analysisKey, data);
      
      // Cache the repair result
      const repairKey = queryKeys.fileRepair(file.name + file.size + file.lastModified);
      queryClient.setQueryData(repairKey, data);
    },
  });
};

// CSRF token query hook
export const useCsrfToken = () => {
  return useQuery({
    queryKey: queryKeys.csrfToken(),
    queryFn: async () => {
      const response = await ApiClient.getCsrfToken();
      if (!response.success) {
        throw new Error(response.error || 'Failed to get CSRF token');
      }
      return response.data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Form submission mutation hook
export const useFormSubmission = () => {
  return useMutation({
    mutationFn: async (formData: { 
      endpoint: string; 
      data: Record<string, any>; 
      csrfToken?: string;
    }) => {
      const { endpoint, data, csrfToken } = formData;
      
      const submitData = csrfToken ? { ...data, csrfToken } : data;
      
      const response = await ApiClient.submitForm(endpoint, submitData);
      
      if (!response.success) {
        throw new Error(response.error || 'Form submission failed');
      }
      
      return response.data;
    },
  });
};

// Prefetch file analysis
export const usePrefetchFileAnalysis = () => {
  const queryClient = useQueryClient();
  
  return (file: File) => {
    const queryKey = queryKeys.fileAnalysis(file.name + file.size + file.lastModified);
    
    queryClient.prefetchQuery({
      queryKey,
      queryFn: async () => {
        const arrayBuffer = await file.arrayBuffer();
        const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
        
        const response = await ApiClient.analyzeFile(
          '/api/excel-processor',
          base64Data,
          file.name,
          file.size,
          'analyze'
        );
        
        if (!response.success) {
          throw new Error(response.error || 'Analysis failed');
        }
        
        return {
          originalFile: file,
          workbook: null,
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: new Date(file.lastModified)
          },
          sheets: [],
          status: response.data?.repairSummary ? 'success' : 'error',
          message: response.data?.repairSummary?.summary || 'Analysis completed',
          repairReport: response.data?.repairSummary
        };
      },
      staleTime: 5 * 60 * 1000,
    });
  };
};