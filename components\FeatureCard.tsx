import React from 'react';
import { motion } from 'framer-motion';
import Icon from './Icon';
import { FeatureItem } from '../types';

interface FeatureCardProps {
  item: FeatureItem;
  cardClassName?: string;
  iconBgColor?: string;
  iconTextColor?: string;
  index: number;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ 
  item, 
  cardClassName = "bg-white/20 backdrop-blur-xl p-4 sm:p-6 rounded-xl border border-white/30 shadow-2xl",
  iconBgColor = "bg-green-100",
  iconTextColor = "text-green-600",
  index
}) => {
  return (
    <motion.div 
      className={`${cardClassName} cursor-pointer group transition-all duration-300 hover:border-green-300 relative overflow-hidden`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ 
        y: -8, 
        boxShadow: "0px 20px 40px rgba(34, 197, 94, 0.15)",
        scale: 1.02
      }}
      whileTap={{ scale: 0.98 }}
    >
      <motion.div 
        className={`w-10 h-10 sm:w-12 sm:h-12 ${iconBgColor} backdrop-blur-xl rounded-full flex items-center justify-center ${iconTextColor} mb-3 sm:mb-4 group-hover:scale-110 transition-all duration-300 group-hover:bg-white/30`}
        whileHover={{ rotate: 5 }}
      >
        <Icon iconClass={item.iconClass} className="text-lg sm:text-xl" />
      </motion.div>
      <h3 className="font-display text-lg sm:text-xl font-semibold mb-2 tracking-tight group-hover:text-green-700 transition-colors duration-300">{item.title}</h3>
      <p className="font-body text-sm sm:text-base text-gray-700 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">{item.description}</p>
      
      {/* Subtle shine effect on hover */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700 ease-out" />
    </motion.div>
  );
};

export default FeatureCard;
