/**
 * Keyboard Navigation Utilities
 * Provides comprehensive keyboard navigation support for the application
 */

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  category: 'file' | 'navigation' | 'accessibility' | 'general';
}

export class KeyboardNavigationManager {
  private shortcuts: KeyboardShortcut[] = [];
  private isEnabled = true;
  private listeners: ((shortcuts: KeyboardShortcut[]) => void)[] = [];

  constructor() {
    this.setupGlobalKeyboardListener();
  }

  /**
   * Register a keyboard shortcut
   */
  registerShortcut(shortcut: KeyboardShortcut) {
    this.shortcuts.push(shortcut);
    this.notifyListeners();
  }

  /**
   * Remove a keyboard shortcut
   */
  removeShortcut(key: string, modifiers?: { ctrlKey?: boolean; altKey?: boolean; shiftKey?: boolean; metaKey?: boolean }) {
    this.shortcuts = this.shortcuts.filter(shortcut => {
      if (shortcut.key !== key) return true;

      if (modifiers) {
        return !(
          (modifiers.ctrlKey === undefined || shortcut.ctrlKey === modifiers.ctrlKey) &&
          (modifiers.altKey === undefined || shortcut.altKey === modifiers.altKey) &&
          (modifiers.shiftKey === undefined || shortcut.shiftKey === modifiers.shiftKey) &&
          (modifiers.metaKey === undefined || shortcut.metaKey === modifiers.metaKey)
        );
      }

      return false;
    });
    this.notifyListeners();
  }

  /**
   * Get all registered shortcuts
   */
  getShortcuts(): KeyboardShortcut[] {
    return [...this.shortcuts];
  }

  /**
   * Get shortcuts by category
   */
  getShortcutsByCategory(category: KeyboardShortcut['category']): KeyboardShortcut[] {
    return this.shortcuts.filter(shortcut => shortcut.category === category);
  }

  /**
   * Enable/disable keyboard navigation
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  /**
   * Subscribe to shortcut changes
   */
  subscribe(listener: (shortcuts: KeyboardShortcut[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify listeners of shortcut changes
   */
  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.shortcuts));
  }

  /**
   * Setup global keyboard event listener
   */
  private setupGlobalKeyboardListener() {
    document.addEventListener('keydown', (event) => {
      if (!this.isEnabled) return;

      // Don't trigger shortcuts when typing in inputs
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return;
      }

      // Find matching shortcut
      const matchingShortcut = this.shortcuts.find(shortcut => {
        return (
          shortcut.key.toLowerCase() === event.key.toLowerCase() &&
          !!shortcut.ctrlKey === event.ctrlKey &&
          !!shortcut.altKey === event.altKey &&
          !!shortcut.shiftKey === event.shiftKey &&
          !!shortcut.metaKey === event.metaKey
        );
      });

      if (matchingShortcut) {
        event.preventDefault();
        matchingShortcut.action();
      }
    });
  }

  /**
   * Format shortcut for display
   */
  static formatShortcut(shortcut: KeyboardShortcut): string {
    const parts: string[] = [];

    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.metaKey) parts.push('Cmd');

    parts.push(shortcut.key.toUpperCase());

    return parts.join(' + ');
  }
}

// Global instance
export const keyboardNavigation = new KeyboardNavigationManager();

// Focus management utilities
export class FocusManager {
  private static focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[role="button"]:not([disabled])',
    '[role="link"]:not([disabled])'
  ].join(', ');

  /**
   * Get all focusable elements within a container
   */
  static getFocusableElements(container: HTMLElement = document.body): HTMLElement[] {
    return Array.from(container.querySelectorAll(this.focusableSelectors)) as HTMLElement[];
  }

  /**
   * Focus the first focusable element in a container
   */
  static focusFirst(container: HTMLElement = document.body): boolean {
    const focusable = this.getFocusableElements(container);
    if (focusable.length > 0) {
      focusable[0].focus();
      return true;
    }
    return false;
  }

  /**
   * Focus the last focusable element in a container
   */
  static focusLast(container: HTMLElement = document.body): boolean {
    const focusable = this.getFocusableElements(container);
    if (focusable.length > 0) {
      focusable[focusable.length - 1].focus();
      return true;
    }
    return false;
  }

  /**
   * Focus the next focusable element
   */
  static focusNext(currentElement?: HTMLElement): boolean {
    const current = currentElement || document.activeElement as HTMLElement;
    const focusable = this.getFocusableElements();
    const currentIndex = focusable.indexOf(current);

    if (currentIndex >= 0 && currentIndex < focusable.length - 1) {
      focusable[currentIndex + 1].focus();
      return true;
    } else if (focusable.length > 0) {
      // Wrap to first element
      focusable[0].focus();
      return true;
    }
    return false;
  }

  /**
   * Focus the previous focusable element
   */
  static focusPrevious(currentElement?: HTMLElement): boolean {
    const current = currentElement || document.activeElement as HTMLElement;
    const focusable = this.getFocusableElements();
    const currentIndex = focusable.indexOf(current);

    if (currentIndex > 0) {
      focusable[currentIndex - 1].focus();
      return true;
    } else if (focusable.length > 0) {
      // Wrap to last element
      focusable[focusable.length - 1].focus();
      return true;
    }
    return false;
  }

  /**
   * Trap focus within a container
   */
  static trapFocus(container: HTMLElement): () => void {
    const focusable = this.getFocusableElements(container);
    if (focusable.length === 0) return () => {};

    const firstFocusable = focusable[0];
    const lastFocusable = focusable[focusable.length - 1];

    // Focus first element initially
    firstFocusable.focus();

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        // Shift + Tab (backward)
        if (document.activeElement === firstFocusable) {
          event.preventDefault();
          lastFocusable.focus();
        }
      } else {
        // Tab (forward)
        if (document.activeElement === lastFocusable) {
          event.preventDefault();
          firstFocusable.focus();
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }
}

// Skip link utilities
export class SkipLinkManager {
  private static skipLinks: { id: string; label: string; target: string }[] = [];

  /**
   * Register a skip link
   */
  static registerSkipLink(id: string, label: string, target: string) {
    this.skipLinks.push({ id, label, target });
    this.updateSkipLinksContainer();
  }

  /**
   * Remove a skip link
   */
  static removeSkipLink(id: string) {
    this.skipLinks = this.skipLinks.filter(link => link.id !== id);
    this.updateSkipLinksContainer();
  }

  /**
   * Create or update the skip links container
   */
  private static updateSkipLinksContainer() {
    let container = document.getElementById('skip-links');

    if (!container) {
      container = document.createElement('div');
      container.id = 'skip-links';
      container.className = 'sr-only focus-within:not-sr-only fixed top-2 left-2 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-2 space-y-1';
      document.body.insertBefore(container, document.body.firstChild);
    }

    container.innerHTML = this.skipLinks
      .map(link => `
        <a 
          href="#${link.target}" 
          class="block px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
          onclick="document.getElementById('${link.target}')?.focus()"
        >
          ${link.label}
        </a>
      `)
      .join('');
  }
}

// Announcement utilities for screen readers
export class AnnouncementManager {
  private static container: HTMLElement | null = null;

  /**
   * Initialize the announcement container
   */
  private static initContainer() {
    if (this.container) return;

    this.container = document.createElement('div');
    this.container.setAttribute('aria-live', 'polite');
    this.container.setAttribute('aria-atomic', 'true');
    this.container.className = 'sr-only';
    this.container.id = 'announcements';
    document.body.appendChild(this.container);
  }

  /**
   * Announce a message to screen readers
   */
  static announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    this.initContainer();

    if (this.container) {
      this.container.setAttribute('aria-live', priority);
      this.container.textContent = message;

      // Clear after a delay to allow for re-announcements
      setTimeout(() => {
        if (this.container) {
          this.container.textContent = '';
        }
      }, 1000);
    }
  }
}

// React hook for keyboard navigation
export function useKeyboardNavigation() {
  const [shortcuts, setShortcuts] = React.useState<KeyboardShortcut[]>([]);

  React.useEffect(() => {
    const unsubscribe = keyboardNavigation.subscribe(setShortcuts);
    setShortcuts(keyboardNavigation.getShortcuts());
    return unsubscribe;
  }, []);

  return {
    shortcuts,
    registerShortcut: (shortcut: KeyboardShortcut) => keyboardNavigation.registerShortcut(shortcut),
    removeShortcut: (key: string, modifiers?: any) => keyboardNavigation.removeShortcut(key, modifiers),
    setEnabled: (enabled: boolean) => keyboardNavigation.setEnabled(enabled),
    announce: AnnouncementManager.announce,
    focusFirst: FocusManager.focusFirst,
    focusLast: FocusManager.focusLast,
    focusNext: FocusManager.focusNext,
    focusPrevious: FocusManager.focusPrevious,
    trapFocus: FocusManager.trapFocus,
  };
}

// Import React for the hook
import React from 'react';