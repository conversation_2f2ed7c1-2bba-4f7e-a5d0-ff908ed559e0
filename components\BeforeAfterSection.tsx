import React from 'react';
import Icon from './Icon';

const BeforeAfterSection: React.FC = () => {
  return (
    <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-24">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-6 sm:mb-8 text-center px-2">See SheetHealer in Action</h2>
        <p className="text-lg sm:text-xl text-gray-700 mb-8 sm:mb-12 text-center max-w-3xl mx-auto px-2">
          Watch how we transform corrupted, unusable files into perfect spreadsheets
        </p>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
          {/* Before Card */}
          <div className="bg-white/20 backdrop-blur-xl rounded-xl shadow-2xl overflow-hidden border border-white/30">
            <div className="p-3 sm:p-4 bg-red-100/50 backdrop-blur-xl border-b border-white/30">
              <div className="flex items-start">
                <Icon iconClass="far fa-file-lines text-2xl sm:text-3xl text-red-500 mr-2 sm:mr-3 mt-1" ariaHidden={false} />
                <div>
                  <h3 className="text-lg sm:text-xl font-semibold text-red-700">Before: Corrupted File</h3>
                  <p className="text-xs sm:text-sm text-gray-700">File won&apos;t open, data appears scrambled</p>
                </div>
              </div>
            </div>
            <div className="p-4 sm:p-6 bg-red-50/30 backdrop-blur-xl min-h-[240px] sm:min-h-[280px]">
              <div className="space-y-2 sm:space-y-3 text-xs sm:text-sm text-red-700 leading-normal">
                <div className="bg-red-100/50 backdrop-blur-xl p-2 sm:p-3 rounded border border-white/30">
                  <p className="font-semibold text-red-800 text-xs sm:text-sm">⚠️ ERROR: File format corrupted</p>
                  <p className="text-xs text-red-600 mt-1">Excel cannot open the file &apos;Q1_Report.xlsx&apos;</p>
                </div>
                <div className="font-mono text-xs bg-gray-100/50 backdrop-blur-xl p-2 rounded border border-white/30">
                  <div className="text-red-500">####### ###### ######</div>
                  <div className="text-red-500">####### ###### ######</div>
                  <div className="text-red-500">####### ###### ######</div>
                </div>
                <div className="space-y-1">
                  <p className="flex items-center text-xs sm:text-sm"><span className="text-red-500 mr-2">✗</span> Unable to read formulas</p>
                  <p className="flex items-center text-xs sm:text-sm"><span className="text-red-500 mr-2">✗</span> Missing headers and data</p>
                  <p className="flex items-center text-xs sm:text-sm"><span className="text-red-500 mr-2">✗</span> File structure damaged</p>
                  <p className="flex items-center text-xs sm:text-sm"><span className="text-red-500 mr-2">✗</span> Cannot recover manually</p>
                </div>
              </div>
            </div>
          </div>

          {/* After Card */}
          <div className="bg-white/20 backdrop-blur-xl rounded-xl shadow-2xl overflow-hidden border border-white/30">
            <div className="p-3 sm:p-4 bg-green-100/50 backdrop-blur-xl border-b border-white/30">
              <div className="flex items-start">
                <Icon iconClass="fas fa-check-circle text-2xl sm:text-3xl text-green-500 mr-2 sm:mr-3 mt-1" ariaHidden={false}/>
                <div>
                  <h3 className="text-lg sm:text-xl font-semibold text-green-700">After: Fully Repaired</h3>
                  <p className="text-xs sm:text-sm text-gray-700">Perfect file, all data recovered</p>
                </div>
              </div>
            </div>
            <div className="p-4 sm:p-6 bg-green-50/30 backdrop-blur-xl min-h-[240px] sm:min-h-[280px]">
              <div className="bg-green-100/50 backdrop-blur-xl p-2 sm:p-3 rounded border border-white/30 mb-3 sm:mb-4">
                <div className="font-semibold text-green-800 text-center text-xs sm:text-sm">✅ Q1_Report.xlsx - Fully Restored</div>
                <p className="text-xs text-green-600 text-center mt-1">Repair completed in 3.2 seconds</p>
              </div>
              <div className="bg-white/50 backdrop-blur-xl p-2 sm:p-3 rounded border border-white/30 mb-3 sm:mb-4">
                <div className="space-y-1 sm:space-y-1.5 text-xs sm:text-sm text-gray-700">
                  <div className="grid grid-cols-3 gap-x-1 sm:gap-x-2 pb-1 sm:pb-1.5 mb-1 sm:mb-1.5 border-b border-white/30 bg-gray-50/50 backdrop-blur-xl p-1 sm:p-2 rounded">
                    <span className="font-medium text-gray-700 text-xs sm:text-sm">Product</span>
                    <span className="font-medium text-gray-700 text-right text-xs sm:text-sm">Revenue</span>
                    <span className="font-medium text-gray-700 text-right text-xs sm:text-sm">Growth</span>
                  </div>
                  <div className="grid grid-cols-3 gap-x-1 sm:gap-x-2 p-1">
                    <span className="text-xs sm:text-sm">Widget A</span>
                    <span className="text-right font-mono text-xs sm:text-sm">$125,000</span>
                    <span className="text-right text-green-600 font-medium text-xs sm:text-sm">+15%</span>
                  </div>
                  <div className="grid grid-cols-3 gap-x-1 sm:gap-x-2 p-1">
                    <span className="text-xs sm:text-sm">Widget B</span>
                    <span className="text-right font-mono text-xs sm:text-sm">$89,500</span>
                    <span className="text-right text-green-600 font-medium text-xs sm:text-sm">+8%</span>
                  </div>
                  <div className="grid grid-cols-3 gap-x-1 sm:gap-x-2 p-1 border-t border-gray-200 pt-2">
                    <span className="font-medium text-xs sm:text-sm">Total</span>
                    <span className="text-right font-mono font-medium text-xs sm:text-sm">$214,500</span>
                    <span className="text-right text-green-600 font-medium text-xs sm:text-sm">+12%</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 sm:gap-2 text-xs">
                <div className="flex items-center text-green-700">
                  <Icon iconClass="fas fa-check text-green-500 mr-2 text-xs" ariaHidden={false}/> All formulas working
                </div>
                <div className="flex items-center text-green-700">
                  <Icon iconClass="fas fa-check text-green-500 mr-2 text-xs" ariaHidden={false}/> Data fully recovered
                </div>
                <div className="flex items-center text-green-700">
                  <Icon iconClass="fas fa-check text-green-500 mr-2 text-xs" ariaHidden={false}/> Formatting preserved
                </div>
                <div className="flex items-center text-green-700">
                  <Icon iconClass="fas fa-check text-green-500 mr-2 text-xs" ariaHidden={false}/> Charts functional
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BeforeAfterSection;
