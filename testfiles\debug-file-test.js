import fs from 'fs';
import * as XLS<PERSON> from 'xlsx';

// Test the same validation logic as our web app
function validateFile(fileName, fileSize) {
  console.log(`🔍 Validating file: ${fileName} (${fileSize} bytes)`);
  
  // Check file size (max 25MB for better memory management)
  const maxSize = 25 * 1024 * 1024;
  if (fileSize > maxSize) {
    return { isValid: false, message: 'File size exceeds 25MB limit. Please use a smaller file or contact support for large file processing.' };
  }

  // Check for minimum file size (empty files)
  if (fileSize < 100) {
    return { isValid: false, message: 'File appears to be empty or too small to be a valid Excel file.' };
  }

  // Check file extension
  const SUPPORTED_FORMATS = ['.xlsx', '.xls', '.xlsb', '.xlsm'];
  const fileExtension = '.' + fileName.split('.').pop()?.toLowerCase();
  if (!SUPPORTED_FORMATS.includes(fileExtension)) {
    return { 
      isValid: false, 
      message: `Unsupported file format. Supported formats: ${SUPPORTED_FORMATS.join(', ')}` 
    };
  }

  return { isValid: true, message: 'File is valid' };
}

// Test the same processing logic as our web app
async function processFile(fileName) {
  console.log(`\n🚀 Testing file: ${fileName}`);
  
  try {
    // Read file
    const data = fs.readFileSync(fileName);
    console.log(`📖 File read successfully: ${data.length} bytes`);
    
    // Validate
    const validation = validateFile(fileName, data.length);
    console.log(`✅ Validation result:`, validation);
    
    if (!validation.isValid) {
      console.log(`❌ Validation failed: ${validation.message}`);
      return;
    }
    
    // Parse with same multi-layered approach as web app
    let workbook;
    try {
      console.log('🔧 Attempting primary SheetJS parsing...');
      workbook = XLSX.read(data, { 
        cellFormula: true,
        cellDates: true,
        type: 'buffer'
      });
      console.log('✅ Primary parsing successful, sheets:', workbook.SheetNames);
    } catch (primaryError) {
      console.log('⚠️ Primary parsing failed:', primaryError.message);
      try {
        console.log('🔧 Attempting secondary parsing...');
        workbook = XLSX.read(data, { 
          cellFormula: false,
          cellDates: false,
          type: 'buffer',
          WTF: false,
          dense: false
        });
        console.log('✅ Secondary parsing successful, sheets:', workbook.SheetNames);
      } catch (secondaryError) {
        console.log('⚠️ Secondary parsing failed:', secondaryError.message);
        try {
          console.log('🔧 Attempting tertiary parsing (binary string)...');
          let binaryString = '';
          for (let i = 0; i < data.length; i++) {
            binaryString += String.fromCharCode(data[i]);
          }
          workbook = XLSX.read(binaryString, { 
            type: 'binary',
            cellFormula: false,
            cellDates: false,
            WTF: false
          });
          console.log('✅ Tertiary parsing successful, sheets:', workbook.SheetNames);
        } catch (tertiaryError) {
          console.log('❌ All parsing methods failed:');
          console.log('  Primary:', primaryError.message);
          console.log('  Secondary:', secondaryError.message);
          console.log('  Tertiary:', tertiaryError.message);
          return;
        }
      }
    }
    
    // Analyze sheets
    console.log('📊 Analyzing sheets...');
    const sheets = workbook.SheetNames.map(sheetName => {
      try {
        const worksheet = workbook.Sheets[sheetName];
        
        if (!worksheet) {
          return {
            name: sheetName,
            cellCount: 0,
            hasFormulas: false,
            hasErrors: false
          };
        }

        let cellCount = 0;
        try {
          const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
          cellCount = Math.max(0, (range.e.r - range.s.r + 1) * (range.e.c - range.s.c + 1));
        } catch (rangeError) {
          console.warn(`Range calculation failed for sheet ${sheetName}:`, rangeError.message);
          cellCount = 0;
        }
        
        let hasFormulas = false;
        let hasErrors = false;

        const cellKeys = Object.keys(worksheet);
        const maxCellsToCheck = 10000;
        
        for (let i = 0; i < Math.min(cellKeys.length, maxCellsToCheck); i++) {
          const cellAddress = cellKeys[i];
          if (cellAddress.startsWith('!')) continue;
          
          try {
            const cell = worksheet[cellAddress];
            if (cell && typeof cell === 'object') {
              if (cell.f) hasFormulas = true;
              if (cell.t === 'e') hasErrors = true;
            }
          } catch (_cellError) {
            continue;
          }
          
          if (hasFormulas && hasErrors) break;
        }

        return {
          name: sheetName,
          cellCount,
          hasFormulas,
          hasErrors
        };
      } catch (sheetError) {
        console.warn(`Analysis failed for sheet ${sheetName}:`, sheetError.message);
        return {
          name: sheetName,
          cellCount: 0,
          hasFormulas: false,
          hasErrors: false
        };
      }
    });
    
    console.log('📋 Sheet analysis results:');
    sheets.forEach(sheet => {
      console.log(`  - ${sheet.name}: ${sheet.cellCount} cells, formulas: ${sheet.hasFormulas}, errors: ${sheet.hasErrors}`);
    });
    
    const hasErrors = sheets.some(sheet => sheet.hasErrors);
    const isEmpty = sheets.every(sheet => sheet.cellCount <= 1);
    
    let status = 'success';
    let message = 'File processed successfully';
    
    if (hasErrors) {
      status = 'warning';
      message = 'File contains errors that can be repaired';
    } else if (isEmpty) {
      status = 'warning';
      message = 'File appears to be empty or minimal content detected';
    }
    
    console.log(`🎯 Final result: ${status} - ${message}`);
    
  } catch (error) {
    console.error('❌ Fatal error:', error.message);
  }
}

// Test all your files
async function testAllFiles() {
  const testFiles = ['test30.xlsx', 'test50.xlsx', 'test70.xlsx', 'test100.xlsx', 'test-corrupted-excel.xlsx'];
  
  for (const file of testFiles) {
    if (fs.existsSync(file)) {
      await processFile(file);
    } else {
      console.log(`⚠️ File not found: ${file}`);
    }
  }
}

testAllFiles(); 