{"name": "sheethealer---excel-file-recovery", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:integration": "npx ts-node tests/run-integration-tests.ts", "test:validation": "node lib/validation.test.js", "test:all": "npm run test:validation && npm run test:integration"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@types/react-dropzone": "^4.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "exceljs": "^4.4.0", "framer-motion": "^12.18.1", "js-cookie": "^3.0.5", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-cookie-consent": "^9.0.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-ga4": "^2.1.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "react-tweet": "^3.2.2", "resend": "^4.6.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-react": "^0.4.9", "@types/js-cookie": "^3.0.6", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vercel/node": "^2.3.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0", "vitest": "^2.1.8"}}