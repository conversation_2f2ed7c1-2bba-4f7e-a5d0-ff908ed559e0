
import React from 'react';
import { FileUpload, ProcessedFileResult, FileUploadHandles } from '../FileUpload';
import { FileProcessingResult } from '../FileProcessingResult';

interface RepairPageFileUploadProps {
  processedFile: ProcessedFileResult | null;
  onFileProcessed: (result: ProcessedFileResult) => void;
  onRepairFile: (file: File) => void;
  onAnalyzeAnother: () => void;
  isRepairing?: boolean;
}

export const RepairPageFileUpload = React.forwardRef<FileUploadHandles, RepairPageFileUploadProps>(({ 
  processedFile, 
  onFileProcessed, 
  onRepairFile, 
  onAnalyzeAnother,
  isRepairing = false
}, ref) => {
  return (
    <div className="relative w-full">
      {/* Always keep FileUpload mounted but hidden when showing results */}
      <div className={!processedFile ? 'block' : 'hidden'}>
        <FileUpload onFileProcessed={onFileProcessed} ref={ref} className="w-full" />
      </div>
      
      {processedFile && (
        <div className="bg-white rounded-2xl border-2 border-gray-200 p-6 w-full">
          <FileProcessingResult 
            result={processedFile}
            onRepairFile={onRepairFile}
            onAnalyzeAnother={onAnalyzeAnother}
            isRepairing={isRepairing}
          />
        </div>
      )}
      
      {/* Decorative elements */}
      <div className="absolute -z-10 top-4 right-4 lg:top-8 lg:right-8 w-16 h-16 lg:w-24 lg:h-24 bg-lime-200 rounded-full opacity-50 hidden md:block"></div>
      <div className="absolute -z-10 bottom-4 left-4 lg:bottom-8 lg:left-8 w-12 h-12 lg:w-16 lg:h-16 bg-yellow-200 rounded-full opacity-50 hidden md:block"></div>
    </div>
  );
});

// This is a test comment to force re-evaluation

