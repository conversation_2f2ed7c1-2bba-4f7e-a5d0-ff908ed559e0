import fs from 'fs';
import * as XLSX from 'xlsx';

// Import our repair service logic (using ES modules)
const modulePath = '../lib/excelRepair.ts';

// For now, let's implement the detection logic locally to test
function detectErrorCells(worksheet, sheetName) {
  const issues = [];
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');

  for (let R = range.s.r; R <= range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
      const cell = worksheet[cellAddress];

      if (cell && cell.t === 'e') {
        issues.push({
          sheet: sheetName,
          cell: cellAddress,
          issue: `Error value: ${cell.v || 'Unknown error'}`,
          action: 'We can remove this error cell',
          severity: 'medium'
        });
        console.log(`Found error cell: ${sheetName}!${cellAddress} = ${cell.v}`);
      }
    }
  }

  return issues;
}

function detectBrokenFormulas(worksheet, sheetName) {
  const issues = [];
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');

  for (let R = range.s.r; R <= range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
      const cell = worksheet[cellAddress];

      if (cell && cell.f) {
        const formula = cell.f;
        
        // Check for broken references
        if (formula.includes('#REF!')) {
          issues.push({
            sheet: sheetName,
            cell: cellAddress,
            issue: `Broken formula: ${formula}`,
            action: 'We can fix broken references',
            severity: 'medium'
          });
          console.log(`Found broken formula: ${sheetName}!${cellAddress} = ${formula}`);
        }
      }
    }
  }

  return issues;
}

async function testCorruptionDetection(fileName) {
  console.log(`\n🔍 Testing corruption detection for: ${fileName}`);
  
  try {
    const data = fs.readFileSync(fileName);
    const workbook = XLSX.read(data, { 
      type: 'buffer',
      cellDates: true,
      cellNF: true,
      cellStyles: true,
      cellFormula: true,
      sheetStubs: true,
      WTF: true
    });

    console.log(`📊 Workbook loaded with ${workbook.SheetNames.length} sheets: ${workbook.SheetNames.join(', ')}`);

    let totalIssues = 0;
    const allIssues = [];

    for (const sheetName of workbook.SheetNames) {
      console.log(`\n📋 Analyzing sheet: ${sheetName}`);
      const worksheet = workbook.Sheets[sheetName];
      
      // Detect error cells
      const errorIssues = detectErrorCells(worksheet, sheetName);
      allIssues.push(...errorIssues);
      totalIssues += errorIssues.length;
      console.log(`  - Error cells found: ${errorIssues.length}`);

      // Detect broken formulas
      const formulaIssues = detectBrokenFormulas(worksheet, sheetName);
      allIssues.push(...formulaIssues);
      totalIssues += formulaIssues.length;
      console.log(`  - Broken formulas found: ${formulaIssues.length}`);
    }

    console.log(`\n📈 Total issues found: ${totalIssues}`);
    
    if (totalIssues === 0) {
      console.log('✅ No problems detected - file is healthy!');
    } else {
      console.log('⚠️ File contains issues that can be repaired:');
      allIssues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue.sheet}!${issue.cell || 'General'}: ${issue.issue}`);
      });
    }

  } catch (error) {
    console.error('❌ Error testing file:', error.message);
  }
}

// Test the corrupted file
testCorruptionDetection('test-corrupted-excel.xlsx');

// Also test the file with real errors
if (fs.existsSync('test-with-real-errors.xlsx')) {
  testCorruptionDetection('test-with-real-errors.xlsx');
}
